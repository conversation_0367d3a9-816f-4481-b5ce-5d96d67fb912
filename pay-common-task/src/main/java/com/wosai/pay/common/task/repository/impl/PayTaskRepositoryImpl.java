package com.wosai.pay.common.task.repository.impl;

import com.wosai.pay.common.task.enums.PayTaskStatusEnum;
import com.wosai.pay.common.task.exception.TaskBizException;
import com.wosai.pay.common.task.exception.TaskDBException;
import com.wosai.pay.common.task.repository.PayTaskRepository;
import com.wosai.pay.common.task.repository.dto.PayTaskCreateDTO;
import com.wosai.pay.common.task.repository.entity.PayTaskConfigEntity;
import com.wosai.pay.common.task.repository.entity.PayTaskEntity;
import com.wosai.pay.common.task.repository.mapper.PayTaskConfigMapper;
import com.wosai.pay.common.task.repository.mapper.PayTaskMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.PreDestroy;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.*;


/**
 * <AUTHOR>
 * @description 任务实例表, 持久层实现
 * @date 2025-06-19
 */
public class PayTaskRepositoryImpl implements PayTaskRepository {
    private static final Logger log = LoggerFactory.getLogger(PayTaskRepositoryImpl.class);
    private final PayTaskMapper taskMapper;
    private final ConcurrentHashMap<Integer, PayTaskConfigEntity> taskConfigCache = new ConcurrentHashMap<>(100);
    private final PayTaskConfigMapper taskConfigMapper;
    private final ScheduledExecutorService scheduler;

    public PayTaskRepositoryImpl(PayTaskMapper taskMapper,
                                 PayTaskConfigMapper taskConfigMapper) {
        this.taskMapper = taskMapper;
        this.taskConfigMapper = taskConfigMapper;
        this.scheduler = Executors.newSingleThreadScheduledExecutor();

        // 初始化定时任务，每10分钟全量加载一次
        scheduler.scheduleAtFixedRate(this::loadAllTaskConfigs, 0, 10, TimeUnit.MINUTES);
    }

    /**
     * 全量加载任务配置到内存中
     */
    private void loadAllTaskConfigs() {
        try {
            List<PayTaskConfigEntity> configs = taskConfigMapper.getActiveTaskConfigs();
            if (configs != null) {
                configs.forEach(config -> taskConfigCache.put(config.getTaskType(), config));
            }
        } catch (Throwable e) {
            log.error("全量加载任务配置失败", e);
        }
    }

    private PayTaskConfigEntity getTaskConfigByType(Integer taskType) {
        PayTaskConfigEntity config = taskConfigCache.get(taskType);
        if (config == null) {
            log.error("任务配置不存在, taskType: {}", taskType);
            return null;
        }
        return config;
    }

    @Override
    public Long create(PayTaskCreateDTO taskDTO) {
        PayTaskEntity task = initTask(taskDTO);
        try {
            return taskMapper.insert(task);
        } catch (Exception e) {
            log.error("创建任务实例失败, 任务类型: {}, 分区ID: {}, 幂等ID: {}, 错误: {}",
                    task.getTaskType(), task.getPartitionId(), task.getIdempotentId(), e.getMessage(), e);
            throw new TaskDBException("创建任务实例失败");
        }
    }

    @Override
    public int batchCreate(List<PayTaskCreateDTO> taskDTOs) {
        return doBatchCreate(taskDTOs);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int batchCreateWithTransaction(List<PayTaskCreateDTO> taskDTOs) {
        return doBatchCreate(taskDTOs);
    }

    private int doBatchCreate(List<PayTaskCreateDTO> taskDTOs) {
        if (taskDTOs == null || taskDTOs.isEmpty()) {
            log.error("批量创建任务实例失败, 实例DTO列表不能为空");
            throw new TaskBizException("批量创建任务实例失败, 实例DTO列表不能为空");
        }

        List<PayTaskEntity> tasks = new ArrayList<>(taskDTOs.size());
        for (PayTaskCreateDTO dto : taskDTOs) {
            tasks.add(initTask(dto));
        }

        try {
            return taskMapper.batchInsert(tasks);
        } catch (Exception e) {
            log.error("批量创建任务实例失败, 实例数量: {}, 错误: {}", tasks.size(), e.getMessage(), e);
            throw new TaskDBException("批量创建任务实例失败");
        }
    }

    private PayTaskEntity initTask(PayTaskCreateDTO taskDTO) {
        if (taskDTO == null) {
            log.error("创建任务实例失败, 实例DTO不能为空");
            throw new TaskBizException("任务实例DTO不能为空");
        }

        PayTaskEntity task = taskDTO.toEntity();
        task.setStatus(PayTaskStatusEnum.PENDING.getStatus());

        // 根据taskType获取配置并生成随机partitionId
        PayTaskConfigEntity config = getTaskConfigByType(task.getTaskType());
        if (config != null && config.getPartitionCnt() != null && config.getPartitionCnt() > 1) {
            task.setPartitionId(generateRandomPartitionId(config.getPartitionCnt()));
        } else {
            task.setPartitionId(0);
        }
        return task;
    }

    @Override
    public int updateSelectiveById(PayTaskEntity task) {
        if (task == null || task.getId() == null) {
            log.error("选择性更新任务实例失败, 实例或ID不能为空");
            throw new TaskBizException("实例或ID不能为空");
        }

        try {
            return taskMapper.updateSelectiveById(task);
        } catch (Exception e) {
            log.error("选择性更新任务实例失败, ID: {}, 错误: {}", task.getId(), e.getMessage(), e);
            throw new TaskDBException("选择性更新任务实例失败");
        }
    }

    @Override
    public int batchUpdateStatusByIds(List<PayTaskEntity> tasks) {
        if (tasks == null || tasks.isEmpty()) {
            log.error("批量更新任务实例状态失败, 实例列表不能为空");
            throw new TaskBizException("实例列表不能为空");
        }

        try {
            return taskMapper.batchUpdateStatusByIds(tasks);
        } catch (Exception e) {
            log.error("批量更新任务实例状态失败, 实例数量: {}, 错误: {}", tasks.size(), e.getMessage(), e);
            throw new TaskDBException("批量更新任务实例状态失败");
        }
    }

    @Override
    public List<PayTaskEntity> queryTasksByCondition(Integer taskType, Integer partitionId, List<PayTaskStatusEnum> statusList,
                                                     LocalDateTime planTime, Long lastId, int batchSize) {
        if (batchSize <= 0) {
            log.error("查询任务实例失败, batchSize必须大于0");
            throw new TaskBizException("batchSize必须大于0");
        }
        return taskMapper.queryTasksByCondition(taskType, partitionId, statusList, planTime, lastId, batchSize);
    }

    /**
     * 生成随机的partitionId
     *
     * @param partitionCnt
     * @return
     */
    private Integer generateRandomPartitionId(int partitionCnt) {
        return ThreadLocalRandom.current().nextInt(partitionCnt);
    }

    @Override
    public PayTaskEntity queryByIdempotentId(String idempotentId) {
        if (idempotentId == null || idempotentId.isEmpty()) {
            log.error("查询任务实例失败, 幂等ID不能为空");
            throw new TaskBizException("幂等ID不能为空");
        }

        try {
            return taskMapper.queryByIdempotentId(idempotentId);
        } catch (Exception e) {
            log.error("根据幂等ID查询任务实例失败, idempotentId: {}, 错误: {}", idempotentId, e.getMessage(), e);
            throw new TaskDBException("根据幂等ID查询任务实例失败");
        }
    }

    @Override
    public List<String> queryExistingIdempotentIds(List<String> idempotentIds) {
        if (idempotentIds == null || idempotentIds.isEmpty()) {
            log.error("查询存在的幂等ID失败, 幂等ID列表不能为空");
            throw new TaskBizException("幂等ID列表不能为空");
        }

        try {
            return taskMapper.queryExistingIdempotentIds(idempotentIds);
        } catch (Exception e) {
            log.error("查询存在的幂等ID失败, idempotentIds: {}, 错误: {}", idempotentIds, e.getMessage(), e);
            throw new TaskDBException("查询存在的幂等ID失败");
        }
    }

    @PreDestroy
    public void shutdown() {
        if (scheduler != null) {
            scheduler.shutdown();
            try {
                if (!scheduler.awaitTermination(5, TimeUnit.SECONDS)) {
                    scheduler.shutdownNow();
                }
            } catch (InterruptedException e) {
                scheduler.shutdownNow();
                Thread.currentThread().interrupt();
            }
        }
    }
}
