package com.wosai.pay.common.task.autoconfigure;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @description 通用任务全局配置
 * @date 2025-06-18
 */
@Configuration
public class PayTaskProperties {
    /**
     * 是否启用通用任务，默认为true
     */
    @Value("${pay.common.config.task.enable:true}")
    private Boolean enable;

    /**
     * 全局锁的前缀（由于同一个redis实例可能会被多个项目共用，所以需要加个前缀防止冲突）
     */
    @Value("${pay.common.config.task.global-lock-prefix}")
    private String globalLockPrefix;

    /**
     * 链路追踪ID的字段名，默认是trace_id
     */
    @Value("${pay.common.config.task.trace-id-key:trace_id}")
    private String traceIdKey;

    /**
     * 任务配置表的表名
     */
    @Value("${pay.common.config.task.task-config.table-name:pay_task_config}")
    private String taskConfigTableName;

    /**
     * 任务实例表的表名
     */
    @Value("${pay.common.config.task.table-name:pay_task}")
    private String taskTableName;

    /**
     * 业务方自定义字段列表（逗号分隔）
     */
    @Value("#{'${pay.common.config.task.biz-columns:}'.split(',').?[!#this.isEmpty()]}")
    private List<String> bizColumns = new ArrayList<>();

    /**
     * 是否启用Mapper日志打印，默认为true
     */
    @Value("${pay.common.config.task.mapper.log.enable:true}")
    private Boolean mapperLogEnable;

    /**
     * 普通任务扫描周期，默认30s
     */
    @Value("${pay.common.config.task.normal.scan-interval:30}")
    private Integer normalScanInterval;

    /**
     * 线程池队列长度，默认100
     */
    @Value("${pay.common.config.task.thread-pool.queue-capacity:100}")
    private Integer queueCapacity;

    /**
     * 处理任务配置列表的线程池核心线程数，默认20
     */
    @Value("${pay.common.config.task.task-config.thread-pool.core-size:20}")
    private Integer taskConfigThreadPoolCoreSize;

    /**
     * 处理任务配置列表的线程池最大线程数，默认50
     */
    @Value("${pay.common.config.task.task-config.thread-pool.max-size:50}")
    private Integer taskConfigThreadPoolMaxSize;

    /**
     * 业务的默认全局共享线程池核心线程数，默认10
     */
    @Value("${pay.common.config.task.biz-global-share.thread-pool.core-size:10}")
    private Integer bizGlobalShareThreadPoolCoreSize;

    /**
     * 业务的默认全局共享线程池最大线程数，默认50
     */
    @Value("${pay.common.config.task.biz-global-share.thread-pool.max-size:50}")
    private Integer bizGlobalShareThreadPoolMaxSize;

    /**
     * 重试任务扫描周期，默认5分钟
     */
    @Value("${pay.common.config.task.retry.scan-interval:300}")
    private Integer retryScanInterval;

    /**
     * 每批处理的任务实例数量，默认1000
     */
    @Value("${pay.common.config.task.batch-size:1000}")
    private Integer batchSize;

    /**
     * 最大迭代次数，默认1000
     */
    @Value("${pay.common.config.task.max-iterations:1000}")
    private Integer maxIterations;

    /**
     * 并发处理每批任务实例时(由batchSize控制)，等待全部处理完成的超时时间，默认15分钟
     */
    @Value("${pay.common.config.task.countDownLatch.timeout:900}")
    private Integer countDownLatchTimeout;

    /**
     * 是否启用线程池监控，默认为true
     */
    @Value("${pay.common.config.task.monitor.enable:true}")
    private Boolean monitorEnable;

    /**
     * 线程池监控间隔(秒)，默认120秒
     */
    @Value("${pay.common.config.task.monitor.interval:120}")
    private Integer monitorInterval;

    /**
     * 线程池队列告警阈值，默认50
     */
    @Value("${pay.common.config.task.monitor.queue-threshold:50}")
    private Integer queueWarningThreshold;

    /**
     * 线程池活跃线程告警阈值，默认20
     */
    @Value("${pay.common.config.task.monitor.active-thread-threshold:20}")
    private Integer activeThreadWarningThreshold;

    /**
     * 告警地址，用于告警通知
     */
    @Value("${pay.common.config.task.alarm-notify.url:}")
    private String alarmNotifyUrl;

    /**
     * 达到最大重试次数的告警频率(秒)，默认半小时
     */
    @Value("${pay.common.config.task.alarm-frequency.reach-max-retry-cnt:1800}")
    private Integer alarmFrequencyOfReachMaxRetryCnt;

    public boolean checkIsEnable() {
        return enable != null && enable;
    }

    public boolean checkIsMonitorEnable() {
        return monitorEnable != null && monitorEnable;
    }

    public boolean checkIsMapperLogEnable() {
        return mapperLogEnable != null && mapperLogEnable;
    }

    public List<String> getBizColumns() {
        return Collections.unmodifiableList(bizColumns);
    }

    public Boolean getEnable() {
        return this.enable;
    }

    public String getGlobalLockPrefix() {
        return this.globalLockPrefix;
    }

    public String getTaskConfigTableName() {
        return this.taskConfigTableName;
    }

    public String getTaskTableName() {
        return this.taskTableName;
    }

    public Boolean getMapperLogEnable() {
        return this.mapperLogEnable;
    }

    public Integer getNormalScanInterval() {
        return this.normalScanInterval;
    }

    public Integer getQueueCapacity() {
        return this.queueCapacity;
    }

    public Integer getTaskConfigThreadPoolCoreSize() {
        return this.taskConfigThreadPoolCoreSize;
    }

    public Integer getTaskConfigThreadPoolMaxSize() {
        return this.taskConfigThreadPoolMaxSize;
    }

    public Integer getBizGlobalShareThreadPoolCoreSize() {
        return this.bizGlobalShareThreadPoolCoreSize;
    }

    public Integer getBizGlobalShareThreadPoolMaxSize() {
        return this.bizGlobalShareThreadPoolMaxSize;
    }

    public Integer getRetryScanInterval() {
        return this.retryScanInterval;
    }

    public Integer getBatchSize() {
        return this.batchSize;
    }

    public Integer getMaxIterations() {
        return this.maxIterations;
    }

    public Integer getCountDownLatchTimeout() {
        return this.countDownLatchTimeout;
    }

    public Boolean getMonitorEnable() {
        return this.monitorEnable;
    }

    public Integer getMonitorInterval() {
        return this.monitorInterval;
    }

    public Integer getQueueWarningThreshold() {
        return this.queueWarningThreshold;
    }

    public Integer getActiveThreadWarningThreshold() {
        return this.activeThreadWarningThreshold;
    }

    public String getAlarmNotifyUrl() {
        return this.alarmNotifyUrl;
    }

    public Integer getAlarmFrequencyOfReachMaxRetryCnt() {
        return this.alarmFrequencyOfReachMaxRetryCnt;
    }

    public void setEnable(Boolean enable) {
        this.enable = enable;
    }

    public void setGlobalLockPrefix(String globalLockPrefix) {
        this.globalLockPrefix = globalLockPrefix;
    }

    public void setTaskConfigTableName(String taskConfigTableName) {
        this.taskConfigTableName = taskConfigTableName;
    }

    public void setTaskTableName(String taskTableName) {
        this.taskTableName = taskTableName;
    }

    public void setBizColumns(List<String> bizColumns) {
        this.bizColumns = bizColumns;
    }

    public void setMapperLogEnable(Boolean mapperLogEnable) {
        this.mapperLogEnable = mapperLogEnable;
    }

    public void setNormalScanInterval(Integer normalScanInterval) {
        this.normalScanInterval = normalScanInterval;
    }

    public void setQueueCapacity(Integer queueCapacity) {
        this.queueCapacity = queueCapacity;
    }

    public void setTaskConfigThreadPoolCoreSize(Integer taskConfigThreadPoolCoreSize) {
        this.taskConfigThreadPoolCoreSize = taskConfigThreadPoolCoreSize;
    }

    public void setTaskConfigThreadPoolMaxSize(Integer taskConfigThreadPoolMaxSize) {
        this.taskConfigThreadPoolMaxSize = taskConfigThreadPoolMaxSize;
    }

    public void setBizGlobalShareThreadPoolCoreSize(Integer bizGlobalShareThreadPoolCoreSize) {
        this.bizGlobalShareThreadPoolCoreSize = bizGlobalShareThreadPoolCoreSize;
    }

    public void setBizGlobalShareThreadPoolMaxSize(Integer bizGlobalShareThreadPoolMaxSize) {
        this.bizGlobalShareThreadPoolMaxSize = bizGlobalShareThreadPoolMaxSize;
    }

    public void setRetryScanInterval(Integer retryScanInterval) {
        this.retryScanInterval = retryScanInterval;
    }

    public void setBatchSize(Integer batchSize) {
        this.batchSize = batchSize;
    }

    public void setMaxIterations(Integer maxIterations) {
        this.maxIterations = maxIterations;
    }

    public void setCountDownLatchTimeout(Integer countDownLatchTimeout) {
        this.countDownLatchTimeout = countDownLatchTimeout;
    }

    public void setMonitorEnable(Boolean monitorEnable) {
        this.monitorEnable = monitorEnable;
    }

    public void setMonitorInterval(Integer monitorInterval) {
        this.monitorInterval = monitorInterval;
    }

    public void setQueueWarningThreshold(Integer queueWarningThreshold) {
        this.queueWarningThreshold = queueWarningThreshold;
    }

    public void setActiveThreadWarningThreshold(Integer activeThreadWarningThreshold) {
        this.activeThreadWarningThreshold = activeThreadWarningThreshold;
    }

    public void setAlarmNotifyUrl(String alarmNotifyUrl) {
        this.alarmNotifyUrl = alarmNotifyUrl;
    }

    public void setAlarmFrequencyOfReachMaxRetryCnt(Integer alarmFrequencyOfReachMaxRetryCnt) {
        this.alarmFrequencyOfReachMaxRetryCnt = alarmFrequencyOfReachMaxRetryCnt;
    }

    public String getTraceIdKey() {
        return traceIdKey;
    }

    public void setTraceIdKey(String traceIdKey) {
        this.traceIdKey = traceIdKey;
    }
}
