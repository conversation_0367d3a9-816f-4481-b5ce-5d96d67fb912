package com.wosai.pay.common.task.exception;

import com.wosai.pay.common.task.enums.PayTaskErrorTypeEnum;

/**
 * <AUTHOR>
 * @description 远程服务异常
 * @date 2025-06-19
 */
public class TaskRemoteException extends TaskBaseException {
    private static final long serialVersionUID = -210829049114374268L;

    public TaskRemoteException(String message) {
        super(PayTaskErrorTypeEnum.RETRY_REMOTE, message);
    }

    public TaskRemoteException(String message, Throwable cause) {
        super(PayTaskErrorTypeEnum.RETRY_REMOTE, message, cause);
    }
}