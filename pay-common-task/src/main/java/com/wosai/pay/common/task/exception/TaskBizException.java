package com.wosai.pay.common.task.exception;

import com.wosai.pay.common.task.enums.PayTaskErrorTypeEnum;

/**
 * <AUTHOR>
 * @description 业务异常，需要重试
 * @date 2025-06-19
 */
public class TaskBizException extends TaskBaseException {
    private static final long serialVersionUID = 6597239934491131712L;

    public TaskBizException(String message) {
        super(PayTaskErrorTypeEnum.RETRY_BIZ, message);
    }

    public TaskBizException(String message, Throwable cause) {
        super(PayTaskErrorTypeEnum.RETRY_BIZ, message, cause);
    }
}