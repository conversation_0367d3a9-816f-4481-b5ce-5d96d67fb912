package com.wosai.pay.common.task.repository.impl;

import com.wosai.pay.common.task.enums.PayTaskConfigStatusEnum;
import com.wosai.pay.common.task.exception.TaskBizException;
import com.wosai.pay.common.task.exception.TaskDBException;
import com.wosai.pay.common.task.repository.PayTaskConfigRepository;
import com.wosai.pay.common.task.repository.entity.PayTaskConfigEntity;
import com.wosai.pay.common.task.repository.mapper.PayTaskConfigMapper;
import com.wosai.pay.common.task.util.TaskJsonUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;

/**
 * <AUTHOR>
 * @description 任务配置表，持久层
 * @date 2025-06-19
 */
public class PayTaskConfigRepositoryImpl implements PayTaskConfigRepository {
    private static final Logger log = LoggerFactory.getLogger(PayTaskConfigRepositoryImpl.class);
    private final PayTaskConfigMapper taskConfigMapper;

    public PayTaskConfigRepositoryImpl(PayTaskConfigMapper taskConfigMapper) {
        this.taskConfigMapper = taskConfigMapper;
    }

    @Override
    public long insert(PayTaskConfigEntity config) {
        // 参数校验
        validateTaskConfigParams(config);

        PayTaskConfigEntity existing = taskConfigMapper.getByTaskType(config.getTaskType());
        if (existing != null) {
            log.error("已存在相同的任务, taskType={}, config={}", config.getTaskType(), TaskJsonUtil.encode(config));
            throw new TaskBizException("已存在相同的任务, 无法创建");
        }

        try {
            return taskConfigMapper.insert(config);
        } catch (Exception e) {
            log.error("新增任务配置失败, 任务类型: {}, 错误: {}", config.getTaskType(), e.getMessage(), e);
            throw new TaskDBException("新增任务配置失败");
        }
    }

    @Override
    public void updateSelectiveById(PayTaskConfigEntity config) {
        if (config.getId() == null) {
            throw new TaskBizException("id不能为null");
        }

        long taskId = config.getId();

        //参数校验
        validateTaskConfigParams(config);

        // 检查任务配置是否已存在
        PayTaskConfigEntity existing = taskConfigMapper.getById(taskId);
        if (existing == null) {
            log.error("任务不存在, 无法更新, ID: {}, config: {}", taskId, TaskJsonUtil.encode(config));
            throw new TaskBizException("任务不存在, 无法更新");
        }

        if (config.getPartitionCnt() != null && config.getPartitionCnt() < existing.getPartitionCnt()) {
            log.error("入参错误, partitionCnt不能比原来的小, ID: {}, oldPartitionCnt: {}, newPartitionCnt: {}",
                    taskId, existing.getPartitionCnt(), config.getPartitionCnt());
            throw new TaskBizException("partitionCnt不能比原来的小");
        }

        try {
            taskConfigMapper.updateSelectiveById(config);
            log.info("更新任务配置成功, ID: {}", config.getId());
        } catch (Exception e) {
            log.error("更新任务配置失败, ID: {}, config: {}, 错误: {}", taskId, TaskJsonUtil.encode(config), e.getMessage(), e);
            throw new TaskDBException("更新任务配置失败");
        }
    }

    @Override
    public List<PayTaskConfigEntity> getActiveTaskConfigs() {
        return taskConfigMapper.getActiveTaskConfigs();
    }

    @Override
    public PayTaskConfigEntity getByTaskType(Integer taskType) {
        if (null == taskType) {
            log.error("根据taskType查询任务配置, taskType为null");
            throw new TaskDBException("taskType不能为null");
        }
        return taskConfigMapper.getByTaskType(taskType);
    }

    /**
     * 校验任务配置的参数是否合法
     *
     * @param request
     */
    private void validateTaskConfigParams(PayTaskConfigEntity request) {
        if (request.getPartitionCnt() != null && request.getPartitionCnt() < 1) {
            throw new TaskBizException("partitionCnt必须大于等于1");
        }

        if (request.getTaskType() == null) {
            throw new TaskBizException("taskType不能为null");
        }

        if (request.getSchedulePeriod() != null) {
            if (!request.getSchedulePeriod().startsWith("[") || !request.getSchedulePeriod().endsWith("]")) {
                throw new TaskBizException("schedulePeriod必须是数组格式的字符串");
            }
            String[] periods = request.getSchedulePeriod()
                    .substring(1, request.getSchedulePeriod().length() - 1)
                    .split(",");
            for (String period : periods) {
                String trimmed = period.trim();
                if (!trimmed.matches("^\\d{2}:\\d{2}-\\d{2}:\\d{2}$")) {
                    throw new TaskBizException("schedulePeriod元素格式必须为hh:mm-hh:mm");
                }
            }
        }

        if (request.getMaxRetryTimes() != null && request.getMaxRetryTimes() < 0) {
            throw new TaskBizException("maxRetryTimes必须大于等于0");
        }

        if (request.getRetryInterval() != null && request.getRetryInterval() < 5) {
            throw new TaskBizException("retryInterval必须大于等于5");
        }

        if (request.getTimeoutSeconds() != null && request.getTimeoutSeconds() < 60) {
            throw new TaskBizException("timeoutSeconds必须大于等于60");
        }

        if (request.getStatus() != null && request.getStatus() != PayTaskConfigStatusEnum.ENABLE.getStatus()
                && request.getStatus() != PayTaskConfigStatusEnum.DISABLE.getStatus()) {
            throw new TaskBizException("status必须为0或1");
        }
    }
}
