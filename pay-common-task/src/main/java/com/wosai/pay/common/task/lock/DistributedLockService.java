package com.wosai.pay.common.task.lock;

import com.wosai.pay.common.task.autoconfigure.PayTaskProperties;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.concurrent.TimeUnit;

import static com.wosai.pay.common.task.constant.PayTaskConstant.SEPARATOR;

/**
 * <AUTHOR>
 * @description 分布式锁
 * @date 2025-06-17
 */
public class DistributedLockService {
    private final RedissonClient redissonClient;
    private final PayTaskProperties taskProperties;
    private static final Logger log = LoggerFactory.getLogger(DistributedLockService.class);

    public DistributedLockService(RedissonClient redissonClient, PayTaskProperties taskProperties) {
        this.redissonClient = redissonClient;
        this.taskProperties = taskProperties;
    }

    /**
     * 构建key
     *
     * @param prefix
     * @param keys
     * @return
     */
    public RLock buildLock(String prefix, Object... keys) {
        String key = buildKey(prefix, keys);
        return redissonClient.getLock(key);
    }

    /**
     * 构建key
     *
     * @param prefix
     * @param keys
     * @return
     */
    public String buildKey(String prefix, Object... keys) {
        //固定增加全局的前缀，避免多个项目共用同一个实例时，key发生冲突
        String globalPrefix = taskProperties.getGlobalLockPrefix();
        StringBuilder sb = new StringBuilder(globalPrefix).append(SEPARATOR).append(prefix);

        for (Object key : keys) {
            sb.append(SEPARATOR).append(key);
        }
        return sb.toString();
    }

    /**
     * 尝试加锁，自动续期
     *
     * @param lock
     * @return
     */
    public boolean tryLock(RLock lock) {
        try {
            return lock.tryLock();
        } catch (Exception e) {
            log.error("tryLock failed, key={}", lock != null ? lock.getName() : "null", e);
            return false;
        }
    }

    /**
     * 尝试加锁，指定超时时间
     *
     * @param lock
     * @param waitTime
     * @param leaseTime
     * @return
     */
    public boolean tryLock(RLock lock, long waitTime, long leaseTime) {
        try {
            return lock.tryLock(waitTime, leaseTime, TimeUnit.SECONDS);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            return false;
        } catch (Exception e) {
            log.error("tryLock with timeout failed, key={}", lock != null ? lock.getName() : "null", e);
            return false;
        }
    }

    /**
     * 释放锁
     *
     * @param lock
     */
    public void unlock(RLock lock) {
        if (null == lock) {
            return;
        }
        try {
            if (lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        } catch (Exception e) {
            log.error("unlock failed, key={}, error={}", lock.getName(), e.getMessage(), e);
        }
    }
}