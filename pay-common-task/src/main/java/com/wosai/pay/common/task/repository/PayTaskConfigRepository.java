package com.wosai.pay.common.task.repository;

import com.wosai.pay.common.base.exception.JsonException;
import com.wosai.pay.common.task.repository.entity.PayTaskConfigEntity;

import java.util.List;

/**
 * <AUTHOR>
 * @description 任务配置表，持久层接口
 * @date 2025-06-19
 */
public interface PayTaskConfigRepository {

    /**
     * 创建任务配置
     *
     * @param config 任务配置实体
     * @return 创建的记录ID
     */
    long insert(PayTaskConfigEntity config);

    /**
     * 根据ID选择性更新任务配置
     *
     * @param config 任务配置实体
     * @return 更新影响的行数
     */
    void updateSelectiveById(PayTaskConfigEntity config) throws JsonException;

    /**
     * 获取所有启用的任务配置列表
     *
     * @return
     */
    List<PayTaskConfigEntity> getActiveTaskConfigs();

    /**
     * 根据任务类型获取任务配置
     *
     * @param taskType
     * @return
     */
    PayTaskConfigEntity getByTaskType(Integer taskType);
}