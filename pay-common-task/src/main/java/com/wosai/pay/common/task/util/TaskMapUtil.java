package com.wosai.pay.common.task.util;

import java.util.LinkedHashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @description
 * @date 2025-06-30
 */
public class TaskMapUtil {
    /**
     * 初始化map
     *
     * @param entries
     * @return
     * @param <K>
     * @param <V>
     */
    @SuppressWarnings("unchecked")
    public static <K, V> Map<K, V> initMap(Object... entries) {
        Map<K, V> map = new LinkedHashMap<>();
        for (int i = 0; i < entries.length; i += 2) {
            map.put((K) entries[i], (V) entries[i+1]);
        }
        return map;
    }
}
