package com.wosai.pay.common.task.core;

import com.wosai.pay.common.task.autoconfigure.PayTaskProperties;
import com.wosai.pay.common.task.constant.PayTaskAlarmConstant;
import com.wosai.pay.common.task.util.AlarmNotify;
import com.wosai.pay.common.task.util.TaskJsonUtil;
import com.wosai.pay.common.task.util.TaskMapUtil;
import org.redisson.api.RTopic;
import org.redisson.api.RedissonClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.DisposableBean;
import org.springframework.context.ApplicationContext;

import java.util.Arrays;
import java.util.List;

import static com.wosai.pay.common.task.constant.PayTaskConstant.SEPARATOR;
import static com.wosai.pay.common.task.constant.PayTaskConstant.TASK_PROPERTIES_UPDATE_TOPIC;

/**
 * 任务全局配置分布式管理器
 */
public class PayTaskPropertiesDistributor implements DisposableBean {
    private static final String LOG_PREFIX = "[PropertiesDistributor]";
    private static final Logger log = LoggerFactory.getLogger(PayTaskPropertiesDistributor.class);

    private final PayTaskProperties taskProperties;
    private final RedissonClient redissonClient;
    private final ApplicationContext applicationContext;
    private final AlarmNotify alarmNotifier;

    public PayTaskPropertiesDistributor(PayTaskProperties taskProperties,
                                        ApplicationContext applicationContext,
                                        RedissonClient redissonClient,
                                        AlarmNotify alarmNotifier) {
        this.taskProperties = taskProperties;
        this.applicationContext = applicationContext;
        this.redissonClient = redissonClient;
        this.alarmNotifier = alarmNotifier;
    }

    //允许动态更新的配置
    private static final List<String> ALLOWED_DYNAMIC_UPDATE_FIELDS = Arrays.asList(
            "enable",
            "mapperLogEnable",
            "normalScanInterval",
            "taskConfigThreadPoolCoreSize",
            "taskConfigThreadPoolMaxSize",
            "bizGlobalShareThreadPoolCoreSize",
            "bizGlobalShareThreadPoolMaxSize",
            "retryScanInterval",
            "batchSize",
            "maxIterations",
            "countDownLatchTimeout",
            "monitorEnable",
            "monitorInterval",
            "queueWarningThreshold",
            "activeThreadWarningThreshold",
            "alarmNotifyUrl",
            "alarmFrequencyOfReachMaxRetryCnt"
    );

    /**
     * 初始化
     */
    public void init() {
        //订阅配置更新事件
        subscribeTaskPropertiesUpdate();
    }

    /**
     * 订阅配置更新事件
     */
    private void subscribeTaskPropertiesUpdate() {
        try {
            RTopic topic = redissonClient.getTopic(getTaskPropertiesUpdateTopic());
            topic.addListener(PayTaskProperties.class, (channel, newProperties) -> {
                synchronized (this) {
                    log.info("{}: 收到全局任务配置更新事件, redisTopic={}, param={}",
                            LOG_PREFIX, getTaskPropertiesUpdateTopic(), TaskJsonUtil.encode(newProperties));
                    partialUpdateTaskProperties(newProperties);
                    //重新初始化任务组件
                    reinitializeComponents();
                }
            });
            log.info("{}: 已订阅配置更新事件, redisTopic={}", LOG_PREFIX, getTaskPropertiesUpdateTopic());
        } catch (Exception e) {
            log.error("{}: 订阅配置更新事件异常, redisTopic={}", LOG_PREFIX, getTaskPropertiesUpdateTopic(), e);
            alarmNotifier.sendAlert("订阅配置更新事件异常", TaskMapUtil.initMap(
                    PayTaskAlarmConstant.ALARM_ERROR_MSG, e.getMessage()));
        }
    }

    /**
     * 发布配置更新事件
     */
    public synchronized void publishTaskPropertiesUpdate(PayTaskProperties newProperties) {
        String topicKey = getTaskPropertiesUpdateTopic();
        try {
            RTopic topic = redissonClient.getTopic(topicKey);
            topic.publish(newProperties);
            log.info("{}: 配置更新事件已发布, topic={}", LOG_PREFIX, topicKey);
        } catch (Exception e) {
            log.error("{}: 发布配置更新事件失败, topic={}", LOG_PREFIX, topicKey, e);
            alarmNotifier.sendAlert("发布配置更新事件失败", TaskMapUtil.initMap(
                    PayTaskAlarmConstant.ALARM_ERROR_MSG, e.getMessage()));
        }
    }

    /**
     * 构建任务sdk全局配置key
     *
     * @return
     */
    private String getTaskPropertiesUpdateTopic() {
        return taskProperties.getGlobalLockPrefix() + SEPARATOR + TASK_PROPERTIES_UPDATE_TOPIC;
    }

    /**
     * 更新部分配置，受限于ALLOWED_DYNAMIC_UPDATE_FIELDS
     *
     * @param newProperties
     */
    private void partialUpdateTaskProperties(PayTaskProperties newProperties) {
        try {
            for (String fieldName : ALLOWED_DYNAMIC_UPDATE_FIELDS) {
                java.lang.reflect.Field field = PayTaskProperties.class.getDeclaredField(fieldName);
                field.setAccessible(true);
                Object value = field.get(newProperties);
                if (value != null) {
                    log.info("{}: 更新配置字段 {}, 旧值={}, 新值={}",
                            LOG_PREFIX, fieldName, field.get(taskProperties), value);
                    field.set(taskProperties, value);
                }
            }
        } catch (Exception e) {
            log.error("{}: 更新全局任务配置失败", LOG_PREFIX, e);
            alarmNotifier.sendAlert("更新全局任务配置失败", TaskMapUtil.initMap(
                    PayTaskAlarmConstant.ALARM_ERROR_MSG, e.getMessage()));
        }
    }

    /**
     * 重新初始化任务组件
     */
    private void reinitializeComponents() {
        log.info("{}: 重新初始化任务组件...", LOG_PREFIX);

        try {
            applicationContext.getBean(PayTaskDaemonThread.class).reload();
            log.info("{}: 任务组件重新初始化完成", LOG_PREFIX);
        } catch (Exception e) {
            log.error("{}: 重新初始化任务组件失败", LOG_PREFIX, e);
            alarmNotifier.sendAlert("重新初始化任务组件失败", TaskMapUtil.initMap(
                    PayTaskAlarmConstant.ALARM_ERROR_MSG, e.getMessage()));
        }
    }

    /**
     * 释放资源
     */
    @Override
    public void destroy() {
        if (redissonClient != null) {
            RTopic topic = redissonClient.getTopic(getTaskPropertiesUpdateTopic());
            if (topic != null) {
                topic.removeAllListeners();
                log.info("{}: 已取消订阅配置更新事件, topic={}", LOG_PREFIX, getTaskPropertiesUpdateTopic());
            }
        }
    }
}