package com.wosai.pay.common.task.repository.mapper.impl;

import com.wosai.pay.common.task.autoconfigure.PayTaskProperties;
import com.wosai.pay.common.task.enums.PayTaskConfigStatusEnum;
import com.wosai.pay.common.task.exception.TaskDBException;
import com.wosai.pay.common.task.repository.entity.PayTaskConfigEntity;
import com.wosai.pay.common.task.repository.mapper.PayTaskConfigMapper;
import org.springframework.dao.EmptyResultDataAccessException;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.jdbc.support.GeneratedKeyHolder;
import org.springframework.jdbc.support.KeyHolder;

import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

import static com.wosai.pay.common.task.constant.PayTaskConfigColumn.*;

/**
 * <AUTHOR>
 * @description
 * @date 2025-06-17
 */
public class PayTaskConfigMapperImpl implements PayTaskConfigMapper {
    private final JdbcTemplate jdbcTemplate;
    private final PayTaskProperties taskProperties;

    public PayTaskConfigMapperImpl(JdbcTemplate jdbcTemplate, PayTaskProperties taskProperties) {
        this.jdbcTemplate = jdbcTemplate;
        this.taskProperties = taskProperties;
    }

    @Override
    public long insert(PayTaskConfigEntity config) {
        String sql = "INSERT INTO " + taskProperties.getTaskConfigTableName() + " (" +
                TASK_NAME + ", " + TASK_TYPE + ", " +
                PARTITION_CNT + ", " + SCHEDULE_PERIOD + ", " +
                SCHEDULE_INTERVAL + ", " + MAX_RETRY_TIMES + ", " +
                RETRY_INTERVAL + ", " + TIMEOUT_SECONDS + ", " +
                STATUS + ", " + ALERT_CONFIG + ", " +
                EXTRA + ", " + REMARK +
                ") VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";

        KeyHolder keyHolder = new GeneratedKeyHolder();
        jdbcTemplate.update(connection -> {
            PreparedStatement ps = connection.prepareStatement(sql, new String[]{"id"});
            ps.setString(1, config.getTaskName());
            ps.setInt(2, config.getTaskType());
            ps.setInt(3, config.getPartitionCnt());
            ps.setString(4, config.getSchedulePeriod());
            ps.setInt(5, config.getScheduleInterval());
            ps.setInt(6, config.getMaxRetryTimes());
            ps.setInt(7, config.getRetryInterval());
            ps.setInt(8, config.getTimeoutSeconds());
            ps.setInt(9, PayTaskConfigStatusEnum.ENABLE.getStatus());
            ps.setString(10, config.getAlertConfig());
            ps.setString(11, config.getExtra());
            ps.setString(12, config.getRemark());
            return ps;
        }, keyHolder);

        return Objects.requireNonNull(keyHolder.getKey()).longValue();
    }

    @Override
    public int updateSelectiveById(PayTaskConfigEntity config) {
        StringBuilder sql = new StringBuilder("UPDATE " + taskProperties.getTaskConfigTableName() + " SET version = version + 1");
        List<Object> params = new ArrayList<>();

        if (config.getPartitionCnt() != null) {
            sql.append(", " + PARTITION_CNT + " = ?");
            params.add(config.getPartitionCnt());
        }
        if (config.getSchedulePeriod() != null) {
            sql.append(", " + SCHEDULE_PERIOD + " = ?");
            params.add(config.getSchedulePeriod());
        }
        if (config.getScheduleInterval() != null) {
            sql.append(", " + SCHEDULE_INTERVAL + " = ?");
            params.add(config.getScheduleInterval());
        }
        if (config.getMaxRetryTimes() != null) {
            sql.append(", " + MAX_RETRY_TIMES + " = ?");
            params.add(config.getMaxRetryTimes());
        }
        if (config.getRetryInterval() != null) {
            sql.append(", " + RETRY_INTERVAL + " = ?");
            params.add(config.getRetryInterval());
        }
        if (config.getTimeoutSeconds() != null) {
            sql.append(", " + TIMEOUT_SECONDS + " = ?");
            params.add(config.getTimeoutSeconds());
        }
        if (config.getStatus() != null) {
            sql.append(", " + STATUS + " = ?");
            params.add(config.getStatus());
        }
        if (config.getAlertConfig() != null) {
            sql.append(", " + ALERT_CONFIG + " = ?");
            params.add(config.getAlertConfig());
        }
        if (config.getExtra() != null) {
            sql.append(", " + EXTRA + " = ?");
            params.add(config.getExtra());
        }
        if (config.getRemark() != null) {
            sql.append(", " + REMARK + " = ?");
            params.add(config.getRemark());
        }

        sql.append(" WHERE id = ?");
        params.add(config.getId());

        return jdbcTemplate.update(sql.toString(), params.toArray());
    }

    @Override
    public PayTaskConfigEntity getById(Long id) {
        if (id == null) {
            throw new TaskDBException("ID不能为空");
        }
        String sql = "SELECT * FROM " + taskProperties.getTaskConfigTableName() +
                " WHERE " + ID + " = ?";
        try {
            return jdbcTemplate.queryForObject(sql, new TaskConfigRowMapper(), id);
        } catch (EmptyResultDataAccessException e) {
            return null;
        }
    }

    @Override
    public List<PayTaskConfigEntity> getActiveTaskConfigs() {
        String sql = "SELECT * FROM " + taskProperties.getTaskConfigTableName() +
                " WHERE " + STATUS + " = 1";
        return jdbcTemplate.query(sql, new TaskConfigRowMapper());
    }

    @Override
    public PayTaskConfigEntity getByTaskType(Integer taskType) {
        String sql = "SELECT * FROM " + taskProperties.getTaskConfigTableName() +
                " WHERE " + TASK_TYPE + " = ?";
        try {
            return jdbcTemplate.queryForObject(sql, new TaskConfigRowMapper(), taskType);
        } catch (EmptyResultDataAccessException e) {
            return null;
        }
    }

    private static class TaskConfigRowMapper implements RowMapper<PayTaskConfigEntity> {
        @Override
        public PayTaskConfigEntity mapRow(ResultSet rs, int rowNum) throws SQLException {
            PayTaskConfigEntity config = new PayTaskConfigEntity();
            config.setId(rs.getLong(ID));
            config.setTaskName(rs.getString(TASK_NAME));
            config.setTaskType(rs.getInt(TASK_TYPE));
            config.setPartitionCnt(rs.getInt(PARTITION_CNT));
            config.setSchedulePeriod(rs.getString(SCHEDULE_PERIOD));
            config.setScheduleInterval(rs.getInt(SCHEDULE_INTERVAL));
            config.setMaxRetryTimes(rs.getInt(MAX_RETRY_TIMES));
            config.setRetryInterval(rs.getInt(RETRY_INTERVAL));
            config.setTimeoutSeconds(rs.getInt(TIMEOUT_SECONDS));
            config.setStatus(rs.getInt(STATUS));
            config.setAlertConfig(rs.getString(ALERT_CONFIG));
            config.setExtra(rs.getString(EXTRA));
            config.setRemark(rs.getString(REMARK));
            config.setVersion(rs.getLong(VERSION));
            config.setCtime(rs.getObject(CTIME, LocalDateTime.class));
            config.setMtime(rs.getObject(MTIME, LocalDateTime.class));
            config.setDeleted(rs.getInt(DELETED));
            return config;
        }
    }
}