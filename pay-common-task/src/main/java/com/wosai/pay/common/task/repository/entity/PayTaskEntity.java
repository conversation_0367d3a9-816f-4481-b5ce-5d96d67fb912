package com.wosai.pay.common.task.repository.entity;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.wosai.pay.common.base.util.LocalDateTimeFormatter;
import com.wosai.pay.common.task.enums.PayTaskErrorTypeEnum;
import com.wosai.pay.common.task.enums.PayTaskStatusEnum;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @description 任务实例实体
 * @date 2025-06-17
 */
public class PayTaskEntity {
    /**
     * 自增id
     */
    private Long id;
    /**
     * 任务类型。用于区分不同的任务
     */
    private Integer taskType;
    /**
     * 分片id。不同的服务节点可以处理同一个task_type下不同分片id的任务，目的是为了降低单机负载
     */
    private Integer partitionId;
    /**
     * 幂等id。用来防止重复插入相同任务
     */
    private String idempotentId;
    /**
     * 业务标识，如商户ID
     */
    private String bizKey;
    /**
     * 计划执行时间
     */
    @JsonDeserialize(using = LocalDateTimeFormatter.LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeFormatter.LocalDateTimeSerializer.class)
    private LocalDateTime planTime;
    /**
     * 任务实例的执行状态
     *
     * @see PayTaskStatusEnum
     */
    private Integer status;
    /**
     * 任务执行的步骤。由业务方自己定义，默认为null
     */
    private String step;
    /**
     * 重试次数
     */
    private Integer retryCnt;
    /**
     * 下次重试时间
     */
    @JsonDeserialize(using = LocalDateTimeFormatter.LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeFormatter.LocalDateTimeSerializer.class)
    private LocalDateTime nextRetryTime;
    /**
     * 错误类型
     *
     * @see PayTaskErrorTypeEnum
     */
    private String errorType;
    /**
     * 错误信息
     */
    private String errorMsg;
    /**
     * 扩展字段
     */
    private String extra;
    /**
     * 版本号
     */
    private Long version;
    /**
     * 创建时间
     */
    @JsonDeserialize(using = LocalDateTimeFormatter.LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeFormatter.LocalDateTimeSerializer.class)
    private LocalDateTime ctime;
    /**
     * 更新时间
     */
    @JsonDeserialize(using = LocalDateTimeFormatter.LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeFormatter.LocalDateTimeSerializer.class)
    private LocalDateTime mtime;
    /**
     * 是否删除 0：未删除 1：已删除
     */
    private Integer deleted;

    /**
     * 业务扩展字段容器
     */
    private Map<String, Object> bizExtFields = new HashMap<>();

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Integer getTaskType() {
        return taskType;
    }

    public void setTaskType(Integer taskType) {
        this.taskType = taskType;
    }

    public Integer getPartitionId() {
        return partitionId;
    }

    public void setPartitionId(Integer partitionId) {
        this.partitionId = partitionId;
    }

    public String getIdempotentId() {
        return idempotentId;
    }

    public void setIdempotentId(String idempotentId) {
        this.idempotentId = idempotentId;
    }

    public String getBizKey() {
        return bizKey;
    }

    public void setBizKey(String bizKey) {
        this.bizKey = bizKey;
    }

    public LocalDateTime getPlanTime() {
        return planTime;
    }

    public void setPlanTime(LocalDateTime planTime) {
        this.planTime = planTime;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getStep() {
        return step;
    }

    public void setStep(String step) {
        this.step = step;
    }

    public Integer getRetryCnt() {
        return retryCnt;
    }

    public void setRetryCnt(Integer retryCnt) {
        this.retryCnt = retryCnt;
    }

    public LocalDateTime getNextRetryTime() {
        return nextRetryTime;
    }

    public void setNextRetryTime(LocalDateTime nextRetryTime) {
        this.nextRetryTime = nextRetryTime;
    }

    public String getErrorType() {
        return errorType;
    }

    public void setErrorType(String errorType) {
        this.errorType = errorType;
    }

    public String getErrorMsg() {
        return errorMsg;
    }

    public void setErrorMsg(String errorMsg) {
        this.errorMsg = errorMsg;
    }

    public String getExtra() {
        return extra;
    }

    public void setExtra(String extra) {
        this.extra = extra;
    }

    public Long getVersion() {
        return version;
    }

    public void setVersion(Long version) {
        this.version = version;
    }

    public LocalDateTime getCtime() {
        return ctime;
    }

    public void setCtime(LocalDateTime ctime) {
        this.ctime = ctime;
    }

    public LocalDateTime getMtime() {
        return mtime;
    }

    public void setMtime(LocalDateTime mtime) {
        this.mtime = mtime;
    }

    public Integer getDeleted() {
        return deleted;
    }

    public void setDeleted(Integer deleted) {
        this.deleted = deleted;
    }

    public Map<String, Object> getBizExtFields() {
        return bizExtFields;
    }

    public void setBizExtFields(Map<String, Object> bizExtFields) {
        this.bizExtFields = bizExtFields;
    }

    public void setBizExtField(String column, Object value) {
        this.bizExtFields.put(column, value);
    }

    public Object getBizExtField(String column) {
        return this.bizExtFields.get(column);
    }
}