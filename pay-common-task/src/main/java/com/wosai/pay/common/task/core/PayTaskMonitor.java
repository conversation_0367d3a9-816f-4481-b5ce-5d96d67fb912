package com.wosai.pay.common.task.core;

import com.wosai.pay.common.task.autoconfigure.PayTaskProperties;
import com.wosai.pay.common.task.constant.PayTaskAlarmConstant;
import com.wosai.pay.common.task.dto.ThreadPoolConfigDTO;
import com.wosai.pay.common.task.util.AlarmNotify;
import com.wosai.pay.common.task.util.TaskJsonUtil;
import com.wosai.pay.common.task.util.TaskMapUtil;
import org.redisson.api.RTopic;
import org.redisson.api.RedissonClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.DisposableBean;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

import static com.wosai.pay.common.task.constant.PayTaskConstant.*;

/**
 * 任务线程池监控器
 */
public class PayTaskMonitor implements DisposableBean {
    private static final Logger log = LoggerFactory.getLogger(PayTaskMonitor.class);
    private static final String LOG_PREFIX = "[Monitor]";

    private final AlarmNotify alarmNotifier;
    private final RedissonClient redissonClient;
    private final PayTaskProperties taskProperties;

    // 注册的线程池
    private final Map<String, ThreadPoolTaskExecutor> threadPools = new ConcurrentHashMap<>();

    // 监控调度器
    private ScheduledExecutorService monitorScheduler;

    public PayTaskMonitor(PayTaskProperties taskProperties,
                          RedissonClient redissonClient,
                          AlarmNotify alarmNotifier) {
        this.taskProperties = taskProperties;
        this.redissonClient = redissonClient;
        this.alarmNotifier = alarmNotifier;
    }

    /**
     * 初始化
     */
    public void init() {
        //订阅线程池参数更新事件
        subscribeThreadPoolUpdates();
    }

    /**
     * 注册线程池
     */
    public void registerThreadPool(String poolName, ThreadPoolTaskExecutor threadPoolTaskExecutor) {
        threadPools.put(poolName, threadPoolTaskExecutor);
        log.info("{}: 注册线程池 {} 成功", LOG_PREFIX, poolName);
    }

    //初始化监控器
    private void initMonitorScheduler() {
        monitorScheduler = Executors.newSingleThreadScheduledExecutor(r -> {
            Thread t = new Thread(r);
            t.setName(MONITOR_THREAD_POOL_NAME + CONNECTOR + t.getId());
            // 设置异常处理器
            t.setUncaughtExceptionHandler((thread, e) ->
                    log.error("{}: 监控调度器发生异常", LOG_PREFIX, e)
            );
            return t;
        });
        monitorScheduler.scheduleWithFixedDelay(this::monitorAllPools,
                0, taskProperties.getMonitorInterval(), TimeUnit.SECONDS);
    }

    /**
     * 启动监控
     */
    public void start() {
        if (!taskProperties.checkIsMonitorEnable()) {
            log.info("{}: 监控器未启用, 不再监控线程池状态", LOG_PREFIX);
            return;
        }

        //初始化监控器
        initMonitorScheduler();
        log.info("{}: 启动线程池监控器，监控间隔 {} 秒", LOG_PREFIX, taskProperties.getMonitorInterval());
    }

    /**
     * 重新加载
     */
    public void reload() {
        log.info("{}: 监控器重新初始化...", LOG_PREFIX);
        if (monitorScheduler != null && !monitorScheduler.isShutdown()) {
            monitorScheduler.shutdown();
        }

        if (taskProperties.checkIsMonitorEnable()) {
            //重新初始化监控器
            initMonitorScheduler();
            log.info("{}: 重新启动监控器，监控间隔 {} 秒", LOG_PREFIX, taskProperties.getMonitorInterval());
        } else {
            log.info("{}: 监控器模块关闭, 不再监控线程池执行情况", LOG_PREFIX);
        }

        log.info("{}: 监控器初始化完成", LOG_PREFIX);
    }

    /**
     * 停止监控
     */
    public void stop() {
        if (monitorScheduler != null) {
            monitorScheduler.shutdown();
            log.info("{}: 线程池监控器已停止", LOG_PREFIX);
        }
    }

    /**
     * 监控所有线程池
     */
    private void monitorAllPools() {
        try {
            threadPools.forEach(this::monitorSinglePool);
        } catch (Exception e) {
            log.error("{}: 线程池监控异常", LOG_PREFIX, e);
        }
    }

    /**
     * 监控单个线程池
     */
    private void monitorSinglePool(String poolName, ThreadPoolTaskExecutor executor) {
        if (executor == null) {
            return;
        }

        int activeCount = executor.getActiveCount();
        int queueSize = executor.getThreadPoolExecutor().getQueue().size();
        int poolSize = executor.getPoolSize();
        int corePoolSize = executor.getCorePoolSize();
        int maxPoolSize = executor.getMaxPoolSize();
        long totalTaskCount = executor.getThreadPoolExecutor().getTaskCount();
        long completedTaskCount = executor.getThreadPoolExecutor().getCompletedTaskCount();
        long largestPoolSize = executor.getThreadPoolExecutor().getLargestPoolSize();

        // 记录监控指标
        int queueCapacity = executor.getThreadPoolExecutor().getQueue().remainingCapacity() + queueSize;
        log.info("{}: [{}] - 活跃线程数/当前线程总数: {}/{}, 队列大小/队列容量: {}/{}, 历史最大线程数: {}, 核心线程: {}, 最大线程: {}, 已完成任务数: {}, 已提交任务总数: {}",
                LOG_PREFIX, poolName, activeCount, poolSize, queueSize, queueCapacity, largestPoolSize, corePoolSize, maxPoolSize, completedTaskCount, totalTaskCount);

        // 触发告警条件
        if (queueSize > taskProperties.getQueueWarningThreshold()) {
            sendThreadPoolAlarm(poolName, "队列积压告警", executor);
        }

        if (activeCount > taskProperties.getActiveThreadWarningThreshold()) {
            sendThreadPoolAlarm(poolName, "活跃线程过多告警", executor);
        }
    }

    /**
     * 发送线程池监控告警
     *
     * @param poolName
     * @param executor
     */
    private void sendThreadPoolAlarm(String poolName, String alarmType, ThreadPoolTaskExecutor executor) {
        Map<String, Object> alarmInfo = TaskMapUtil.initMap(
                "线程池", poolName,
                "当前队列积压数/告警阈值", executor.getThreadPoolExecutor().getQueue().size() + "/" + taskProperties.getQueueWarningThreshold(),
                "当前活跃线程数/告警阈值", executor.getActiveCount() + "/" + taskProperties.getActiveThreadWarningThreshold(),
                "当前线程总数", executor.getPoolSize(),
                "核心线程数", executor.getCorePoolSize(),
                "最大线程数", executor.getMaxPoolSize());
        alarmNotifier.sendAlert(alarmType, alarmInfo);
    }

    /**
     * 订阅线程池配置更新事件
     */
    private void subscribeThreadPoolUpdates() {
        try {
            RTopic topic = redissonClient.getTopic(getThreadPoolUpdateTopic());
            topic.addListener(ThreadPoolConfigDTO.class, (channel, config) -> {
                synchronized (this) {
                    log.info("{}: 收到线程池配置更新事件: redisTopic={}, param={}",
                            LOG_PREFIX, getThreadPoolUpdateTopic(), TaskJsonUtil.encode(config));
                    updateLocalThreadPoolConfig(config);
                }
            });
            log.info("{}: 已订阅线程池配置更新事件, redisTopic={}", LOG_PREFIX, getThreadPoolUpdateTopic());
        } catch (Exception e) {
            log.error("{}: 订阅线程池配置更新事件异常, 尝试重新订阅", LOG_PREFIX, e);
            alarmNotifier.sendAlert("订阅线程池配置更新事件异常", TaskMapUtil.initMap(
                    PayTaskAlarmConstant.ALARM_ERROR_MSG, e.getMessage()));
        }
    }

    /**
     * 更新线程池配置并发布到所有节点
     */
    public String updateThreadPoolConfig(ThreadPoolConfigDTO config) {
        if (config == null) {
            return "线程池配置不能为null";
        }

        // 1. 参数校验
        String errorInfo = validateThreadPoolParams(config);
        if (errorInfo != null) {
            return errorInfo;
        }

        // 2. 发布配置更新事件
        try {
            RTopic topic = redissonClient.getTopic(getThreadPoolUpdateTopic());
            topic.publish(config);
            log.info("{}: 线程池配置更新事件已发布: {}, topic={}", LOG_PREFIX, config, getThreadPoolUpdateTopic());
            return "success";
        } catch (Exception e) {
            String errorMsg = "发布线程池配置更新事件失败: " + e.getMessage();
            log.error("{}: {}", LOG_PREFIX, errorMsg, e);
            alarmNotifier.sendAlert("发布线程池配置更新事件失败", TaskMapUtil.initMap(
                    PayTaskAlarmConstant.ALARM_ERROR_MSG, e.getMessage()));
            return errorMsg;
        }
    }

    /**
     * 更新本地线程池配置
     */
    private void updateLocalThreadPoolConfig(ThreadPoolConfigDTO config) {
        try {
            ThreadPoolTaskExecutor executor = threadPools.get(config.getPoolName());
            if (executor == null) {
                String errorMsg = "更新失败, 找不到指定的线程池: " + config.getPoolName();
                log.error("{}: {}", LOG_PREFIX, errorMsg);
                return;
            }

            synchronized (executor) {
                executor.setCorePoolSize(config.getCoreSize());
                executor.setMaxPoolSize(config.getMaxSize());
            }
            log.info("{}: 本地线程池 {} 配置更新成功, coreSize={}, maxSize={}",
                    LOG_PREFIX, config.getPoolName(), config.getCoreSize(), config.getMaxSize());
        } catch (Exception e) {
            String errorMsg = String.format("更新线程池 %s 配置失败: %s",
                    config.getPoolName(), e.getMessage());
            log.error("{}: {}", LOG_PREFIX, errorMsg, e);
            alarmNotifier.sendAlert("线程池配置变更失败", TaskMapUtil.initMap(
                    PayTaskAlarmConstant.ALARM_THREAD_POOL_NAME, config.getPoolName(),
                    PayTaskAlarmConstant.ALARM_ERROR_MSG, e.getMessage()));
        }
    }

    /**
     * 校验线程池参数
     */
    private String validateThreadPoolParams(ThreadPoolConfigDTO config) {
        if (config == null) {
            String errorMsg = "更新失败, 线程池配置不能为null";
            log.error("{}: {}", LOG_PREFIX, errorMsg);
            return errorMsg;
        }
        if (config.getPoolName() == null || config.getPoolName().isEmpty()) {
            String errorMsg = "更新失败, 线程池名字不能为null";
            log.error("{}: {}", LOG_PREFIX, errorMsg);
            return errorMsg;
        }
        if (config.getCoreSize() == null || config.getMaxSize() == null) {
            log.error("{}: 更新失败, 必须指定coreSize和maxSize", LOG_PREFIX);
            return "更新失败, 必须配置coreSize和maxSize";
        }
        if (config.getCoreSize() <= 0 || config.getMaxSize() <= 0) {
            String errorMsg = String.format("更新失败, 无效的线程池配置 coreSize=%d, maxSize=%d",
                    config.getCoreSize(), config.getMaxSize());
            log.error("{}: {}", LOG_PREFIX, errorMsg);
            return errorMsg;
        }
        if (config.getCoreSize() > config.getMaxSize()) {
            String errorMsg = String.format("更新失败, 核心线程数(%d)不能大于最大线程数(%d)",
                    config.getCoreSize(), config.getMaxSize());
            log.error("{}: {}", LOG_PREFIX, errorMsg);
            return errorMsg;
        }

        ThreadPoolTaskExecutor executor = threadPools.get(config.getPoolName());
        if (executor == null) {
            String errorMsg = "更新失败, 找不到指定的线程池: " + config.getPoolName();
            log.error("{}: {}", LOG_PREFIX, errorMsg);
            return errorMsg;
        }

        return null;
    }

    /**
     * 获取线程池配置更新topic
     *
     * @return
     */
    private String getThreadPoolUpdateTopic() {
        return taskProperties.getGlobalLockPrefix() + SEPARATOR + THREAD_POOL_UPDATE_TOPIC;
    }

    @Override
    public void destroy() {
        //关闭监控调度器
        stop();

        if (redissonClient != null) {
            //取消订阅
            RTopic threadPoolTopic = redissonClient.getTopic(getThreadPoolUpdateTopic());
            if (threadPoolTopic != null) {
                threadPoolTopic.removeAllListeners();
                log.info("{}: 已取消订阅线程池配置更新事件, topic={}", LOG_PREFIX, getThreadPoolUpdateTopic());
            }
        }
    }

    public Map<String, ThreadPoolTaskExecutor> getThreadPools() {
        return threadPools;
    }
}