package com.wosai.pay.common.task.service;

import com.wosai.pay.common.task.autoconfigure.PayTaskProperties;
import com.wosai.pay.common.task.dto.ThreadPoolConfigDTO;
import com.wosai.pay.common.task.dto.ThreadPoolStatusDTO;

import java.util.List;

/**
 * 任务服务接口
 */
public interface PayTaskService {
    /**
     * 查询全局的任务配置
     */
    PayTaskProperties queryGlobalTaskConfig();

    /**
     * 重新加载任务组件
     *
     * @return 最新任务配置
     */
    String reload(PayTaskProperties newProperties);

    /**
     * 查询所有线程池状态
     *
     * @return 线程池状态列表
     */
    List<ThreadPoolStatusDTO> queryAllThreadPoolRunningInfo();

    /**
     * 修改线程池配置
     * @param config 线程池配置DTO
     * @return 是否修改成功
     */
    String updateThreadPoolConfig(ThreadPoolConfigDTO config);
}