package com.wosai.pay.common.task.constant;

/**
 * <AUTHOR>
 * @description
 * @date 2025-06-23
 */

public class PayTaskRedisPrefix {
    /**
     * 任务全局锁
     */
    public static final String TASK_GLOBAL_LOCK = "tk_global_lock";
    /**
     * 重试任务全局锁
     */
    public static final String RETRY_TASK_GLOBAL_LOCK = "tk_retry_global_lock";
    /**
     * 任务业务维度的锁
     */
    public static final String TASK_BIZ_LOCK = "tk_biz_lock";
    /**
     * 任务最后执行时间
     */
    public static final String TASK_LAST_EXEC_TIME = "tk_last_exec_time";
    /**
     * 达到最大重试次数告警
     */
    public static final String ALARM_REACH_MAX_RETRY_CNT = "alarm_reach_max_retry_count";
}
