package com.wosai.pay.common.task.repository.dto;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.wosai.pay.common.base.util.LocalDateTimeFormatter;
import com.wosai.pay.common.task.repository.entity.PayTaskEntity;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * 创建任务实例DTO
 */
public class PayTaskCreateDTO {
    private Integer taskType;   //任务类型
    private String idempotentId;//幂等id
    private String bizKey;      //业务字段
    @JsonDeserialize(using = LocalDateTimeFormatter.LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeFormatter.LocalDateTimeSerializer.class)
    private LocalDateTime planTime;//计划执行时间
    private String step; //执行步骤
    private String extra;//扩展信息
    private Map<String, Object> bizExtFields = new HashMap<>();//任务实例表扩展字段

    public Integer getTaskType() {
        return taskType;
    }

    public void setTaskType(Integer taskType) {
        this.taskType = taskType;
    }

    public String getIdempotentId() {
        return idempotentId;
    }

    public void setIdempotentId(String idempotentId) {
        this.idempotentId = idempotentId;
    }

    public String getBizKey() {
        return bizKey;
    }

    public void setBizKey(String bizKey) {
        this.bizKey = bizKey;
    }

    public LocalDateTime getPlanTime() {
        return planTime;
    }

    public void setPlanTime(LocalDateTime planTime) {
        this.planTime = planTime;
    }

    public String getStep() {
        return step;
    }

    public void setStep(String step) {
        this.step = step;
    }

    public String getExtra() {
        return extra;
    }

    public void setExtra(String extra) {
        this.extra = extra;
    }

    public Map<String, Object> getBizExtFields() {
        return bizExtFields;
    }

    public void setBizExtFields(Map<String, Object> bizExtFields) {
        this.bizExtFields = bizExtFields;
    }

    public PayTaskEntity toEntity() {
        PayTaskEntity entity = new PayTaskEntity();
        entity.setTaskType(this.taskType);
        entity.setIdempotentId(this.idempotentId);
        entity.setPlanTime(this.planTime);
        entity.setBizKey(this.bizKey);
        entity.setStep(this.step);
        entity.setExtra(this.extra);
        if (this.bizExtFields != null && !this.bizExtFields.isEmpty()) {
            this.bizExtFields.forEach(entity::setBizExtField);
        }
        return entity;
    }
}