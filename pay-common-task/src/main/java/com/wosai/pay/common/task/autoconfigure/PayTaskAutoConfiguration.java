package com.wosai.pay.common.task.autoconfigure;

import com.wosai.pay.common.task.AbstractPayTaskStrategy;
import com.wosai.pay.common.task.core.PayTaskDaemonThread;
import com.wosai.pay.common.task.core.PayTaskExecuteTemplate;
import com.wosai.pay.common.task.core.PayTaskMonitor;
import com.wosai.pay.common.task.core.PayTaskPropertiesDistributor;
import com.wosai.pay.common.task.lock.DistributedLockService;
import com.wosai.pay.common.task.repository.PayTaskConfigRepository;
import com.wosai.pay.common.task.repository.PayTaskRepository;
import com.wosai.pay.common.task.repository.impl.PayTaskConfigRepositoryImpl;
import com.wosai.pay.common.task.repository.impl.PayTaskRepositoryImpl;
import com.wosai.pay.common.task.repository.mapper.PayTaskConfigMapper;
import com.wosai.pay.common.task.repository.mapper.PayTaskMapper;
import com.wosai.pay.common.task.repository.mapper.impl.PayTaskConfigMapperImpl;
import com.wosai.pay.common.task.repository.mapper.impl.PayTaskMapperImpl;
import com.wosai.pay.common.task.service.PayTaskService;
import com.wosai.pay.common.task.service.PayTaskServiceImpl;
import com.wosai.pay.common.task.util.AlarmNotify;
import com.wosai.pay.common.task.util.TraceUtil;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationListener;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.event.ContextRefreshedEvent;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.Map;

import static com.wosai.pay.common.task.constant.PayTaskConstant.*;

/**
 * <AUTHOR>
 * @description 通用任务模块自动装配类
 * @date 2025-06-18
 */
@Configuration
public class PayTaskAutoConfiguration implements ApplicationListener<ContextRefreshedEvent> {
    @Bean
    public DistributedLockService distributedLockService(PayTaskComponent taskComponent, PayTaskProperties taskProperties) {
        return new DistributedLockService(taskComponent.getRedissonClient(), taskProperties);
    }

    @Bean
    public PayTaskConfigMapper payTaskConfigMapper(PayTaskComponent taskComponent, PayTaskProperties taskProperties) {
        return new PayTaskConfigMapperImpl(taskComponent.getJdbcTemplate(), taskProperties);
    }

    @Bean
    public PayTaskMapper payTaskMapper(PayTaskComponent taskComponent, PayTaskProperties taskProperties) {
        return new PayTaskMapperImpl(taskComponent.getJdbcTemplate(), taskProperties);
    }

    @Bean
    public PayTaskConfigRepository payTaskConfigRepository(PayTaskConfigMapper taskConfigMapper) {
        return new PayTaskConfigRepositoryImpl(taskConfigMapper);
    }

    @Bean
    public PayTaskRepository payTaskRepository(
            PayTaskMapper taskMapper,
            PayTaskConfigMapper taskConfigMapper) {
        return new PayTaskRepositoryImpl(taskMapper, taskConfigMapper);
    }

    @Bean(initMethod = "init")
    public PayTaskPropertiesDistributor payTaskPropertiesDistributor(PayTaskProperties taskProperties,
                                                                     ApplicationContext applicationContext,
                                                                     PayTaskComponent taskComponent,
                                                                     AlarmNotify alarmNotifier) {
        return new PayTaskPropertiesDistributor(taskProperties, applicationContext, taskComponent.getRedissonClient(), alarmNotifier);
    }

    @Bean
    public PayTaskService payTaskService(PayTaskProperties taskProperties,
                                         PayTaskMonitor taskMonitor,
                                         PayTaskPropertiesDistributor taskPropertiesDistributor) {
        return new PayTaskServiceImpl(taskProperties, taskMonitor, taskPropertiesDistributor);
    }

    @Bean
    public PayTaskExecuteTemplate taskExecuteTemplate(
            DistributedLockService lockService,
            PayTaskProperties taskProperties,
            PayTaskRepository taskRepository,
            ThreadPoolTaskExecutor bizGlobalShareTaskExecutor,
            PayTaskComponent taskComponent,
            PayTaskMonitor taskMonitor,
            AlarmNotify alarmNotify) {
        return new PayTaskExecuteTemplate(lockService, taskProperties,
                taskRepository, bizGlobalShareTaskExecutor, taskComponent.getRedissonClient(), taskMonitor, alarmNotify);
    }

    @Bean
    public ThreadPoolTaskExecutor normalTaskExecutor(PayTaskProperties taskProperties) {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(taskProperties.getTaskConfigThreadPoolCoreSize());
        executor.setMaxPoolSize(taskProperties.getTaskConfigThreadPoolMaxSize());
        executor.setQueueCapacity(taskProperties.getQueueCapacity());
        executor.setThreadNamePrefix(DAEMON_TASK_THREAD_POOL_NAME + CONNECTOR);
        executor.initialize();
        return executor;
    }

    @Bean
    public ThreadPoolTaskExecutor bizGlobalShareTaskExecutor(PayTaskProperties taskProperties) {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(taskProperties.getBizGlobalShareThreadPoolCoreSize());
        executor.setMaxPoolSize(taskProperties.getBizGlobalShareThreadPoolMaxSize());
        executor.setQueueCapacity(taskProperties.getQueueCapacity());
        executor.setThreadNamePrefix(BIZ_GLOBAL_SHARE_THREAD_POOL_NAME + CONNECTOR);
        executor.initialize();
        return executor;
    }

    /**
     * 初始化告警通知bean
     */
    @Bean
    public AlarmNotify alarmNotifier(PayTaskProperties taskProperties) {
        return new AlarmNotify(taskProperties);
    }

    /**
     * 初始化任务监控器bean
     */
    @Bean(initMethod = "init")
    public PayTaskMonitor taskMonitor(PayTaskProperties taskProperties, PayTaskComponent taskComponent, AlarmNotify alarmNotifier) {
        return new PayTaskMonitor(taskProperties, taskComponent.getRedissonClient(), alarmNotifier);
    }

    /**
     * 初始化守护线程bean
     *
     * @param taskProperties       任务全局配置
     * @param taskConfigRepository 任务配置持久层
     * @param taskExecuteTemplate  任务执行模板类
     * @param taskMonitor          任务监控器
     * @return
     */
    @Bean(initMethod = "start", destroyMethod = "stop")
    public PayTaskDaemonThread taskDaemonThread(
            PayTaskProperties taskProperties,
            PayTaskConfigRepository taskConfigRepository,
            PayTaskExecuteTemplate taskExecuteTemplate,
            ThreadPoolTaskExecutor normalTaskExecutor,
            PayTaskMonitor taskMonitor) {
        return new PayTaskDaemonThread(
                taskProperties,
                taskConfigRepository,
                taskExecuteTemplate,
                normalTaskExecutor,
                taskMonitor);
    }

    @Override
    public void onApplicationEvent(ContextRefreshedEvent event) {
        if (event.getApplicationContext().getParent() != null) {
            //父类容器直接返回
            return;
        }
        ApplicationContext applicationContext = event.getApplicationContext();
        PayTaskExecuteTemplate executeTemplate = applicationContext.getBean(PayTaskExecuteTemplate.class);
        // 获取所有AbstractPayTaskStrategy实现类
        Map<String, AbstractPayTaskStrategy> strategies =
                applicationContext.getBeansOfType(AbstractPayTaskStrategy.class);

        // 注册到executeTemplate中
        strategies.forEach((beanName, strategy) -> {
            executeTemplate.registerStrategy(strategy.getPayTaskType(), strategy);
        });

        //重置traceIdKey
        PayTaskProperties taskProperties = applicationContext.getBean(PayTaskProperties.class);
        TraceUtil.resetTraceIdKey(taskProperties.getTraceIdKey());
    }
}