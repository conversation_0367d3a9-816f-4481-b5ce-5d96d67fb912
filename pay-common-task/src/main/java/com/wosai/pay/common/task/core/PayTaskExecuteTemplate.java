package com.wosai.pay.common.task.core;

import com.wosai.pay.common.task.util.TraceUtil;
import com.wosai.pay.common.task.AbstractPayTaskStrategy;
import com.wosai.pay.common.task.PayTaskStrategy;
import com.wosai.pay.common.task.autoconfigure.PayTaskProperties;
import com.wosai.pay.common.task.constant.PayTaskAlarmConstant;
import com.wosai.pay.common.task.enums.PayTaskStatusEnum;
import com.wosai.pay.common.task.lock.DistributedLockService;
import com.wosai.pay.common.task.repository.PayTaskRepository;
import com.wosai.pay.common.task.repository.entity.PayTaskConfigEntity;
import com.wosai.pay.common.task.repository.entity.PayTaskEntity;
import com.wosai.pay.common.task.util.AlarmNotify;
import com.wosai.pay.common.task.util.TaskMapUtil;
import org.redisson.api.RBucket;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.util.StopWatch;

import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.RejectedExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.wosai.pay.common.task.constant.PayTaskConstant.BIZ_CUSTOM_THREAD_POOL_NAME_PREFIX;
import static com.wosai.pay.common.task.constant.PayTaskConstant.CONNECTOR;
import static com.wosai.pay.common.task.constant.PayTaskRedisPrefix.*;
import static com.wosai.pay.common.task.enums.PayTaskStatusEnum.*;

public class PayTaskExecuteTemplate {
    private static final String LOG_PREFIX = "[BatchExec]";
    private static final String LOG_PREFIX_RETRY = "[TaskRetry]";
    private static final Logger log = LoggerFactory.getLogger(PayTaskExecuteTemplate.class);

    private final PayTaskProperties taskProperties;
    private final DistributedLockService distributedLockService;
    private final PayTaskRepository taskRepository;
    private final PayTaskMonitor taskMonitor;
    private final ThreadPoolTaskExecutor bizGlobalShareTaskExecutor;
    private final RedissonClient redissonClient;
    private final AlarmNotify alarmNotify;
    private final Map<Integer, PayTaskStrategy> strategyMap = new ConcurrentHashMap<>();

    private static final List<PayTaskStatusEnum> PENDING_TASK_STATUS_LIST = Arrays.asList(
            PENDING,
            IN_PROGRESS
    );
    private static final List<PayTaskStatusEnum> RETRY_TASK_STATUS_LIST = Collections.singletonList(
            PayTaskStatusEnum.FAILURE
    );

    public PayTaskExecuteTemplate(DistributedLockService distributedLockService,
                                  PayTaskProperties taskProperties,
                                  PayTaskRepository taskRepository,
                                  ThreadPoolTaskExecutor bizGlobalShareTaskExecutor,
                                  RedissonClient redissonClient,
                                  PayTaskMonitor taskMonitor,
                                  AlarmNotify alarmNotify) {
        this.distributedLockService = distributedLockService;
        this.taskProperties = taskProperties;
        this.taskRepository = taskRepository;
        this.bizGlobalShareTaskExecutor = bizGlobalShareTaskExecutor;
        this.redissonClient = redissonClient;
        this.taskMonitor = taskMonitor;
        this.alarmNotify = alarmNotify;
    }

    /**
     * 执行任务
     *
     * @param config      任务配置
     * @param partitionId 分片id
     */
    public void execute(PayTaskConfigEntity config, int partitionId) {
        int taskType = config.getTaskType();
        StopWatch stopWatch = null;
        RLock lock = null;
        try {
            TraceUtil.createTraceId();
            // 尝试获取锁，如果获取成功则执行任务
            lock = buildGlobalTaskLock(config.getTaskType(), partitionId);
            if (!distributedLockService.tryLock(lock)) {
                log.warn("{}: 获取分布式锁失败, 跳过执行, taskType={}, partitionId={}", LOG_PREFIX, taskType, partitionId);
                return;
            }

            log.info("{}: 调度器开始执行, taskName={}, taskType={}, partitionId={}", LOG_PREFIX, config.getTaskName(), taskType, partitionId);
            stopWatch = new StopWatch();
            stopWatch.start();

            // 记录本次执行时间，过期时间为调度周期
            recordTaskLastExecTime(taskType, partitionId, config.getScheduleInterval());

            //处理任务
            doExecute(config, partitionId);
        } catch (Throwable e) {
            //出现异常则删除最新执行时间
            deleteTaskLastExecTime(taskType, partitionId);

            log.error("{}: 调度器执行异常, taskName={}, taskType={}, partitionId={}, error={}",
                    LOG_PREFIX, config.getTaskName(), taskType, partitionId, e.getMessage(), e);
        } finally {
            if (null != stopWatch && stopWatch.isRunning()) {
                stopWatch.stop();
                String msg = String.format("%s: 调度器执行完成, taskName=%s, taskType=%d, partitionId=%d, 耗时:%.1f秒 [%.1f分钟]",
                        LOG_PREFIX, config.getTaskName(), taskType, partitionId, stopWatch.getTotalTimeSeconds(), stopWatch.getTotalTimeSeconds() / 60);
                log.info("{}", msg);
            }
            distributedLockService.unlock(lock);
            TraceUtil.removeAll();
        }
    }

    private void doExecute(PayTaskConfigEntity config, int partitionId) {
        int taskType = config.getTaskType();
        // 根据taskType获取任务策略
        PayTaskStrategy strategy = getStrategy(taskType);
        if (strategy == null) {
            log.error("{}: No strategy registered for task type: {}", LOG_PREFIX, taskType);
            alarmNotify.sendAlert("任务无法执行告警", TaskMapUtil.initMap(
                    PayTaskAlarmConstant.ALARM_TASK_NAME, config.getTaskName(),
                    PayTaskAlarmConstant.ALARM_TASK_TYPE, taskType,
                    PayTaskAlarmConstant.ALARM_ERROR_MSG, "未注册该任务类型的处理策略"));
            return;
        }

        int batchSize = taskProperties.getBatchSize();
        Long lastId = 0L;
        int iterationCount = 0;
        int maxIterations = taskProperties.getMaxIterations();
        LocalDateTime currentDate = LocalDateTime.now();

        while (iterationCount++ < maxIterations) {
            // 1. 获取待处理任务
            List<PayTaskEntity> tasks = taskRepository.queryTasksByCondition(
                    taskType,
                    partitionId,
                    PENDING_TASK_STATUS_LIST,
                    currentDate,
                    lastId,
                    batchSize
            );

            // 2. 处理当前批次
            if (!tasks.isEmpty()) {
                log.info("{}: 当前处理批次, taskType={}, partitionId={}, batchNo={}, batchSize={}, taskSize={}",
                        LOG_PREFIX, taskType, partitionId, iterationCount, batchSize, tasks.size());

                //批量处理
                processTaskBatch(config, tasks, strategy, iterationCount);
                lastId = tasks.get(tasks.size() - 1).getId();
            }

            // 3. 检查是否继续
            if (tasks.size() < batchSize) {
                break;
            }
        }
    }

    /**
     * 批量处理任务实例
     *
     * @param config   任务配置
     * @param tasks    任务实例列表
     * @param strategy 处理策略
     * @param batchNo  批次号
     */
    private void processTaskBatch(PayTaskConfigEntity config, List<PayTaskEntity> tasks, PayTaskStrategy strategy, int batchNo) {
        //过滤出有效任务
        List<PayTaskEntity> tasksToProcess = filterTask(config, tasks);
        if (tasksToProcess.isEmpty()) {
            log.info("{}: 当前批次没有需要处理的任务, 跳过处理, taskType={}, batchNo={}", LOG_PREFIX, config.getTaskType(), batchNo);
            return;
        }

        int partitionId = tasks.get(0).getPartitionId();
        //获取业务线程池
        ThreadPoolTaskExecutor bizThreadPool = strategy.getBizThreadPool();
        if (bizThreadPool == null) {
            log.error("{}: 业务线程池未配置, 无法提交任务, taskType={}", LOG_PREFIX, config.getTaskType());
            return;
        }

        CountDownLatch countDownLatch = new CountDownLatch(tasksToProcess.size());
        //将任务提交到业务线程池中执行
        for (PayTaskEntity task : tasksToProcess) {
            submitTaskToThreadPool(config, strategy, bizThreadPool, task, countDownLatch);
        }

        // 等待所有任务完成
        try {
            boolean finished = countDownLatch.await(taskProperties.getCountDownLatchTimeout(), TimeUnit.SECONDS);
            if (!finished) {
                log.error("{}: 任务执行超时, 终止等待, taskType={}, partitionId={}, batchNo={}",
                        LOG_PREFIX, strategy.getPayTaskType(), partitionId, batchNo);
                alarmNotify.sendAlert("任务执行超时",
                        TaskMapUtil.initMap(
                                PayTaskAlarmConstant.ALARM_TASK_NAME, config.getTaskName(),
                                PayTaskAlarmConstant.ALARM_TASK_TYPE, strategy.getPayTaskType(),
                                PayTaskAlarmConstant.ALARM_PARTITION_ID, partitionId,
                                PayTaskAlarmConstant.ALARM_BATCH_NO, batchNo));
            }
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.error("{}: 等待任务实例完成时被中断, taskType={}, partitionId={}, batchNo={}",
                    LOG_PREFIX, strategy.getPayTaskType(), partitionId, batchNo, e);
        }
    }

    /**
     * 提交任务到线程池执行
     *
     * @param config         任务配置
     * @param strategy       任务策略
     * @param bizThreadPool  业务线程池
     * @param task           任务实例
     * @param countDownLatch 计数器
     */
    private void submitTaskToThreadPool(PayTaskConfigEntity config,
                                        PayTaskStrategy strategy,
                                        ThreadPoolTaskExecutor bizThreadPool,
                                        PayTaskEntity task,
                                        CountDownLatch countDownLatch) {
        try {
            bizThreadPool.execute(() -> {
                try {
                    strategy.execute(config, task);
                } finally {
                    countDownLatch.countDown();
                }
            });
        } catch (RejectedExecutionException e) {
            log.error("{}: 任务被线程池拒绝, 将在下次调度处理, taskId={}, threadPoolName={}",
                    LOG_PREFIX, task.getId(), bizThreadPool.getThreadNamePrefix(), e);
            countDownLatch.countDown();
        } catch (Exception e) {
            log.error("{}: 提交任务到线程池异常, taskId={}, threadPoolName={}", LOG_PREFIX, task.getId(), bizThreadPool.getThreadNamePrefix(), e);
            countDownLatch.countDown();
        }
    }

    /**
     * 过滤掉无须执行的任务
     *
     * @param config
     * @param tasks
     * @return
     */
    private List<PayTaskEntity> filterTask(PayTaskConfigEntity config, List<PayTaskEntity> tasks) {
        //过滤掉已经是进行中但还未处理超时的任务
        LocalDateTime currentTime = LocalDateTime.now();
        LocalDateTime timeoutDeadline = currentTime.minusSeconds(config.getTimeoutSeconds());
        return tasks.stream()
                .filter(task -> {
                    int status = task.getStatus();
                    // 过滤掉取消状态和成功状态的任务
                    if (CANCEL.getStatus() == status || SUCCESS.getStatus() == status) {
                        return false;
                    }

                    if (task.getNextRetryTime() != null && task.getNextRetryTime().isAfter(currentTime)) {
                        //还未到下次重试时间，跳过，等待下次调度
                        return false;
                    }

                    // 如果不是进行中状态，直接处理
                    if (IN_PROGRESS.getStatus() != status) {
                        return true;
                    }

                    // 如果任务已经是进行中的状态，过滤掉处理未超时的任务
                    return task.getMtime().isBefore(timeoutDeadline);
                })
                .collect(Collectors.toList());
    }

    /**
     * 注册任务策略
     *
     * @param taskType 任务类型
     * @param strategy 业务策略
     */
    public void registerStrategy(Integer taskType, PayTaskStrategy strategy) {
        // 如果是AbstractPayTaskStrategy子类，自动注入依赖
        if (strategy instanceof AbstractPayTaskStrategy) {
            AbstractPayTaskStrategy abstractStrategy = (AbstractPayTaskStrategy) strategy;
            abstractStrategy.setDistributedLockService(distributedLockService);
            abstractStrategy.setTaskRepository(taskRepository);
            abstractStrategy.setBizGlobalShareTaskExecutor(bizGlobalShareTaskExecutor);
            abstractStrategy.setTaskProperties(taskProperties);
            abstractStrategy.setRedissonClient(redissonClient);
            abstractStrategy.setAlarmNotify(alarmNotify);
        }

        //注册业务策略
        strategyMap.put(taskType, strategy);

        // 注册业务线程池到监控器
        ThreadPoolTaskExecutor bizThreadPool = strategy.getBizThreadPool();
        if (bizThreadPool != null) {
            String threadNamePrefix = bizThreadPool.getThreadNamePrefix();
            if (threadNamePrefix.isEmpty()) {
                threadNamePrefix = "unknown";
            }
            String threadName = threadNamePrefix.endsWith(CONNECTOR)
                    ? threadNamePrefix.substring(0, threadNamePrefix.length() - 1)
                    : threadNamePrefix;
            taskMonitor.registerThreadPool(BIZ_CUSTOM_THREAD_POOL_NAME_PREFIX + threadName, bizThreadPool);
        }
    }

    /**
     * 根据taskType获取任务策略
     *
     * @param taskType 任务类型
     * @return
     */
    public PayTaskStrategy getStrategy(Integer taskType) {
        return strategyMap.get(taskType);
    }

    /**
     * 执行重试任务
     *
     * @param taskConfigMap 任务配置集合
     */
    public void retryTaskExecute(Map<Integer, PayTaskConfigEntity> taskConfigMap) {
        StopWatch stopWatch = null;
        RLock lock = null;
        try {
            TraceUtil.createTraceId();
            lock = distributedLockService.buildLock(RETRY_TASK_GLOBAL_LOCK);
            if (!distributedLockService.tryLock(lock)) {
                log.info("{}: 分布式锁未抢到, 跳过本次执行", LOG_PREFIX_RETRY);
                return;
            }

            //处理需要重试的任务
            log.info("{}: 重试调度器开始执行", LOG_PREFIX_RETRY);

            stopWatch = new StopWatch();
            stopWatch.start();

            processRetryTasks(taskConfigMap);
        } catch (Throwable e) {
            log.error("{}: 重试调度器执行异常, error={}", LOG_PREFIX_RETRY, e.getMessage(), e);
        } finally {
            if (null != stopWatch && stopWatch.isRunning()) {
                stopWatch.stop();
                log.info("{}: 重试调度器执行完成, 耗时:{}ms [{}s]", LOG_PREFIX_RETRY, stopWatch.getTotalTimeMillis(), stopWatch.getTotalTimeSeconds());
            }
            distributedLockService.unlock(lock);
            TraceUtil.removeAll();
        }
    }

    /**
     * 处理重试任务的核心逻辑
     */
    public void processRetryTasks(Map<Integer, PayTaskConfigEntity> taskConfigMap) {
        try {
            Long lastId = 0L;
            int iterationCount = 0;
            int maxIterations = taskProperties.getMaxIterations();
            int batchSize = taskProperties.getBatchSize();
            while (iterationCount++ < maxIterations) {
                // 查询所有类型的失败任务
                List<PayTaskEntity> tasks = taskRepository.queryTasksByCondition(
                        null, // 不限制taskType
                        null, // 不限制partitionId
                        RETRY_TASK_STATUS_LIST,
                        null,
                        lastId,
                        batchSize
                );

                if (tasks.isEmpty()) {
                    break;
                }

                log.info("{}: 重试任务当前处理批次, batchNo={}, batchSize={}, taskSize={}", LOG_PREFIX_RETRY, iterationCount, batchSize, tasks.size());

                // 按taskType分组处理
                Map<Integer, List<PayTaskEntity>> taskTypeGroupMap = tasks.stream()
                        .collect(Collectors.groupingBy(PayTaskEntity::getTaskType));

                // 处理每个任务组的重试逻辑
                for (Map.Entry<Integer, List<PayTaskEntity>> entry : taskTypeGroupMap.entrySet()) {
                    Integer taskType = entry.getKey();
                    PayTaskStrategy strategy = getStrategy(taskType);
                    if (strategy == null) {
                        log.error("{}: 重试调度任务, 业务策略未注册, taskType={}", LOG_PREFIX_RETRY, taskType);
                        continue;
                    }

                    PayTaskConfigEntity taskConfig = taskConfigMap.get(taskType);
                    if (taskConfig == null) {
                        log.error("{}: 重试调度任务, 未找到任务配置, 无法重试执行该任务, taskType={}", LOG_PREFIX_RETRY, taskType);
                        continue;
                    }

                    int maxRetryTimes = taskConfig.getMaxRetryTimes();
                    List<PayTaskEntity> taskList = entry.getValue();
                    List<PayTaskEntity> needCancelTasks = new ArrayList<>(taskList.size());
                    List<PayTaskEntity> needRetryTasks = new ArrayList<>(taskList.size());
                    // 批量更新任务状态和重试信息
                    taskList.forEach(task -> {
                        if (task.getRetryCnt() >= maxRetryTimes) {
                            //超过最大重试次数，则重置为取消状态
                            task.setStatus(CANCEL.getStatus());
                            needCancelTasks.add(task);
                        } else {
                            // 设置重试次数
                            task.setRetryCnt(task.getRetryCnt() + 1);
                            needRetryTasks.add(task);
                        }
                    });

                    // 批量取消任务
                    if (!needCancelTasks.isEmpty()) {
                        taskRepository.batchUpdateStatusByIds(needCancelTasks);
                    }

                    //批量处理
                    if (!needRetryTasks.isEmpty()) {
                        processTaskBatch(taskConfig, needRetryTasks, strategy, iterationCount);
                    }
                }

                lastId = tasks.get(tasks.size() - 1).getId();

                // 检查是否继续
                if (tasks.size() < batchSize) {
                    break;
                }
            }
        } catch (Exception e) {
            log.error("{}: 重试任务处理异常, error={}", LOG_PREFIX_RETRY, e.getMessage(), e);
        }
    }

    /**
     * 构建任务全局锁
     *
     * @param taskType
     * @param partitionId
     * @return
     */
    public RLock buildGlobalTaskLock(int taskType, int partitionId) {
        return distributedLockService.buildLock(TASK_GLOBAL_LOCK, taskType, partitionId);
    }

    /**
     * 构建任务上次执行时间的key
     *
     * @param taskType
     * @param partitionId
     * @return
     */
    public String buildTaskLastExecTimeKey(int taskType, int partitionId) {
        return distributedLockService.buildKey(TASK_LAST_EXEC_TIME, taskType, partitionId);
    }

    /**
     * 检查是否达到下次执行时间
     *
     * @param taskType         任务类型
     * @param partitionId      分区id
     * @param scheduleInterval 调度间隔(秒)
     * @return true表示到执行时间，false表示未到执行时间
     */
    public boolean checkIfReachNextExecTime(int taskType, int partitionId, int scheduleInterval) {
        try {
            String lastExecKey = buildTaskLastExecTimeKey(taskType, partitionId);
            RBucket<Long> lastExecBucket = redissonClient.getBucket(lastExecKey);
            Long lastExecTime = lastExecBucket.get();
            if (lastExecTime != null) {
                long nextExecTime = lastExecTime + scheduleInterval * 1000L;
                return System.currentTimeMillis() >= nextExecTime;
            }
            return true;
        } catch (Exception e) {
            log.error("{}: 检查任务是否达到下次执行时间, 发生异常, taskType={}, partitionId={}, error={}", LOG_PREFIX, taskType, partitionId, e.getMessage(), e);
            return true;
        }
    }

    /**
     * 记录任务本次执行时间
     *
     * @param taskType         任务类型
     * @param partitionId      分区id
     * @param scheduleInterval 调度间隔(秒)
     */
    private void recordTaskLastExecTime(int taskType, int partitionId, int scheduleInterval) {
        try {
            String lastExecKey = buildTaskLastExecTimeKey(taskType, partitionId);
            RBucket<Long> lastExecBucket = redissonClient.getBucket(lastExecKey);
            lastExecBucket.set(System.currentTimeMillis(), scheduleInterval, TimeUnit.SECONDS);
        } catch (Exception e) {
            log.error("{}: 记录任务本次执行时间, 发生异常, taskType={}, partitionId={}, error={}",
                    LOG_PREFIX, taskType, partitionId, e.getMessage(), e);
        }
    }

    /**
     * 删除任务本次执行时间
     *
     * @param taskType    任务类型
     * @param partitionId 分区id
     */
    private void deleteTaskLastExecTime(int taskType, int partitionId) {
        try {
            String lastExecKey = buildTaskLastExecTimeKey(taskType, partitionId);
            redissonClient.getBucket(lastExecKey).delete();
        } catch (Exception e) {
            log.error("{}: 移除任务本次执行时间, 发生异常, taskType={}, partitionId={}, error={}",
                    LOG_PREFIX, taskType, partitionId, e.getMessage(), e);
        }
    }
}