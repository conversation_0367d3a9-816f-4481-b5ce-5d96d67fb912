package com.wosai.pay.common.task.exception;

import com.wosai.pay.common.base.exception.BaseException;
import com.wosai.pay.common.task.enums.PayTaskErrorTypeEnum;

/**
 * <AUTHOR>
 * @description 通用任务SDK, 基础异常类
 * @date 2025-06-19
 */
public class TaskBaseException extends BaseException {
    private static final long serialVersionUID = -2508265953654744495L;

    private final PayTaskErrorTypeEnum errorType;

    public TaskBaseException(PayTaskErrorTypeEnum errorType, String message) {
        super(message);
        this.errorType = errorType;
    }

    public TaskBaseException(PayTaskErrorTypeEnum errorType, String message, Throwable cause) {
        super(message, cause);
        this.errorType = errorType;
    }

    public PayTaskErrorTypeEnum getErrorType() {
        return this.errorType;
    }
}