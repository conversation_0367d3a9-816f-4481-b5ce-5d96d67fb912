package com.wosai.pay.common.task.repository.mapper;

import com.wosai.pay.common.task.repository.entity.PayTaskConfigEntity;

import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2025-06-17
 */
public interface PayTaskConfigMapper {

    /**
     * 创建任务配置
     *
     * @param config 任务配置实体
     * @return 创建的记录ID
     */
    long insert(PayTaskConfigEntity config);

    /**
     * 根据ID选择性更新任务配置
     *
     * @param config 任务配置实体
     * @return 更新影响的行数
     */
    int updateSelectiveById(PayTaskConfigEntity config);

    /**
     * 获取所有启用的任务配置列表
     *
     * @return
     */
    List<PayTaskConfigEntity> getActiveTaskConfigs();

    /**
     * 根据任务类型获取任务配置
     *
     * @param taskType
     * @return
     */
    PayTaskConfigEntity getByTaskType(Integer taskType);

    /**
     * 根据ID获取任务配置
     *
     * @param id 任务配置ID
     * @return 任务配置实体
     */
    PayTaskConfigEntity getById(Long id);
}