package com.wosai.pay.common.task.exception;

import com.wosai.pay.common.task.enums.PayTaskErrorTypeEnum;

/**
 * <AUTHOR>
 * @description DB异常
 * @date 2025-06-19
 */
public class TaskDBException extends TaskBaseException {
    private static final long serialVersionUID = 7677402686760541936L;

    public TaskDBException(String message) {
        super(PayTaskErrorTypeEnum.RETRY_DB, message);
    }

    public TaskDBException(String message, Throwable cause) {
        super(PayTaskErrorTypeEnum.RETRY_DB, message, cause);
    }
}