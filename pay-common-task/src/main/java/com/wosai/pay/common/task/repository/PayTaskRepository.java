package com.wosai.pay.common.task.repository;

import com.wosai.pay.common.task.enums.PayTaskStatusEnum;
import com.wosai.pay.common.task.repository.dto.PayTaskCreateDTO;
import com.wosai.pay.common.task.repository.entity.PayTaskEntity;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @description 任务实例表，持久层接口
 * @date 2025-06-19
 */
public interface PayTaskRepository {
    /**
     * 创建任务实例
     *
     * @param taskDTO 任务实例实体
     * @return 创建记录的主键ID
     */
    Long create(PayTaskCreateDTO taskDTO);

    /**
     * 批量创建任务实例（不带事务，允许部分插入成功、部分失败）
     *
     * @param taskDTOs 任务实例DTO列表
     * @return 创建记录的主键ID列表
     */
    int batchCreate(List<PayTaskCreateDTO> taskDTOs);

    /**
     * 批量创建任务实例（带事务，使用默认事务）
     *
     * @param taskDTOs 任务实例DTO列表
     * @return 创建记录的主键ID列表
     */
    int batchCreateWithTransaction(List<PayTaskCreateDTO> taskDTOs);

    /**
     * 批量更新任务实例状态
     *
     * @param tasks 任务实例列表
     * @return 更新影响的行数
     */
    int batchUpdateStatusByIds(List<PayTaskEntity> tasks);

    /**
     * 根据ID选择性更新任务实例
     *
     * @param task 任务实例实体
     * @return 更新影响的行数
     */
    int updateSelectiveById(PayTaskEntity task);

    /**
     * 根据条件查询任务实例列表
     *
     * @param taskType    任务类型
     * @param partitionId 分区ID
     * @param statusList  任务状态
     * @param planTime    计划时间
     * @param lastId      最后处理的ID
     * @param batchSize   批量大小
     * @return 任务实例列表
     */
    List<PayTaskEntity> queryTasksByCondition(Integer taskType,
                                              Integer partitionId,
                                              List<PayTaskStatusEnum> statusList,
                                              LocalDateTime planTime,
                                              Long lastId,
                                              int batchSize);

    /**
     * 根据幂等ID查询任务实例
     *
     * @param idempotentId 幂等ID
     * @return 任务实例实体
     */
    PayTaskEntity queryByIdempotentId(String idempotentId);

    /**
     * 根据幂等ID列表查询存在的幂等ID
     *
     * @param idempotentIds 幂等ID列表
     * @return 已存在的幂等ID列表
     */
    List<String> queryExistingIdempotentIds(List<String> idempotentIds);
}