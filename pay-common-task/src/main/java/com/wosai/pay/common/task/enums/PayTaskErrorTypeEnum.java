package com.wosai.pay.common.task.enums;

/**
 * <AUTHOR>
 * @description 错误类型
 * @date 2025-06-19
 */
public enum PayTaskErrorTypeEnum {
    /**
     * 需重试的错误类型
     */
    RETRY_SYSTEM("系统异常"),
    RETRY_DB("DB访问异常"),
    RETRY_REMOTE("远程服务异常"),
    RETRY_BIZ("业务异常"),
    /**
     * 无须重试的错误类型
     */
    SKIP_BIZ("业务异常, 无须重试");

    private final String desc;

    PayTaskErrorTypeEnum(String desc) {
        this.desc = desc;
    }

    public String getDesc() {
        return this.desc;
    }
}
