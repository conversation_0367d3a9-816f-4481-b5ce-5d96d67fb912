package com.wosai.pay.common.task.util;

import org.slf4j.MDC;

import java.util.UUID;

/**
 * <AUTHOR>
 * @description
 * @date 2024/5/29
 */
public class TraceUtil {
    public static final String UID = "uid";
    private static String traceIdKey = "trace_id";

    /**
     * 重置TraceIdKey
     */
    public static synchronized void resetTraceIdKey(String key) {
        if (key != null && !key.isEmpty()) {
            TraceUtil.traceIdKey = key;
        }
    }

    public static String getTraceIdKey() {
        return traceIdKey;
    }

    /**
     * 查询traceId
     */
    public static String getTraceId() {
        return MDC.get(getTraceIdKey());
    }

    /**
     * 创建traceId
     */
    public static void createTraceId() {
        MDC.put(getTraceIdKey(), generatorUUID());
    }

    /**
     * 创建traceId
     */
    public static void createTraceId(String uid) {
        createTraceId();
        recordUid(uid);
    }

    /**
     * 记录uid
     */
    public static void recordUid(String uid) {
        if (uid != null && !uid.isEmpty()) {
            MDC.put(UID, uid);
        }
    }

    /**
     * 清除链路id
     */
    public static void removeAll() {
        removeTraceId();
        removeUid();
    }

    /**
     * 移除链路id
     */
    public static void removeTraceId() {
        MDC.remove(getTraceIdKey());
    }

    /**
     * 移除uid
     */
    public static void removeUid() {
        MDC.remove(UID);
    }

    /**
     * 生成唯一id
     *
     * @return
     */
    public static String generatorUUID() {
        try {
            return UUID.randomUUID().toString().replace("-", "");
        } catch (Exception ignored) {
        }
        return "";
    }
}
