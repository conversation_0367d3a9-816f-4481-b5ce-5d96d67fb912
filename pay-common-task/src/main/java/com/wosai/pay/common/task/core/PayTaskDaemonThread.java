package com.wosai.pay.common.task.core;

import com.wosai.pay.common.task.autoconfigure.PayTaskProperties;
import com.wosai.pay.common.task.repository.PayTaskConfigRepository;
import com.wosai.pay.common.task.repository.entity.PayTaskConfigEntity;
import org.redisson.api.RLock;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.time.LocalTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.Executors;
import java.util.concurrent.RejectedExecutionException;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static com.wosai.pay.common.task.constant.PayTaskConstant.*;

/**
 * <AUTHOR>
 * @description 通用任务守护线程
 * @date 2025-06-18
 */
public class PayTaskDaemonThread {
    private static final String LOG_PREFIX = "[Schedule]";
    private static final Logger log = LoggerFactory.getLogger(PayTaskDaemonThread.class);
    private static final Pattern JSON_CHAR_PATTERN = Pattern.compile("[\\[\\]\"]");

    // 线程池监控器
    private final PayTaskMonitor taskMonitor;
    private final PayTaskProperties taskProperties;
    private final PayTaskConfigRepository taskConfigRepository;
    private final PayTaskExecuteTemplate taskExecuteTemplate;
    private final ThreadPoolTaskExecutor normalTaskExecutor;

    private ScheduledExecutorService normalTaskScheduler;
    private ScheduledExecutorService retryTaskScheduler;

    public PayTaskDaemonThread(PayTaskProperties taskProperties,
                               PayTaskConfigRepository taskConfigRepository,
                               PayTaskExecuteTemplate taskExecuteTemplate,
                               ThreadPoolTaskExecutor normalTaskExecutor,
                               PayTaskMonitor taskMonitor) {
        this.taskProperties = taskProperties;
        this.taskConfigRepository = taskConfigRepository;
        this.taskExecuteTemplate = taskExecuteTemplate;
        this.normalTaskExecutor = normalTaskExecutor;
        this.taskMonitor = taskMonitor;
    }

    public void start() {
        if (!taskProperties.checkIsEnable()) {
            log.info("{}: 调度模块未启用, 不再处理通用任务", LOG_PREFIX);
            return;
        }

        log.info("{}: 开始启动守护线程...", LOG_PREFIX);

        // 初始化监控器
        taskMonitor.registerThreadPool(DAEMON_TASK_THREAD_POOL_NAME, normalTaskExecutor);
        taskMonitor.start();

        // 初始化调度器
        initTaskScheduler();

        log.info("{}: 启动守护线程成功, 普通任务扫描周期:{}秒, 重试任务扫描周期:{}秒",
                LOG_PREFIX, taskProperties.getNormalScanInterval(), taskProperties.getRetryScanInterval());
    }

    /**
     * 扫描普通任务
     */
    private void scanAndDispatchNormalTasks() {
        try {
            // 1. 获取所有启用的任务配置
            List<PayTaskConfigEntity> activeConfigs = taskConfigRepository.getActiveTaskConfigs();

            // 2. 整理分区任务列表
            List<Runnable> partitionTasks = new ArrayList<>();
            for (PayTaskConfigEntity config : activeConfigs) {
                int partitionCnt = config.getPartitionCnt() != null ? config.getPartitionCnt() : 1;

                //将任务根据partitionCnt拆分成多个子任务
                for (int partitionId = 0; partitionId < partitionCnt; partitionId++) {
                    final int finalPartitionId = partitionId;
                    //校验任务是否可以执行
                    if (!validateBeforeSubmit(config, finalPartitionId)) {
                        continue;
                    }

                    partitionTasks.add(() -> taskExecuteTemplate.execute(config, finalPartitionId));
                }
            }

            // 3. 批量提交分区任务
            partitionTasks.forEach(task -> {
                try {
                    normalTaskExecutor.submit(task);
                } catch (RejectedExecutionException e) {
                    log.error("{}: 线程池已满，提交普通任务失败, threadPoolName={}", LOG_PREFIX, normalTaskExecutor.getThreadNamePrefix(), e);
                } catch (Exception e) {
                    log.error("{}: 提交普通任务到线程池失败, threadPoolName={}", LOG_PREFIX, normalTaskExecutor.getThreadNamePrefix(), e);
                }
            });
        } catch (Throwable e) {
            log.error("{}: 扫描已启用的任务列表异常, error={}", LOG_PREFIX, e.getMessage(), e);
        }
    }

    /**
     * 扫描重试任务
     */
    private void retryExecuteTasks() {
        try {
            //加载所有任务配置
            List<PayTaskConfigEntity> activeConfigs = taskConfigRepository.getActiveTaskConfigs();
            Map<Integer, PayTaskConfigEntity> taskConfigMap = activeConfigs.stream()
                    .collect(Collectors.toMap(PayTaskConfigEntity::getTaskType, Function.identity()));

            //处理重试任务
            taskExecuteTemplate.retryTaskExecute(taskConfigMap);
        } catch (Throwable e) {
            log.error("{}: 处理重试任务异常, error={}", LOG_PREFIX, e.getMessage(), e);
        }
    }

    /**
     * 任务提交前的前置校验
     *
     * @param config      任务配置
     * @param partitionId 分区ID
     * @return 校验是否通过
     */
    private boolean validateBeforeSubmit(PayTaskConfigEntity config, int partitionId) {
        // 时间范围校验
        if (!validateTimeRange(config)) {
            return false;
        }

        // 检查是否达到下次调度时间
        if (!taskExecuteTemplate.checkIfReachNextExecTime(config.getTaskType(), partitionId, config.getScheduleInterval())) {
            return false;
        }

        // 检查是否已经存在进行中的任务
        return !checkIfTaskIsRunning(config, partitionId);
    }

    /**
     * 校验当前时间是否在允许的时间范围内
     */
    private boolean validateTimeRange(PayTaskConfigEntity config) {
        String schedulePeriod = config.getSchedulePeriod();
        if (schedulePeriod == null || schedulePeriod.isEmpty()) {
            log.warn("{}: schedulePeriod未配置, taskType={}", LOG_PREFIX, config.getTaskType());
            return true; // 未配置时间范围则默认通过
        }

        try {
            // 使用预编译正则移除JSON格式字符
            String cleanSchedule = JSON_CHAR_PATTERN.matcher(schedulePeriod).replaceAll("");
            String[] timeRanges = cleanSchedule.split(",");

            LocalTime currentTime = LocalTime.now();
            for (String timeRange : timeRanges) {
                String[] parts = timeRange.split("-");
                if (parts.length != 2) {
                    log.error("{}: 调度时间范围配置不正确, taskType={}, timeRange={}", LOG_PREFIX, config.getTaskType(), timeRange);
                    continue;
                }

                LocalTime startTime = LocalTime.parse(parts[0].trim());
                LocalTime endTime = LocalTime.parse(parts[1].trim());

                if (startTime.isBefore(endTime)) {
                    // 同一天时间段
                    if (!currentTime.isBefore(startTime) && !currentTime.isAfter(endTime)) {
                        return true;
                    }
                } else {
                    // 跨天时间段：当前时间在 [startTime, 23:59] 或 [00:00, endTime]
                    if (currentTime.isAfter(startTime) || currentTime.isBefore(endTime)) {
                        return true;
                    }
                }
            }
            return false;
        } catch (Exception e) {
            log.error("{}: schedulePeriod解析失败 taskType={}, schedulePeriod={}",
                    LOG_PREFIX, config.getTaskType(), schedulePeriod, e);
            return false;
        }
    }

    /**
     * 分布式锁校验
     */
    private boolean checkIfTaskIsRunning(PayTaskConfigEntity config, int partitionId) {
        RLock lock = taskExecuteTemplate.buildGlobalTaskLock(config.getTaskType(), partitionId);
        try {
            // 检查锁是否已经被持有。如果已经被持有，说明目前该任务已经在执行中
            if (lock.isLocked()) {
                log.info("{}: 已经存在执行中的任务, 跳过, taskType={}, partitionId={}", LOG_PREFIX, config.getTaskType(), partitionId);
                return true;
            }
            return false;
        } catch (Exception e) {
            log.error("{}: 分布式锁操作异常 taskType={}, partitionId={}",
                    LOG_PREFIX, config.getTaskType(), partitionId, e);
            return true; // 异常时默认不通过
        }
    }

    /**
     * 初始化任务调度器
     */
    private void initTaskScheduler() {
        //普通任务调度器
        int normalTaskScanInterval = taskProperties.getNormalScanInterval();
        normalTaskScheduler = Executors.newSingleThreadScheduledExecutor(r -> {
            Thread t = new Thread(r);
            t.setName(DAEMON_NORMAL_TASK_SCHEDULE_NAME + CONNECTOR + t.getId());
            // 设置异常处理器
            t.setUncaughtExceptionHandler((thread, e) ->
                    log.error("{}: 普通任务调度器发生异常", LOG_PREFIX, e)
            );
            return t;
        });
        normalTaskScheduler.scheduleAtFixedRate(this::scanAndDispatchNormalTasks,
                0, normalTaskScanInterval, TimeUnit.SECONDS);

        //重试任务调度器
        int retryTaskScanInterval = taskProperties.getRetryScanInterval();
        retryTaskScheduler = Executors.newSingleThreadScheduledExecutor(r -> {
            Thread t = new Thread(r);
            t.setName(DAEMON_RETRY_TASK_SCHEDULE_NAME + CONNECTOR + t.getId());
            // 设置异常处理器
            t.setUncaughtExceptionHandler((thread, e) ->
                    log.error("{}: 重试任务调度器发生异常", LOG_PREFIX, e)
            );
            return t;
        });
        retryTaskScheduler.scheduleAtFixedRate(this::retryExecuteTasks,
                0, retryTaskScanInterval, TimeUnit.SECONDS);
    }

    /**
     * 守护线程重新初始化
     */
    public void reload() {
        log.info("{}: 守护线程重新初始化...", LOG_PREFIX);

        // 停止当前调度器
        if (normalTaskScheduler != null && !normalTaskScheduler.isShutdown()) {
            log.info("{}: 关闭普通任务调度器", LOG_PREFIX);
            normalTaskScheduler.shutdown();
        }
        if (retryTaskScheduler != null && !retryTaskScheduler.isShutdown()) {
            log.info("{}: 关闭重试任务调度器", LOG_PREFIX);
            retryTaskScheduler.shutdown();
        }

        // 更新线程池配置
        if (normalTaskExecutor != null) {
            normalTaskExecutor.setCorePoolSize(taskProperties.getTaskConfigThreadPoolCoreSize());
            normalTaskExecutor.setMaxPoolSize(taskProperties.getTaskConfigThreadPoolMaxSize());
            log.info("{}: 处理任务配置列表的线程池更新, coreSize={}, maxSize={}",
                    LOG_PREFIX, taskProperties.getTaskConfigThreadPoolCoreSize(), taskProperties.getTaskConfigThreadPoolMaxSize());
        }

        // 重新启动调度器
        if (taskProperties.checkIsEnable()) {
            initTaskScheduler();
            log.info("{}: 守护线程重新加载完成, 普通任务扫描周期:{}秒, 重试任务扫描周期:{}秒",
                    LOG_PREFIX, taskProperties.getNormalScanInterval(), taskProperties.getRetryScanInterval());
        } else {
            log.info("{}: 调度模块关闭, 不再处理通用任务", LOG_PREFIX);
        }

        //重新加载监控
        taskMonitor.reload();

        log.info("{}: 守护线程重新初始化完成", LOG_PREFIX);
    }

    /**
     * 应用程序退出，释放资源
     */
    public void stop() {
        log.info("{}: 应用程序退出, 开始关闭所有任务资源...", LOG_PREFIX);

        // 关闭调度器
        if (normalTaskScheduler != null && !normalTaskScheduler.isShutdown()) {
            normalTaskScheduler.shutdown();
            log.info("{}: 普通任务调度器已关闭", LOG_PREFIX);
        }

        if (retryTaskScheduler != null && !retryTaskScheduler.isShutdown()) {
            retryTaskScheduler.shutdown();
            log.info("{}: 重试任务调度器已关闭", LOG_PREFIX);
        }

        // 关闭线程池
        if (normalTaskExecutor != null) {
            normalTaskExecutor.getThreadPoolExecutor().shutdown();
            try {
                if (!normalTaskExecutor.getThreadPoolExecutor().awaitTermination(30, TimeUnit.SECONDS)) {
                    normalTaskExecutor.getThreadPoolExecutor().shutdownNow();
                }
            } catch (InterruptedException e) {
                normalTaskExecutor.getThreadPoolExecutor().shutdownNow();
                Thread.currentThread().interrupt();// 恢复中断状态
            }
            log.info("{}: 普通任务线程池已关闭", LOG_PREFIX);
        }

        // 停止监控器
        taskMonitor.stop();

        log.warn("{}: 所有任务资源已释放完成", LOG_PREFIX);
    }
}