package com.wosai.pay.common.task;

import com.wosai.pay.common.task.repository.entity.PayTaskConfigEntity;
import com.wosai.pay.common.task.repository.entity.PayTaskEntity;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

/**
 * 任务策略接口
 */
public interface PayTaskStrategy {

    /**
     * 获取任务类型
     *
     * @return 任务类型编码
     */
    int getPayTaskType();

    /**
     * 执行任务
     *
     * @param taskConfig 任务配置
     * @param task       任务实例
     */
    void execute(PayTaskConfigEntity taskConfig, PayTaskEntity task);

    /**
     * 构建具体业务处理的分布式锁key，防止并发处理
     *
     * @param task
     * @return 分布式锁key
     */
    String buildBizLockKey(PayTaskEntity task);

    /**
     * 获取处理具体业务的线程池
     *
     * @return 业务线程池
     */
    ThreadPoolTaskExecutor getBizThreadPool();

    /**
     * 是否自动更新任务的完成状态
     *
     * @return true表示需要更新任务完成状态，false表示保持原状态，由业务方自行处理
     */
    default boolean shouldUpdateFinishStatus() {
        return true;
    }
}