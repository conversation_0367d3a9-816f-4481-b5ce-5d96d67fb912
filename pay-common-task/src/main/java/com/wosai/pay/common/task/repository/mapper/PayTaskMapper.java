package com.wosai.pay.common.task.repository.mapper;

import com.wosai.pay.common.task.enums.PayTaskStatusEnum;
import com.wosai.pay.common.task.repository.entity.PayTaskEntity;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2025-06-17
 */
public interface PayTaskMapper {
    /**
     * 插入任务实例
     *
     * @param task 任务实例实体
     * @return 插入记录的主键ID
     */
    Long insert(PayTaskEntity task);

    /**
     * 批量插入任务实例
     *
     * @param tasks 任务实例实体列表
     * @return 插入记录的主键ID列表
     */
    int batchInsert(List<PayTaskEntity> tasks);

    /**
     * 根据ID选择性更新任务实例
     *
     * @param task 任务实例实体
     * @return 更新影响的行数
     */
    int updateSelectiveById(PayTaskEntity task);

    /**
     * 批量更新任务实例状态
     *
     * @param tasks 任务实例列表
     * @return 更新影响的行数
     */
    int batchUpdateStatusByIds(List<PayTaskEntity> tasks);

    /**
     * 根据条件查询任务实例列表
     *
     * @param taskType    任务类型
     * @param partitionId 分区ID
     * @param statusList  任务状态
     * @param planTime    计划时间
     * @param lastId      最后处理的ID
     * @param batchSize   批量大小
     * @return 任务实例列表
     */
    List<PayTaskEntity> queryTasksByCondition(Integer taskType,
                                              Integer partitionId,
                                              List<PayTaskStatusEnum> statusList,
                                              LocalDateTime planTime,
                                              Long lastId,
                                              int batchSize);

    /**
     * 根据幂等ID查询任务实例
     *
     * @param idempotentId 幂等ID
     * @return 任务实例实体
     */
    PayTaskEntity queryByIdempotentId(String idempotentId);

    /**
     * 根据幂等ID列表查询已存在的幂等ID
     *
     * @param idempotentIds 幂等ID列表
     * @return 已存在的幂等ID列表
     */
    List<String> queryExistingIdempotentIds(List<String> idempotentIds);
}