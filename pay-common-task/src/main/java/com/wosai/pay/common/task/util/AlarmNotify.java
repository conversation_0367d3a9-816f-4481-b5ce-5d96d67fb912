package com.wosai.pay.common.task.util;

import com.wosai.pay.common.task.autoconfigure.PayTaskProperties;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.Closeable;
import java.io.IOException;
import java.io.OutputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @description 告警通知工具类
 * @date 2025-06-27
 */
public class AlarmNotify implements Closeable {
    private static final String LOG_PREFIX = "[Alarm]";
    private static final Logger log = LoggerFactory.getLogger(AlarmNotify.class);
    private static final String JSON = "application/json; charset=utf-8";
    private static final int CONNECT_TIMEOUT = 3000;
    private static final int READ_TIMEOUT = 5000;
    
    /**
     * 发送HTTP POST请求
     * @param url 请求URL
     * @param json 请求体JSON字符串
     * @return 是否请求成功
     */
    private boolean sendHttpRequest(String url, String json) {
        HttpURLConnection connection = null;
        try {
            URL requestUrl = new URL(url);
            connection = (HttpURLConnection) requestUrl.openConnection();
            connection.setRequestMethod("POST");
            connection.setRequestProperty("Content-Type", JSON);
            connection.setDoOutput(true);
            connection.setConnectTimeout(CONNECT_TIMEOUT);
            connection.setReadTimeout(READ_TIMEOUT);

            try (OutputStream os = connection.getOutputStream()) {
                os.write(json.getBytes());
                os.flush();
            }

            int responseCode = connection.getResponseCode();
            return responseCode >= 200 && responseCode < 300;
        } catch (IOException e) {
            log.error("{}: HTTP请求异常, url={}", LOG_PREFIX, url, e);
            return false;
        } finally {
            if (connection != null) {
                connection.disconnect();
            }
        }
    }
    private final PayTaskProperties taskProperties;

    public AlarmNotify(PayTaskProperties taskProperties) {
        this.taskProperties = taskProperties;
    }

    /**
     * 发送文本消息
     *
     * @param title   消息标题
     * @param content 消息内容
     */
    public void sendTextMessage(String title, String content) {
        String alarmNotifyUrl = taskProperties.getAlarmNotifyUrl();
        if (alarmNotifyUrl == null || alarmNotifyUrl.isEmpty()) {
            // 如果未配置告警url，则不发送告警
            log.info("{}: 未配置告警url, 不发送告警消息, content={}", LOG_PREFIX, content);
            return;
        }

        Map<String, Object> msgMap = new HashMap<>();
        msgMap.put("msg_type", "text");
        Map<String, String> contentMap = new HashMap<>();
        contentMap.put("text", title + "\n" + content);
        msgMap.put("content", contentMap);

        String json = TaskJsonUtil.encode(msgMap);
        boolean success = sendHttpRequest(taskProperties.getAlarmNotifyUrl(), Objects.requireNonNull(json));
        if (success) {
            log.info("{}: 告警消息发送成功: \n{}", LOG_PREFIX, content);
        } else {
            log.error("{}: 告警消息发送失败, url={}, content={}",
                    LOG_PREFIX, taskProperties.getAlarmNotifyUrl(), content);
        }
    }

    /**
     * 发送告警消息
     *
     * @param alertType 告警类型
     * @param params    告警参数map
     */
    public void sendAlert(String alertType, Map<String, Object> params) {
        try {
            StringBuilder messageBuilder = new StringBuilder();
            messageBuilder.append("应用：").append(taskProperties.getGlobalLockPrefix()).append("\n");
            messageBuilder.append("机器名：").append(System.getenv("HOSTNAME")).append("\n");

            for (Map.Entry<String, Object> entry : params.entrySet()) {
                messageBuilder.append(entry.getKey())
                    .append("：")
                    .append(entry.getValue())
                    .append("\n");
            }

            String title = "【通用任务SDK】" + alertType;
            sendTextMessage(title, messageBuilder.toString());
        } catch (Exception e) {
            log.error("{}: 告警发送失败, params={}", LOG_PREFIX, params, e);
        }
    }

    @Override
    public void close() {
        // HttpURLConnection在每次请求后已关闭，无需额外操作
    }
}