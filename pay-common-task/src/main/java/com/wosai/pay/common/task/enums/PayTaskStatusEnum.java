package com.wosai.pay.common.task.enums;

/**
 * <AUTHOR>
 * @description 任务实例的执行状态
 * @date 2025-06-19
 */
public enum PayTaskStatusEnum {
    PENDING(0, "待执行"),
    IN_PROGRESS(1, "执行中"),
    SUCCESS(2, "执行成功"),
    FAILURE(3, "执行失败"),
    CANCEL(4, "取消");

    private final int status;
    private final String remark;

    PayTaskStatusEnum(int status, String remark) {
        this.status = status;
        this.remark = remark;
    }

    public static PayTaskStatusEnum of(Integer status) {
        if (status == null) {
            return PENDING;
        }
        for (PayTaskStatusEnum e : PayTaskStatusEnum.values()) {
            if (status.equals(e.status)) {
                return e;
            }
        }
        return PENDING;
    }

    public int getStatus() {
        return this.status;
    }

    public String getRemark() {
        return this.remark;
    }
}
