package com.wosai.pay.common.task.enums;

import java.util.Objects;

/**
 * <AUTHOR>
 * @description 通用任务配置的状态
 * @date 2025-06-19
 */
public enum PayTaskConfigStatusEnum {
    DISABLE(0, "禁用"),
    ENABLE(1, "启用");

    private final int status;
    private final String desc;

    PayTaskConfigStatusEnum(int status, String desc) {
        this.status = status;
        this.desc = desc;
    }

    public static PayTaskConfigStatusEnum of(Integer status) {
        if (Objects.isNull(status)) {
            return DISABLE;
        }
        for (PayTaskConfigStatusEnum statusEnum : PayTaskConfigStatusEnum.values()) {
            if (statusEnum.getStatus() == status) {
                return statusEnum;
            }
        }
        return DISABLE;
    }

    public int getStatus() {
        return this.status;
    }

    public String getDesc() {
        return this.desc;
    }
}
