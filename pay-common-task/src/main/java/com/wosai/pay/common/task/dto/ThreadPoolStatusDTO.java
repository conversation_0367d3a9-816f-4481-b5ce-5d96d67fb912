package com.wosai.pay.common.task.dto;

/**
 * 线程池状态DTO
 */
public class ThreadPoolStatusDTO {
    /**
     * 线程池名称
     */
    private String poolName;

    /**
     * 核心线程数
     */
    private int coreSize;

    /**
     * 最大线程数
     */
    private int maxSize;

    /**
     * 当前线程数
     */
    private int currentPoolSize;

    /**
     * 活跃线程数
     */
    private int activeCount;

    /**
     * 队列容量
     */
    private int queueCapacity;

    /**
     * 当前队列大小
     */
    private int currentQueueSize;

    /**
     * 已完成任务数
     */
    private long completedTaskCount;

    /**
     * 已提交任务数
     */
    private long totalTaskCount;

    /**
     * 历史最大线程数
     */
    private int largestPoolSize;

    public String getPoolName() {
        return poolName;
    }

    public void setPoolName(String poolName) {
        this.poolName = poolName;
    }

    public int getCoreSize() {
        return coreSize;
    }

    public void setCoreSize(int coreSize) {
        this.coreSize = coreSize;
    }

    public int getMaxSize() {
        return maxSize;
    }

    public void setMaxSize(int maxSize) {
        this.maxSize = maxSize;
    }

    public int getCurrentPoolSize() {
        return currentPoolSize;
    }

    public void setCurrentPoolSize(int currentPoolSize) {
        this.currentPoolSize = currentPoolSize;
    }

    public int getActiveCount() {
        return activeCount;
    }

    public void setActiveCount(int activeCount) {
        this.activeCount = activeCount;
    }

    public int getQueueCapacity() {
        return queueCapacity;
    }

    public void setQueueCapacity(int queueCapacity) {
        this.queueCapacity = queueCapacity;
    }

    public int getCurrentQueueSize() {
        return currentQueueSize;
    }

    public void setCurrentQueueSize(int currentQueueSize) {
        this.currentQueueSize = currentQueueSize;
    }

    public long getCompletedTaskCount() {
        return completedTaskCount;
    }

    public void setCompletedTaskCount(long completedTaskCount) {
        this.completedTaskCount = completedTaskCount;
    }

    public long getTotalTaskCount() {
        return totalTaskCount;
    }

    public void setTotalTaskCount(long totalTaskCount) {
        this.totalTaskCount = totalTaskCount;
    }

    public int getLargestPoolSize() {
        return largestPoolSize;
    }

    public void setLargestPoolSize(int largestPoolSize) {
        this.largestPoolSize = largestPoolSize;
    }
}