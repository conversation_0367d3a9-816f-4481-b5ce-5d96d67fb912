package com.wosai.pay.common.task.service;

import com.wosai.pay.common.task.util.TraceUtil;
import com.wosai.pay.common.task.autoconfigure.PayTaskProperties;
import com.wosai.pay.common.task.core.PayTaskMonitor;
import com.wosai.pay.common.task.core.PayTaskPropertiesDistributor;
import com.wosai.pay.common.task.dto.ThreadPoolConfigDTO;
import com.wosai.pay.common.task.dto.ThreadPoolStatusDTO;
import com.wosai.pay.common.task.exception.TaskSystemException;
import com.wosai.pay.common.task.util.TaskJsonUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description 任务服务类
 * @date 2025-06-19
 */
public class PayTaskServiceImpl implements PayTaskService {
    private static final String LOG_PREFIX = "[TaskService]";
    private static final Logger log = LoggerFactory.getLogger(PayTaskServiceImpl.class);

    private final PayTaskProperties taskProperties;
    private final PayTaskMonitor payTaskMonitor;
    private final PayTaskPropertiesDistributor taskPropertiesDistributor;

    public PayTaskServiceImpl(PayTaskProperties taskProperties,
                              PayTaskMonitor payTaskMonitor,
                              PayTaskPropertiesDistributor taskPropertiesDistributor) {
        this.taskProperties = taskProperties;
        this.payTaskMonitor = payTaskMonitor;
        this.taskPropertiesDistributor = taskPropertiesDistributor;
    }

    @Override
    public PayTaskProperties queryGlobalTaskConfig() {
        PayTaskProperties returnProperties = new PayTaskProperties();
        BeanUtils.copyProperties(taskProperties, returnProperties);
        return returnProperties;
    }

    @Override
    public synchronized String reload(PayTaskProperties newProperties) {
        try {
            TraceUtil.createTraceId();
            log.info("{}: 重新加载任务组件, 入参={}", LOG_PREFIX, TaskJsonUtil.encode(newProperties));
            // 发布配置更新事件
            taskPropertiesDistributor.publishTaskPropertiesUpdate(newProperties);
        } catch (Exception e) {
            log.error("{}: 重新加载任务组件失败", LOG_PREFIX, e);
            return "重新加载任务组件失败：" + e.getMessage();
        } finally {
            TraceUtil.removeAll();
        }

        return "success";
    }

    @Override
    public List<ThreadPoolStatusDTO> queryAllThreadPoolRunningInfo() {
        List<ThreadPoolStatusDTO> result = new ArrayList<>();

        try {
            Map<String, ThreadPoolTaskExecutor> threadPools = new HashMap<>(payTaskMonitor.getThreadPools());
            threadPools.forEach((poolName, executor) -> {
                ThreadPoolStatusDTO status = new ThreadPoolStatusDTO();
                status.setPoolName(poolName);
                status.setCoreSize(executor.getCorePoolSize());
                status.setMaxSize(executor.getMaxPoolSize());
                status.setCurrentPoolSize(executor.getPoolSize());
                status.setActiveCount(executor.getActiveCount());
                int queueSize = executor.getThreadPoolExecutor().getQueue().size();
                int queueCapacity = executor.getThreadPoolExecutor().getQueue().remainingCapacity() + queueSize;
                status.setQueueCapacity(queueCapacity);
                status.setCurrentQueueSize(queueSize);
                status.setCompletedTaskCount(executor.getThreadPoolExecutor().getCompletedTaskCount());
                status.setTotalTaskCount(executor.getThreadPoolExecutor().getTaskCount());
                status.setLargestPoolSize(executor.getThreadPoolExecutor().getLargestPoolSize());
                result.add(status);
            });

            log.info("{}: 查询线程池状态成功，共{}个线程池", LOG_PREFIX, result.size());
        } catch (Exception e) {
            log.error("{}: 查询线程池状态失败", LOG_PREFIX, e);
            throw new TaskSystemException("查询线程池状态失败", e);
        }

        return result;
    }

    @Override
    public synchronized String updateThreadPoolConfig(ThreadPoolConfigDTO config) {
        try {
            TraceUtil.createTraceId();
            log.info("{}: 尝试修改线程池 {} 配置, coreSize={}, maxSize={}",
                    LOG_PREFIX, config.getPoolName(), config.getCoreSize(), config.getMaxSize());
            return payTaskMonitor.updateThreadPoolConfig(config);
        } catch (Exception e) {
            log.error("{}: 修改线程池 {} 配置失败", LOG_PREFIX, config.getPoolName(), e);
            return "修改线程池配置失败, error=" + e.getMessage();
        } finally {
            TraceUtil.removeAll();
        }
    }
}