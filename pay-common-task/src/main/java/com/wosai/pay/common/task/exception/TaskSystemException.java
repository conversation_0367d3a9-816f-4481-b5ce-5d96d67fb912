package com.wosai.pay.common.task.exception;

import com.wosai.pay.common.task.enums.PayTaskErrorTypeEnum;

/**
 * <AUTHOR>
 * @description 系统未知异常
 * @date 2025-06-19
 */
public class TaskSystemException extends TaskBaseException {
    private static final long serialVersionUID = -4621114942084087503L;

    public TaskSystemException(String message) {
        super(PayTaskErrorTypeEnum.RETRY_SYSTEM, message);
    }

    public TaskSystemException(String message, Throwable cause) {
        super(PayTaskErrorTypeEnum.RETRY_SYSTEM, message, cause);
    }
}