package com.wosai.pay.common.task.exception;

import com.wosai.pay.common.task.enums.PayTaskErrorTypeEnum;

/**
 * <AUTHOR>
 * @description 业务异常，无须重试
 * @date 2025-06-19
 */
public class TaskNonRetryableException extends TaskBaseException {
    private static final long serialVersionUID = 5550333927637209776L;

    public TaskNonRetryableException(String message) {
        super(PayTaskErrorTypeEnum.SKIP_BIZ, message);
    }

    public TaskNonRetryableException(String message, Throwable cause) {
        super(PayTaskErrorTypeEnum.SKIP_BIZ, message, cause);
    }
}