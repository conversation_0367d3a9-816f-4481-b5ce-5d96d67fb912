package com.wosai.pay.common.task.util;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.wosai.pay.common.task.exception.TaskSystemException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Objects;


/**
 * <AUTHOR>
 * @description
 * @date 2024/10/9
 */
public class TaskJsonUtil {
    private static final Logger log = LoggerFactory.getLogger(TaskJsonUtil.class);
    private static final ObjectMapper objectMapper = new ObjectMapper();
    static {
        objectMapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
        objectMapper.disable(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES);
        objectMapper.disable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS);
    }

    /**
     * 序列化
     *
     * @param object
     * @return
     */
    public static String encode(Object object) {
        if (Objects.isNull(object)) {
            return null;
        }
        try {
            return objectMapper.writeValueAsString(object);
        } catch (Exception e) {
            throw new TaskSystemException("encode object error", e);
        }
    }

    /**
     * 反序列化
     *
     * @param json
     * @param clazz
     * @return
     * @param <T>
     */
    public static <T> T decode(String json, Class<T> clazz) {
        if (json == null || json.isEmpty()) {
            return null;
        }
        try {
            return objectMapper.readValue(json, clazz);
        } catch (Exception e) {
            log.error("json decode failed, json:{}", json, e);
            throw new TaskSystemException("json decode failed, json:" + json, e);
        }
    }

    /**
     * 反序列化
     *
     * @param json
     * @param reference
     * @return
     * @param <T>
     */
    public static <T> T decode(String json, TypeReference<T> reference) {
        if (json == null || json.isEmpty()) {
            return null;
        }
        try {
            return objectMapper.readValue(json, reference);
        } catch (Exception e) {
            log.error("json decode failed, json:{}", json, e);
            throw new TaskSystemException("json decode failed, json:" + json, e);
        }
    }
}
