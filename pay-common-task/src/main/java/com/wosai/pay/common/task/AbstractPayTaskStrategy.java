package com.wosai.pay.common.task;

import com.fasterxml.jackson.core.type.TypeReference;
import com.wosai.pay.common.base.util.ExpireTimeConstant;
import com.wosai.pay.common.task.util.TraceUtil;
import com.wosai.pay.common.task.autoconfigure.PayTaskProperties;
import com.wosai.pay.common.task.constant.PayTaskAlarmConstant;
import com.wosai.pay.common.task.enums.PayTaskErrorTypeEnum;
import com.wosai.pay.common.task.enums.PayTaskStatusEnum;
import com.wosai.pay.common.task.exception.TaskBaseException;
import com.wosai.pay.common.task.lock.DistributedLockService;
import com.wosai.pay.common.task.repository.PayTaskRepository;
import com.wosai.pay.common.task.repository.entity.PayTaskConfigEntity;
import com.wosai.pay.common.task.repository.entity.PayTaskEntity;
import com.wosai.pay.common.task.util.AlarmNotify;
import com.wosai.pay.common.task.util.TaskJsonUtil;
import com.wosai.pay.common.task.util.TaskMapUtil;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.util.StopWatch;

import java.time.LocalDateTime;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import static com.wosai.pay.common.task.constant.PayTaskConstant.ERROR_MSG_MAX_LENGTH;
import static com.wosai.pay.common.task.constant.PayTaskRedisPrefix.ALARM_REACH_MAX_RETRY_CNT;
import static com.wosai.pay.common.task.constant.PayTaskRedisPrefix.TASK_BIZ_LOCK;

/**
 * <AUTHOR>
 * @description 抽象任务策略模板
 * @date 2025-06-18
 */
public abstract class AbstractPayTaskStrategy implements PayTaskStrategy {
    private static final Logger log = LoggerFactory.getLogger(AbstractPayTaskStrategy.class);
    protected static final String LOG_PREFIX = "[Strategy]";

    protected PayTaskProperties taskProperties;
    protected DistributedLockService distributedLockService;
    protected PayTaskRepository taskRepository;
    protected ThreadPoolTaskExecutor bizGlobalShareTaskExecutor;
    protected AlarmNotify alarmNotify;
    protected RedissonClient redissonClient;

    protected AbstractPayTaskStrategy() {/* 空构造器，依赖由框架注入 */}

    /**
     * 获取处理具体业务的默认线程池
     *
     * @return 业务线程池
     */
    @Override
    public ThreadPoolTaskExecutor getBizThreadPool() {
        return bizGlobalShareTaskExecutor;
    }

    /**
     * 构建具体业务处理的分布式锁key，防止并发处理
     *
     * @param task
     * @return 分布式锁key
     */
    public String buildBizLockKey(PayTaskEntity task) {
        return task.getIdempotentId();
    }

    /**
     * 由业务方实现
     *
     * @param config 任务配置
     * @param task   任务实例
     */
    protected abstract void doExecute(PayTaskConfigEntity config, PayTaskEntity task);

    /**
     * 达到最大重试次数后，是否开启告警。默认true
     */
    protected boolean enableAlarmWhenReachMaxRetryCnt() {
        return true;
    }

    @Override
    public void execute(PayTaskConfigEntity config, PayTaskEntity task) {
        RLock lock = null;
        StopWatch stopWatch = null;
        try {
            TraceUtil.createTraceId(task.getIdempotentId());
            lock = distributedLockService.buildLock(TASK_BIZ_LOCK, buildBizLockKey(task));
            if (!distributedLockService.tryLock(lock, 0, ExpireTimeConstant.FIVE_MINUTES_IN_SECONDS)) {
                log.warn("{}: 获取分布式锁失败, 跳过执行, taskId={}, taskType={}", LOG_PREFIX, task.getId(), task.getTaskType());
                return;
            }

            stopWatch = new StopWatch();
            stopWatch.start();
            log.info("{}: 开始处理任务实例, taskId={}, taskType={}, bizKey={}",
                    LOG_PREFIX, task.getId(), task.getTaskType(), task.getBizKey());

            //变更任务状态到进行中
            updateStatusToProcessing(task);

            //业务方处理
            doExecute(config, task);

            //更新任务状态
            updateSuccessStatus(task);
        } catch (Throwable e) {
            handleTaskException(config, task, e);
        } finally {
            if (null != stopWatch && stopWatch.isRunning()) {
                stopWatch.stop();
                log.info("{}: 任务实例处理完成, taskId={}, taskType={}, bizKey={}, 耗时:{}ms [{}s]",
                        LOG_PREFIX, task.getId(), task.getTaskType(), task.getBizKey(), stopWatch.getTotalTimeMillis(), stopWatch.getTotalTimeSeconds());
            }
            distributedLockService.unlock(lock);
            TraceUtil.removeAll();
        }
    }

    /**
     * 处理业务异常
     *
     * @param task
     * @param exception
     */
    private void handleTaskException(PayTaskConfigEntity config, PayTaskEntity task, Throwable exception) {
        try {
            PayTaskErrorTypeEnum errorType = PayTaskErrorTypeEnum.RETRY_SYSTEM;
            String errorMsg = exception.getMessage();
            boolean isUnknownException = true;
            if (exception instanceof TaskBaseException) {
                TaskBaseException baseException = (TaskBaseException) exception;
                errorType = baseException.getErrorType();
                errorMsg = baseException.getMessage();
                isUnknownException = false;
            }

            //防止errorMsg过长
            if (errorMsg != null && errorMsg.length() > ERROR_MSG_MAX_LENGTH) {
                errorMsg = errorMsg.substring(0, ERROR_MSG_MAX_LENGTH);
            }

            if (isUnknownException) {
                log.error("{}: 任务实例处理异常, idempotentId={}, bizKey={}, error={}",
                        LOG_PREFIX, task.getIdempotentId(), task.getTaskType(), errorMsg, exception);
            } else {
                log.error("{}: 任务实例处理失败, idempotentId={}, bizKey={}, error={}",
                        LOG_PREFIX, task.getIdempotentId(), task.getTaskType(), errorMsg);
            }

            PayTaskEntity updateData = new PayTaskEntity();
            updateData.setId(task.getId());
            updateData.setErrorType(errorType.name());
            updateData.setErrorMsg(errorMsg);
            updateData.setRetryCnt(task.getRetryCnt());
            //设置下次重试时间
            LocalDateTime nextRetryTime = LocalDateTime.now().plusSeconds(config.getRetryInterval());
            updateData.setNextRetryTime(nextRetryTime);
            updateData.setStep(task.getStep());
            if (task.getExtra() != null) {
                updateData.setExtra(task.getExtra());
            }

            PayTaskStatusEnum status = PayTaskStatusEnum.FAILURE;
            if (PayTaskErrorTypeEnum.SKIP_BIZ == errorType) {
                //错误类型是无须重试，则直接设置为取消状态
                status = PayTaskStatusEnum.CANCEL;
            }

            if (task.getRetryCnt() >= config.getMaxRetryTimes()) {
                //超过最大重试次数，则直接设置为取消状态
                status = PayTaskStatusEnum.CANCEL;
                if (enableAlarmWhenReachMaxRetryCnt()) {
                    //发送告警
                    sendReachMaxRetryCntAlarmNotify(config, task, errorMsg);
                }
            }

            updateData.setStatus(status.getStatus());
            taskRepository.updateSelectiveById(updateData);
        } catch (Exception e) {
            log.error("{}: handleTaskException发生异常, taskId={}, taskType={}", LOG_PREFIX, task.getId(), task.getTaskType(), e);
        }
    }

    /**
     * 达到最大重试次数, 发送告警(带疲劳度控制)
     *
     * @param config
     * @param task
     */
    private void sendReachMaxRetryCntAlarmNotify(PayTaskConfigEntity config, PayTaskEntity task, String errorMsg) {
        try {
            String alarmFlagKey = distributedLockService.buildKey(ALARM_REACH_MAX_RETRY_CNT, config.getTaskType());
            // 检查是否已有告警标志
            if (redissonClient.getBucket(alarmFlagKey).isExists()) {
                log.info("{}: 告警频率太高, 本次不再告警, taskName={}, taskType={}, idempotentId={}",
                        LOG_PREFIX, config.getTaskName(), config.getTaskType(), task.getIdempotentId());
                return;
            }

            Map<String, Object> alarmInfo = TaskMapUtil.initMap(
                    PayTaskAlarmConstant.ALARM_TASK_NAME, config.getTaskName(),
                    PayTaskAlarmConstant.ALARM_TASK_TYPE, config.getTaskType(),
                    PayTaskAlarmConstant.ALARM_TASK_ID, task.getId(),
                    PayTaskAlarmConstant.ALARM_TASK_IDEMPOTENT_ID, task.getIdempotentId(),
                    PayTaskAlarmConstant.ALARM_TASK_BIZ_KEY, task.getBizKey(),
                    TraceUtil.getTraceIdKey(), TraceUtil.getTraceId(),
                    PayTaskAlarmConstant.ALARM_ERROR_MSG, errorMsg);
            alarmNotify.sendAlert("任务达到最大重试次数告警", alarmInfo);

            // 设置告警标志
            redissonClient.getBucket(alarmFlagKey).set("1", taskProperties.getAlarmFrequencyOfReachMaxRetryCnt(), TimeUnit.SECONDS);
        } catch (Exception e) {
            log.error("{}: 发送告警异常, taskType={}", LOG_PREFIX, config.getTaskType(), e);
        }
    }

    /**
     * 更新任务状态
     *
     * @param taskId
     * @param status
     */
    protected void updateTaskStatus(long taskId, PayTaskStatusEnum status) {
        PayTaskEntity updateData = new PayTaskEntity();
        updateData.setId(taskId);
        updateData.setStatus(status.getStatus());
        taskRepository.updateSelectiveById(updateData);
    }

    /**
     * 更新任务状态为已完成
     *
     * @param task
     */
    private void updateSuccessStatus(PayTaskEntity task) {
        PayTaskEntity updateData = new PayTaskEntity();
        updateData.setId(task.getId());
        updateData.setRetryCnt(task.getRetryCnt());
        updateData.setNextRetryTime(task.getNextRetryTime());
        updateData.setStep(task.getStep());
        if (task.getExtra() != null) {
            updateData.setExtra(task.getExtra());
        }

        //清空错误信息
        if (task.getErrorType() != null) {
            updateData.setErrorType("");
        }
        if (task.getErrorMsg() != null) {
            updateData.setErrorMsg("");
        }

        //根据业务自定义策略判断是否更新任务的完成状态
        if (shouldUpdateFinishStatus()) {
            updateData.setStatus(PayTaskStatusEnum.SUCCESS.getStatus());
        }
        taskRepository.updateSelectiveById(updateData);
    }

    /**
     * 更新任务状态为进行中
     *
     * @param task
     */
    private void updateStatusToProcessing(PayTaskEntity task) {
        //只有待执行状态的任务才更新到执行中，否则维持原状态不变
        if (task.getStatus() != PayTaskStatusEnum.PENDING.getStatus()) {
            return;
        }

        updateTaskStatus(task.getId(), PayTaskStatusEnum.IN_PROGRESS);
    }

    /**
     * 解析任务实例的extra字段为指定类型
     *
     * @param <T>   目标类型
     * @param task  任务实例
     * @param clazz 目标类型Class对象
     * @return 解析后的extra对象
     */
    protected <T> T parseExtra(PayTaskEntity task, Class<T> clazz) {
        if (isExtraValid(task)) {
            return TaskJsonUtil.decode(task.getExtra(), clazz);
        }

        return null;
    }

    /**
     * 解析任务实例的extra字段为指定类型(支持复杂泛型)
     *
     * @param <T>           目标类型
     * @param task          任务实例
     * @param typeReference 目标类型TypeReference
     * @return 解析后的extra对象
     */
    protected <T> T parseExtra(PayTaskEntity task, TypeReference<T> typeReference) {
        if (isExtraValid(task)) {
            return TaskJsonUtil.decode(task.getExtra(), typeReference);
        }

        return null;
    }

    /**
     * 校验extra是否有效
     *
     * @param task
     * @return
     */
    private boolean isExtraValid(PayTaskEntity task) {
        return task != null && task.getExtra() != null && !task.getExtra().isEmpty();
    }

    /**
     * 将对象保存到任务实例的extra字段
     *
     * @param task   任务实例
     * @param object 要保存的对象
     */
    protected void saveToExtra(PayTaskEntity task, Object object) {
        if (task == null) {
            return;
        }
        if (null == object) {
            log.warn("taskId={}, 保存到extra字段的对象为空", task.getId());
            return;
        }
        try {
            task.setExtra(TaskJsonUtil.encode(object));
        } catch (Exception e) {
            log.error("taskId={}, 保存到extra字段失败, 对象解析异常", task.getId(), e);
        }
    }

    public void setTaskProperties(PayTaskProperties taskProperties) {
        this.taskProperties = taskProperties;
    }

    public void setDistributedLockService(DistributedLockService distributedLockService) {
        this.distributedLockService = distributedLockService;
    }

    public void setTaskRepository(PayTaskRepository taskRepository) {
        this.taskRepository = taskRepository;
    }

    public void setBizGlobalShareTaskExecutor(ThreadPoolTaskExecutor bizGlobalShareTaskExecutor) {
        this.bizGlobalShareTaskExecutor = bizGlobalShareTaskExecutor;
    }

    public void setAlarmNotify(AlarmNotify alarmNotify) {
        this.alarmNotify = alarmNotify;
    }

    public void setRedissonClient(RedissonClient redissonClient) {
        this.redissonClient = redissonClient;
    }
}