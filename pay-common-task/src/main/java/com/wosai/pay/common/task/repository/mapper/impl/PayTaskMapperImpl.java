package com.wosai.pay.common.task.repository.mapper.impl;

import com.wosai.pay.common.task.autoconfigure.PayTaskProperties;
import com.wosai.pay.common.task.enums.PayTaskStatusEnum;
import com.wosai.pay.common.task.exception.TaskDBException;
import com.wosai.pay.common.task.repository.entity.PayTaskEntity;
import com.wosai.pay.common.task.repository.mapper.PayTaskMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.dao.EmptyResultDataAccessException;
import org.springframework.jdbc.core.BatchPreparedStatementSetter;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.jdbc.support.GeneratedKeyHolder;
import org.springframework.jdbc.support.KeyHolder;

import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import static com.wosai.pay.common.task.constant.PayTaskColumn.*;


/**
 * <AUTHOR>
 * @description
 * @date 2025-06-17
 */
public class PayTaskMapperImpl implements PayTaskMapper {
    private static final String LOG_PREFIX = "[SQL]";
    private static final Logger log = LoggerFactory.getLogger(PayTaskMapperImpl.class);

    private final JdbcTemplate jdbcTemplate;
    private final PayTaskProperties taskProperties;

    public PayTaskMapperImpl(JdbcTemplate jdbcTemplate, PayTaskProperties taskProperties) {
        this.jdbcTemplate = jdbcTemplate;
        this.taskProperties = taskProperties;
    }

    // 预编译基础INSERT列名和占位符
    private static final String BASE_INSERT_COLUMNS = TASK_TYPE + ", " +
            PARTITION_ID + ", " +
            IDEMPOTENT_ID + ", " +
            BIZ_KEY + ", " +
            PLAN_TIME + ", " +
            STATUS + ", " +
            STEP + ", " +
            RETRY_CNT + ", " +
            NEXT_RETRY_TIME + ", " +
            ERROR_TYPE + ", " +
            ERROR_MSG + ", " +
            EXTRA;

    private static final String BASE_INSERT_PLACEHOLDERS = "?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?";

    @Override
    public Long insert(PayTaskEntity task) {
        if (task == null) {
            throw new TaskDBException("任务实例不能为空");
        }

        String sql = buildInsertSql();
        KeyHolder keyHolder = new GeneratedKeyHolder();
        jdbcTemplate.update(connection -> {
            PreparedStatement ps = connection.prepareStatement(sql, new String[]{"id"});
            setInsertParameters(ps, task);
            return ps;
        }, keyHolder);

        return keyHolder.getKey() != null ? keyHolder.getKey().longValue() : null;
    }

    @Override
    public int batchInsert(List<PayTaskEntity> tasks) {
        if (tasks == null || tasks.isEmpty()) {
            throw new TaskDBException("任务实例列表不能为空");
        }

        final String sql = buildInsertSql();
        int[] result = jdbcTemplate.batchUpdate(sql, new BatchPreparedStatementSetter() {
            @Override
            public void setValues(PreparedStatement ps, int i) throws SQLException {
                setInsertParameters(ps, tasks.get(i));
            }

            @Override
            public int getBatchSize() {
                return tasks.size();
            }
        });

        // 返回成功插入的总行数
        return Arrays.stream(result).sum();
    }

    @Override
    public int updateSelectiveById(PayTaskEntity task) {
        StringBuilder sql = new StringBuilder(256)
                .append("UPDATE ")
                .append(taskProperties.getTaskTableName())
                .append(" SET version = version +1");

        List<Object> params = new ArrayList<>();

        if (task.getStatus() != null) {
            sql.append(", ").append(STATUS).append(" = ?");
            params.add(task.getStatus());
        }
        if (task.getPlanTime() != null) {
            sql.append(", ").append(PLAN_TIME).append(" = ?");
            params.add(task.getPlanTime());
        }
        if (task.getPartitionId() != null) {
            sql.append(", ").append(PARTITION_ID).append(" = ?");
            params.add(task.getPartitionId());
        }
        if (task.getStep() != null) {
            sql.append(", ").append(STEP).append(" = ?");
            params.add(task.getStep());
        }
        if (task.getRetryCnt() != null) {
            sql.append(", ").append(RETRY_CNT).append(" = ?");
            params.add(task.getRetryCnt());
        }
        if (task.getNextRetryTime() != null) {
            sql.append(", ").append(NEXT_RETRY_TIME).append(" = ?");
            params.add(task.getNextRetryTime());
        }
        if (task.getErrorType() != null) {
            sql.append(", ").append(ERROR_TYPE).append(" = ?");
            params.add(task.getErrorType());
        }
        if (task.getErrorMsg() != null) {
            sql.append(", ").append(ERROR_MSG).append(" = ?");
            params.add(task.getErrorMsg());
        }
        if (task.getExtra() != null) {
            sql.append(", ").append(EXTRA).append(" = ?");
            params.add(task.getExtra());
        }

        sql.append(" WHERE ").append(ID).append(" = ?");
        params.add(task.getId());

        if (taskProperties.checkIsMapperLogEnable()) {
            log.info("{}: {}", LOG_PREFIX, mergeSqlWithParams(sql.toString(), params));
        }
        return jdbcTemplate.update(sql.toString(), params.toArray());
    }

    @Override
    public int batchUpdateStatusByIds(List<PayTaskEntity> tasks) {
        if (tasks == null || tasks.isEmpty()) {
            throw new TaskDBException("任务实例列表不能为空");
        }

        String sql = "UPDATE " + taskProperties.getTaskTableName() + " SET status = ?, version = version + 1 WHERE id = ?";

        return jdbcTemplate.batchUpdate(sql, tasks, tasks.size(),
                (ps, task) -> {
                    ps.setInt(1, task.getStatus());
                    ps.setLong(2, task.getId());
                }).length;
    }

    @Override
    public List<PayTaskEntity> queryTasksByCondition(Integer taskType, Integer partitionId,
                                                     List<PayTaskStatusEnum> statusList,
                                                     LocalDateTime planTime, Long lastId, int batchSize) {
        StringBuilder sql = new StringBuilder("SELECT * FROM ")
                .append(taskProperties.getTaskTableName())
                .append(" WHERE 1=1 ");

        List<Object> params = new ArrayList<>();

        if (taskType != null) {
            sql.append(" AND ").append(TASK_TYPE).append(" = ?");
            params.add(taskType);
        }

        if (partitionId != null) {
            sql.append(" AND ").append(PARTITION_ID).append(" = ?");
            params.add(partitionId);
        }
        if (statusList != null && !statusList.isEmpty()) {
            sql.append(" AND status IN (");
            for (int i = 0; i < statusList.size(); i++) {
                sql.append(i == 0 ? "?" : ",?");
                params.add(statusList.get(i).getStatus());
            }
            sql.append(")");
        }
        if (planTime != null) {
            sql.append(" AND ").append(PLAN_TIME).append(" <= ?");
            params.add(planTime);
        }
        if (lastId != null) {
            sql.append(" AND ").append(ID).append(" > ?");
            params.add(lastId);
        }

        sql.append(" ORDER BY ").append(ID).append(" ASC LIMIT ?");
        params.add(batchSize);

        if (taskProperties.checkIsMapperLogEnable()) {
            log.info("{}: {}", LOG_PREFIX, mergeSqlWithParams(sql.toString(), params));
        }
        return jdbcTemplate.query(sql.toString(), new TaskRowMapper(taskProperties), params.toArray());
    }

    @Override
    public PayTaskEntity queryByIdempotentId(String idempotentId) {
        String sql = "SELECT * FROM " + taskProperties.getTaskTableName() + " WHERE idempotent_id = ?";
        try {
            return jdbcTemplate.queryForObject(sql, new TaskRowMapper(taskProperties), idempotentId);
        } catch (EmptyResultDataAccessException e) {
            return null;
        }
    }

    @Override
    public List<String> queryExistingIdempotentIds(List<String> idempotentIds) {
        StringBuilder sql = new StringBuilder("SELECT ")
                .append(IDEMPOTENT_ID)
                .append(" FROM ")
                .append(taskProperties.getTaskTableName())
                .append(" WHERE ")
                .append(IDEMPOTENT_ID)
                .append(" IN (");

        // 构建IN条件占位符
        for (int i = 0; i < idempotentIds.size(); i++) {
            sql.append(i == 0 ? "?" : ",?");
        }
        sql.append(")");
        return jdbcTemplate.queryForList(sql.toString(), String.class, idempotentIds.toArray());
    }

    /**
     * 构建INSERT SQL语句
     */
    private String buildInsertSql() {
        StringBuilder sqlBuilder = new StringBuilder(256)
                .append("INSERT INTO ")
                .append(taskProperties.getTaskTableName())
                .append(" (")
                .append(BASE_INSERT_COLUMNS);

        StringBuilder placeholderBuilder = new StringBuilder(64)
                .append("(")
                .append(BASE_INSERT_PLACEHOLDERS);

        // 添加业务字段
        for (String column : taskProperties.getBizColumns()) {
            sqlBuilder.append(", ").append(column);
            placeholderBuilder.append(", ?");
        }

        return sqlBuilder.append(") VALUES ")
                .append(placeholderBuilder)
                .append(")")
                .toString();
    }

    /**
     * 设置INSERT参数
     */
    private void setInsertParameters(PreparedStatement ps, PayTaskEntity task) throws SQLException {
        // 设置基础字段参数
        ps.setInt(1, task.getTaskType());
        ps.setObject(2, task.getPartitionId());
        ps.setString(3, task.getIdempotentId());
        ps.setString(4, task.getBizKey());
        ps.setObject(5, task.getPlanTime());
        ps.setObject(6, task.getStatus());
        ps.setString(7, task.getStep());
        ps.setObject(8, task.getRetryCnt() != null ? task.getRetryCnt() : 0);
        ps.setObject(9, task.getNextRetryTime());
        ps.setString(10, task.getErrorType());
        ps.setString(11, task.getErrorMsg());
        ps.setString(12, task.getExtra());

        // 设置业务字段参数
        int paramIndex = 13;
        for (String column : taskProperties.getBizColumns()) {
            Object value = task.getBizExtField(column);
            if (value != null) {
                ps.setObject(paramIndex++, value);
            } else {
                ps.setNull(paramIndex++, java.sql.Types.NULL);
            }
        }
    }

    /**
     * 将SQL和参数合并为可打印的完整SQL(替换占位符)
     */
    private String mergeSqlWithParams(String sql, List<Object> params) {
        if (params == null || params.isEmpty()) {
            return sql;
        }

        StringBuilder sb = new StringBuilder();
        int paramIndex = 0;
        for (int i = 0; i < sql.length(); i++) {
            char c = sql.charAt(i);
            if (c == '?' && paramIndex < params.size()) {
                Object param = params.get(paramIndex++);
                if (param instanceof String) {
                    sb.append("'").append(param).append("'");
                } else {
                    sb.append(param);
                }
            } else {
                sb.append(c);
            }
        }
        return sb.toString();
    }

    private static LocalDateTime convertToLocalDateTime (ResultSet rs, String columnName) throws SQLException {
        java.sql.Timestamp timestamp = rs.getTimestamp (columnName);
        return timestamp != null ? timestamp.toLocalDateTime () : null;
    }

    private static class TaskRowMapper implements RowMapper<PayTaskEntity> {
        private final PayTaskProperties taskProperties;

        public TaskRowMapper(PayTaskProperties taskProperties) {
            this.taskProperties = taskProperties;
        }

        @Override
        public PayTaskEntity mapRow(ResultSet rs, int rowNum) throws SQLException {
            PayTaskEntity task = new PayTaskEntity();
            task.setId(rs.getLong(ID));
            task.setTaskType(rs.getInt(TASK_TYPE));
            task.setPartitionId(rs.getInt(PARTITION_ID));
            task.setIdempotentId(rs.getString(IDEMPOTENT_ID));
            task.setBizKey(rs.getString(BIZ_KEY));
            task.setPlanTime(rs.getObject(PLAN_TIME, LocalDateTime.class));
            task.setStatus(rs.getInt(STATUS));
            task.setStep(rs.getString(STEP));
            task.setRetryCnt(rs.getInt(RETRY_CNT));
            // 下次重试时间
            LocalDateTime nextRetryTimeObj = convertToLocalDateTime(rs, NEXT_RETRY_TIME);
            task.setNextRetryTime(nextRetryTimeObj);
            task.setErrorType(rs.getString(ERROR_TYPE));
            task.setErrorMsg(rs.getString(ERROR_MSG));
            task.setExtra(rs.getString(EXTRA));
            task.setVersion(rs.getLong(VERSION));
            task.setCtime(rs.getObject(CTIME, LocalDateTime.class));
            task.setMtime(rs.getObject(MTIME, LocalDateTime.class));
            task.setDeleted(rs.getInt(DELETED));

            // 映射业务字段到bizExtFields
            for (String column : taskProperties.getBizColumns()) {
                Object value = rs.getObject(column);
                if (value != null) {
                    task.setBizExtField(column, value);
                }
            }
            return task;
        }
    }
}
