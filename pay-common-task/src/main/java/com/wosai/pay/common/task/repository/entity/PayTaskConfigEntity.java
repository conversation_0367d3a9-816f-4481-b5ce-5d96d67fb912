package com.wosai.pay.common.task.repository.entity;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.wosai.pay.common.base.util.LocalDateTimeFormatter;
import com.wosai.pay.common.task.enums.PayTaskConfigStatusEnum;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @description 任务配置实体
 * @date 2025-06-17
 */
public class PayTaskConfigEntity {
    /**
     * 自增id
     */
    private Long id;
    /**
     * 任务名称
     */
    private String taskName;
    /**
     * 任务类型。用于区分不同的任务
     */
    private Integer taskType;
    /**
     * 分片数。用于将任务实例分片，以便多个服务节点可以同时处理同一个task_type下不同分片的任务实例
     */
    private Integer partitionCnt = 1;
    /**
     * 调度周期。该任务会在每天的调度周期内进行调度和重试，支持设置多个时间段，如：["00:00-10:00","12:00-13:00"]
     */
    private String schedulePeriod;
    /**
     * 调度间隔(秒)。多长时间调度一次
     */
    private Integer scheduleInterval = 300;
    /**
     * 最大重试次数。超过该重试次数则不再重试
     */
    private Integer maxRetryTimes = 3;
    /**
     * 重试间隔(秒)。执行失败后，间隔多长时间再进行重试
     */
    private Integer retryInterval = 300;
    /**
     * 任务超时时间(秒)。如果任务是执行中且持续时间超过timeoutSeconds，该任务会被再次执行
     */
    private Integer timeoutSeconds = 600;
    /**
     * 任务配置的状态
     *
     * @see PayTaskConfigStatusEnum
     */
    private Integer status;
    /**
     * 告警配置
     */
    private String alertConfig;
    /**
     * 扩展字段
     */
    private String extra;
    /**
     * 备注
     */
    private String remark;
    /**
     * 版本号
     */
    private Long version;
    /**
     * 创建时间
     */
    @JsonDeserialize(using = LocalDateTimeFormatter.LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeFormatter.LocalDateTimeSerializer.class)
    private LocalDateTime ctime;
    /**
     * 更新时间
     */
    @JsonDeserialize(using = LocalDateTimeFormatter.LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeFormatter.LocalDateTimeSerializer.class)
    private LocalDateTime mtime;
    /**
     * 是否删除 0：未删除 1：已删除
     */
    private Integer deleted;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getTaskName() {
        return taskName;
    }

    public void setTaskName(String taskName) {
        this.taskName = taskName;
    }

    public Integer getTaskType() {
        return taskType;
    }

    public void setTaskType(Integer taskType) {
        this.taskType = taskType;
    }

    public Integer getPartitionCnt() {
        return partitionCnt;
    }

    public void setPartitionCnt(Integer partitionCnt) {
        this.partitionCnt = partitionCnt;
    }

    public String getSchedulePeriod() {
        return schedulePeriod;
    }

    public void setSchedulePeriod(String schedulePeriod) {
        this.schedulePeriod = schedulePeriod;
    }

    public Integer getScheduleInterval() {
        return scheduleInterval;
    }

    public void setScheduleInterval(Integer scheduleInterval) {
        this.scheduleInterval = scheduleInterval;
    }

    public Integer getMaxRetryTimes() {
        return maxRetryTimes;
    }

    public void setMaxRetryTimes(Integer maxRetryTimes) {
        this.maxRetryTimes = maxRetryTimes;
    }

    public Integer getRetryInterval() {
        return retryInterval;
    }

    public void setRetryInterval(Integer retryInterval) {
        this.retryInterval = retryInterval;
    }

    public Integer getTimeoutSeconds() {
        return timeoutSeconds;
    }

    public void setTimeoutSeconds(Integer timeoutSeconds) {
        this.timeoutSeconds = timeoutSeconds;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getAlertConfig() {
        return alertConfig;
    }

    public void setAlertConfig(String alertConfig) {
        this.alertConfig = alertConfig;
    }

    public String getExtra() {
        return extra;
    }

    public void setExtra(String extra) {
        this.extra = extra;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public Long getVersion() {
        return version;
    }

    public void setVersion(Long version) {
        this.version = version;
    }

    public LocalDateTime getCtime() {
        return ctime;
    }

    public void setCtime(LocalDateTime ctime) {
        this.ctime = ctime;
    }

    public LocalDateTime getMtime() {
        return mtime;
    }

    public void setMtime(LocalDateTime mtime) {
        this.mtime = mtime;
    }

    public Integer getDeleted() {
        return deleted;
    }

    public void setDeleted(Integer deleted) {
        this.deleted = deleted;
    }
}