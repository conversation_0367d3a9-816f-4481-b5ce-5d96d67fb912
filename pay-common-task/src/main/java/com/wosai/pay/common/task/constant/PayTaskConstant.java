package com.wosai.pay.common.task.constant;

/**
 * <AUTHOR>
 * @description
 * @date 2025-06-18
 */
public class PayTaskConstant {
    public static final String SEPARATOR = ":";//分隔符
    public static final String CONNECTOR = "-";//连接符
    public static final int ERROR_MSG_MAX_LENGTH = 256;//错误信息最大长度

    public static final String DAEMON_NORMAL_TASK_SCHEDULE_NAME = "DaemonNormalSchedule";     //守护线程-普通任务调度器名字
    public static final String DAEMON_RETRY_TASK_SCHEDULE_NAME = "DaemonRetrySchedule";       //守护线程-重试任务调度器名字
    public static final String DAEMON_TASK_THREAD_POOL_NAME = "DaemonTaskThreadPool";         //守护线程-任务的线程池名字
    public static final String MONITOR_THREAD_POOL_NAME = "MonitorThreadPool";                //监控器线程池名字
    public static final String BIZ_GLOBAL_SHARE_THREAD_POOL_NAME = "GlobalShareThreadPool";   //业务方全局共享的线程池名字
    public static final String BIZ_CUSTOM_THREAD_POOL_NAME_PREFIX = "Biz-";                   //业务方自定义线程池的名字前缀

    //redis 订阅topic
    public static final String TASK_PROPERTIES_UPDATE_TOPIC = "task_properties_update_topic";//任务全局配置更新topic
    public static final String THREAD_POOL_UPDATE_TOPIC = "task_thread_pool_update_topic";//线程池配置更新topic
}
