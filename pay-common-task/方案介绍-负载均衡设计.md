# 方案介绍-负载均衡设计

## 设计背景
在分布式环境中，如何在处理大量任务的同时避免单节点过载是关键挑战。本设计通过引入partitionId概念，实现了任务的均衡处理。

## 核心设计

### 1. 分片ID(partitionId)设计
- 在`PayTaskEntity`中定义partitionId字段
- 设计目的：通过给任务打上不同的分片id，实现多节点并行处理
- 分配策略：新增任务时随机分配partitionId

### 2. 分片处理机制
- 在`PayTaskDaemonThread.scanAndDispatchNormalTasks`中实现
- 处理流程：
  1. 获取所有启用的任务配置
  2. 根据每个任务配置的partitionCnt将该任务拆分成带partitionId的多个子任务
  3. 提交到线程池并发执行

## 实现效果
- 任务随机分配不同的partition标识
- 服务节点只需处理特定partition的任务
- 避免热点问题
- 可以通过增加partitionCnt动态控制并发度

## 核心代码说明

### 1. 任务实体(PayTaskEntity)
```java
/**
 * 分片id。不同的服务节点可以处理同一个task_type下不同分片id的任务，目的是为了降低单机负载
 */
private Integer partitionId;
```

### 2. 任务扫描分发逻辑(PayTaskDaemonThread)
```java
// 将任务根据partitionCnt拆分成多个子任务
for (int partitionId = 0; partitionId < partitionCnt; partitionId++) {
    final int finalPartitionId = partitionId;
    partitionTasks.add(() -> taskExecuteTemplate.execute(config, finalPartitionId));
}

// 批量提交分片任务
partitionTasks.forEach(task -> {
    normalTaskExecutor.submit(task);
});
```

### 3. 分布式锁校验
```java
RLock lock = taskExecuteTemplate.buildGlobalTaskLock(config.getTaskType(), partitionId);
if (lock.isLocked()) {
    log.info("已经存在执行中的任务, 跳过, taskType={}, partitionId={}", config.getTaskType(), partitionId);
    return true;
}
```
通过分布式锁确保同一partition的任务不会被多个节点重复处理