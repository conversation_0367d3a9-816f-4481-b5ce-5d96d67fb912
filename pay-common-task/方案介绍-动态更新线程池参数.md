# 方案介绍-动态更新线程池参数

## 1. 背景
通用任务SDK中的线程池参数（核心线程数、最大线程数）需要在不重启应用的情况下动态调整，并保证集群环境下所有节点的参数同步更新。

## 2. 设计目标
- ✅ 实现线程池参数的动态调整
- ✅ 支持集群环境下所有节点的参数同步
- ✅ 提供参数校验机制
- ✅ 支持监控和告警

## 3. 架构设计
```mermaid
graph TD
    A[管理接口] --> B[PayTaskMonitor]
    B --> C[Redisson]
    C --> D[节点A]
    C --> E[节点B]
    C --> F[节点C]
```

## 4. 工作流程
```mermaid
sequenceDiagram
    participant Admin as 管理接口
    participant Monitor as PayTaskMonitor
    participant Redisson as Redis
    
    Admin->>Monitor: updateThreadPoolConfig(configDTO)
    Monitor->>Redisson: 发布配置更新事件
    Redisson->>Monitor: 推送事件（所有节点）
    Monitor->>Monitor: 更新本地线程池配置
```

## 5. 核心组件

### 5.1 ThreadPoolConfigDTO
```java
public class ThreadPoolConfigDTO {
    private String poolName;  // 线程池名称
    private Integer coreSize; // 核心线程数
    private Integer maxSize;  // 最大线程数
}
```

### 5.2 PayTaskMonitor关键方法
- `subscribeThreadPoolUpdates()`: 订阅Redis配置更新事件
- `updateThreadPoolConfig()`: 更新线程池配置并发布事件
- `updateLocalThreadPoolConfig()`: 更新本地线程池配置

## 6. 关键实现

### 6.1 订阅配置更新
```java
private void subscribeThreadPoolUpdates() {
    RTopic topic = redissonClient.getTopic(getThreadPoolUpdateTopic());
    topic.addListener(ThreadPoolConfigDTO.class, (channel, config) -> {
        log.info("收到线程池配置更新事件: {}", config);
        updateLocalThreadPoolConfig(config);
    });
}
```

### 6.2 发布配置更新
```java
public String updateThreadPoolConfig(ThreadPoolConfigDTO config) {
    // 1. 更新本地配置
    updateLocalThreadPoolConfig(config);
    
    // 2. 发布配置更新事件
    RTopic topic = redissonClient.getTopic(getThreadPoolUpdateTopic());
    topic.publish(config);
    return "success";
}
```

### 6.3 本地配置更新
```java
private ImmutablePair<Boolean, String> updateLocalThreadPoolConfig(ThreadPoolConfigDTO config) {
    // 参数校验
    if (config.getCoreSize() > config.getMaxSize()) {
        return ImmutablePair.of(false, "核心线程数不能大于最大线程数");
    }
    
    // 获取线程池并更新配置
    ThreadPoolTaskExecutor executor = threadPools.get(config.getPoolName());
    executor.setCorePoolSize(config.getCoreSize());
    executor.setMaxPoolSize(config.getMaxSize());
    return ImmutablePair.of(true, "success");
}
```

## 7. Redis数据结构
- **Topic名称**: `{prefix}:task_thread_pool_update_topic`
- **消息类型**: `ThreadPoolConfigDTO`序列化对象

## 8. 异常处理
- 线程池不存在：返回错误信息"找不到指定的线程池"
- 参数非法：返回具体校验失败原因
- Redis连接失败：记录错误日志，本地更新仍生效
- 线程池更新失败：记录错误日志并返回失败原因

## 9. 监控指标
- 活跃线程数
- 当前线程总数  
- 队列大小
- 历史最大线程数
- 已完成任务数
- 已提交任务总数