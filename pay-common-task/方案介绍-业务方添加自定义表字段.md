# 方案介绍-业务方添加自定义表字段

## 背景介绍
通用任务SDK依赖的任务实例表，默认的字段都是固定的，不一定满足所有业务方的诉求。
如果业务方想要给任务实例表增加附带业务属性的一些自定义字段，原有的设计无法满足。

因此，通用任务SDK新增了自定义字段的能力扩展，允许业务方在原有的任务实例表的基础上，随时增加一个或多个自定义字段。

## 1. 功能概述

针对任务实例表(pay_task)，通用任务SDK支持业务方添加自定义字段，主要特性：
- 通过配置添加业务自定义的业务字段，无需修改代码
- 字段自动映射到数据库表和实体对象

## 2. 完整配置说明

### 2.1 新增业务字段步骤（以下仅为示例）

1. 修改application.properties添加字段配置：
```properties
# 业务方自定义字段配置(如果新增多个字段，使用逗号分隔)
pay.common.config.task.biz-columns=merchant_id,provider,channel
```

2. 执行数据库变更脚本添加对应字段：
```sql
ALTER TABLE pay_task
ADD COLUMN IF NOT EXISTS merchant_id VARCHAR(255) DEFAULT NULL COMMENT '商户ID',
ADD COLUMN IF NOT EXISTS provider VARCHAR(64) DEFAULT NULL COMMENT '服务商',
ADD COLUMN IF NOT EXISTS channel VARCHAR(32) DEFAULT NULL COMMENT '渠道';
```

3. 重启应用使配置生效


## 3. 业务方使用指南

### 3.1 写入业务字段

```java
PayTaskEntity task = new PayTaskEntity();
// 设置标准字段
task.setTaskType(1);
task.setBizKey("test123");

// 设置业务扩展字段
task.setBizExtField("merchant_id", "mch_123456");
task.setBizExtField("provider", "alipay");
```

### 3.2 读取业务字段

```java
// 从数据库查询
PayTaskEntity task = mapper.queryById(1L);

// 获取业务字段
String merchantId = (String) task.getBizExtField("merchant_id");
String provider = (String) task.getBizExtField("provider");
```

## 4. 数据库最佳实践

1. 字段命名规范：
   - 使用小写字母和下划线组合，如`merchant_id`
   - 保持与配置中的名称一致

2. 字段类型建议：
   - 字符串类型：VARCHAR(64-255)
   - 数值类型：BIGINT/DECIMAL
   - 时间类型：DATETIME

3. 索引建议：
   - 为高频查询字段添加索引
   ```sql
   ALTER TABLE pay_task ADD INDEX idx_merchant_id (merchant_id);
   ```

## 5. 注意事项

1. 新增字段后需要先执行数据库变更再更新配置
3. 删除字段需要确保业务代码不再依赖该字段
4. 字段名不要与系统保留字段冲突