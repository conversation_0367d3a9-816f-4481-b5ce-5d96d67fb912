# 通用任务SDK-系统设计

## 1. 系统能力概述

### 1.1 核心能力边界

本SDK提供以下核心能力：
1. **通用能力**：封装了任务调度、分批处理、并发控制、执行、异常重试等通用能力
2. **动态变更配置**：内置全局配置的动态调整能力
3. **弹性任务处理**：内置线程池参数的动态调整能力
4. **容错机制**：根据不同异常类型采取不同处理策略
2. **负载均衡**：支持将任务分片，避免单节点性能问题
5. **监控/告警**：实时监控线程池运行状态，异常则告警

### 1.2 适用场景

典型业务场景包括：
- 定时批量处理业务（如结算、分账、报表生成等）
- 异步任务处理（如消息推送、文件导出）
- 需要重试机制的业务

### 1.3 为何选择本SDK

| 对比维度   | 自己实现           | 本SDK方案          |
|--------|----------------|-----------------|
| 开发成本   | 高（需从头实现核心逻辑）   | 低（开箱即用）         |
| 维护成本   | 高（需持续优化和bug修复） | 低（统一维护，团队共享）    |
| 功能完整性  | 有限（通常只满足当前需求）  | 全面（覆盖各类任务处理场景）  |
| 性能可靠性  | 不确定（依赖实现质量）    | 随着不断迭代，会越来越稳定   |
| 配置动态变更 | 一般不支持          | 支持全局配置和线程池动态变更  |
| 扩展性    | 不易扩展           | 通过策略模式轻松扩展新任务类型 |
| 监控能力   | 通常缺乏           | 内置监控指标和告警机制     |
| 技术演进   | 容易停滞           | 持续迭代（团队共享最新改进）  |

关键优势：
1. **降低技术债务**：避免每个项目重复造轮子
2. **提升研发效率**：专注业务逻辑而非基础设施
3. **保障系统稳定**：内置重试、监控、配置动态变更等可靠性机制
4. **统一技术栈**：便于团队知识共享和人员协作

### 1.4 部分设计方案详解
1. **《方案介绍-负载均衡设计.md》**
2. **《方案介绍-业务方添加自定义表字段.md》**
3. **《方案介绍-动态更新SDK全局配置.md》**
4. **《方案介绍-动态更新线程池参数.md》**

## 2. 整体架构

```mermaid
graph TD
    A[业务应用] --> B[通用任务SDK]
    B --> C[任务守护线程]
    C --> D[线程池管理]
    D --> E[策略模板引擎]
    E --> F[分布式锁 Redisson]
    E --> G[数据库访问 JdbcTemplate]
    B --> H[管理接口]
    H --> I[配置热更新]
    H --> J[任务监控]
```

## 3. SDK初始化&执行流程

```mermaid
graph TD
    A[应用启动] --> B[SDK自动装配]
    B --> B1[全局配置管理器初始化]
    B1 --> B3[订阅全局配置更新]
    B --> C[线程池初始化]
    C --> C1[普通任务线程池]
    C --> C2[业务共享线程池]
    B --> D[守护线程初始化]
    D --> D2[初始化普通任务调度器]
    D --> D3[初始化重试任务调度器]
    B --> E[任务策略自动注册]
    D2 --> F[定时扫描已启用的任务配置]
    D3 --> G[加重试全局锁]
    F --> I[根据taskType加分布式锁]
    G --> G1[定时扫描失败状态的任务实例]
    G1 --> H[定时扫描失败状态的任务实例]
    I --> J[拉取任务实例]
    J --> H[提交到业务线程池去执行]
    H --> K[CountDownLatch等待]
    K --> L[根据幂等id加分布式锁]
    L --> L1[业务策略执行]
    L1 --> M[更新任务状态]
    B3 --> O[全局配置热更新] --> D
```

## 4. 异常处理机制

### 4.1 错误类型映射

| 异常类型                      | 错误码          | 处理方式        |
|---------------------------|--------------|-------------|
| TaskDBException           | RETRY_DB     | DB异常，自动重试   |
| TaskSystemException       | RETRY_SYSTEM | 系统未知异常，自动重试 |
| TaskRemoteException       | RETRY_REMOTE | 远程服务异常，自动重试 |
| TaskBizException          | RETRY_BIZ    | 业务异常，自动重试   |
| TaskNonRetryableException | SKIP_BIZ     | 业务异常，无须重试   |

### 4.2 重试流程

```mermaid
graph TD
    A[任务执行失败] --> B{错误类型}
    B -->|SKIP_BIZ| C[更新为取消状态]
    B -->|其他| D{重试次数检查}
    D -->|未超限| E[安排下次重试]
    D -->|已超限| F[更新为取消状态]
```

## 5. 自动配置实现

### 5.1 核心Bean初始化顺序

```mermaid
graph TD
    A[PayTaskProperties] --> B[DistributedLockService]
    A --> C[PayTaskMonitor]
    A --> D[ThreadPoolTaskExecutor]
    B --> E[PayTaskExecuteTemplate]
    C --> E
    D --> E
    E --> F[PayTaskDaemonThread]
    E --> G[策略自动注册]
```

### 5.2 策略自动注册

```java

@PostConstruct
private void registerStrategies() {
    // 获取所有AbstractPayTaskStrategy实现类
    Map<String, AbstractPayTaskStrategy> strategies =
            applicationContext.getBeansOfType(AbstractPayTaskStrategy.class);

    // 注册到executeTemplate中
    strategies.forEach((beanName, strategy) -> {
        executeTemplate.registerStrategy(strategy.getPayTaskType(), strategy);
    });
}
```

## 6. 核心模块设计

### 6.1 任务守护线程 (`PayTaskDaemonThread`)

```java
public class PayTaskDaemonThread {
    // 核心启动方法
    public void start() {
        // 1. 初始化线程池监控
        initMonitor();

        // 2. 启动定时任务调度
        startSchedulers();
    }

    // 初始化监控
    private void initMonitor() {
        // 注册线程池监控
        taskMonitor.registerThreadPool(NORMAL_POOL, normalTaskExecutor);
        taskMonitor.registerThreadPool(RETRY_POOL, retryTaskExecutor);
        taskMonitor.start();
    }

    // 启动调度器
    private void startSchedulers() {
        // 普通任务调度器
        scheduler.scheduleAtFixedRate(
                this::dispatchNormalTasks,
                0,
                config.getScanInterval(),
                TimeUnit.SECONDS
        );

        // 重试任务调度器
        scheduler.scheduleAtFixedRate(
                this::dispatchRetryTasks,
                0,
                config.getRetryInterval(),
                TimeUnit.SECONDS
        );
    }

    // 分发普通任务
    private void dispatchNormalTasks() {
        // 1. 获取所有启用的任务配置
        List<PayTaskConfig> configs = getActiveConfigs();

        // 2. 按分区提交任务
        configs.forEach(config -> {
            for (int i = 0; i < config.getPartitions(); i++) {
                taskExecutor.execute(() ->
                        taskTemplate.execute(config, i)
                );
            }
        });
    }

    // 分发重试任务
    private void dispatchRetryTasks() {
        // 1. 获取需要重试的任务
        List<RetryTask> tasks = fetchRetryTasks();

        // 2. 提交重试执行
        tasks.forEach(task ->
                retryExecutor.execute(() ->
                        taskTemplate.retry(task)
                )
        );
    }
}
```

### 6.2 策略模板引擎 (`PayTaskExecuteTemplate`)

```java
public class PayTaskExecuteTemplate {
    // 核心执行方法
    public void execute(PayTaskConfig config, int partitionId) {
        // 1. 获取分布式锁
        if (!tryLock(config.getTaskType(), partitionId)) {
            return;
        }

        try {
            Long lastId = 0L;
            while (true) {
                // 2. 获取任务批次
                List<Task> tasks = fetchTasks(config, partitionId, lastId);
                if (tasks.isEmpty()) break;

                // 3. 处理任务
                processTasks(config, tasks);

                lastId = tasks.get(tasks.size() - 1).getId();
            }
        } finally {
            releaseLock();
        }
    }

    // 处理任务批次
    private void processTasks(PayTaskConfig config, List<Task> tasks) {
        for (Task task : tasks) {
            try {
                // 更新任务状态为执行中
                updateTaskStatus(task, EXECUTING);

                // 执行具体业务策略
                getStrategy(config.getTaskType()).execute(task);

                // 更新任务状态为成功
                updateTaskStatus(task, SUCCESS);
            } catch (NonRetryableException e) {
                // 不可重试异常处理
                updateTaskStatus(task, CANCELLED, e);
            } catch (Exception e) {
                // 可重试异常处理
                handleRetry(task, config, e);
            }
        }
    }

    // 处理可重试异常
    private void handleRetry(Task task, PayTaskConfig config, Exception e) {
        if (task.getRetryCount() >= config.getMaxRetries()) {
            updateTaskStatus(task, CANCELLED, e);
        } else {
            scheduleRetry(task, config);
        }
    }
}
```

## 7. 线程池管理

### 7.1 线程池配置

```java

@Bean
public ThreadPoolTaskExecutor normalTaskExecutor(PayTaskProperties taskProperties) {
    ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
    executor.setCorePoolSize(taskProperties.getTaskConfigThreadPoolCoreSize());
    executor.setMaxPoolSize(taskProperties.getTaskConfigThreadPoolMaxSize());
    executor.setQueueCapacity(taskProperties.getQueueCapacity());
    executor.setThreadNamePrefix(DAEMON_TASK_THREAD_POOL_NAME + CONNECTOR);
    return executor;
}
```

### 7.2 线程池动态调整

```java
public boolean updateThreadPoolConfig(ThreadPoolConfigDTO config) {
    ThreadPoolTaskExecutor executor = threadPools.get(config.getPoolName());
    if (executor != null) {
        executor.setCorePoolSize(config.getCoreSize());
        executor.setMaxPoolSize(config.getMaxSize());
        return true;
    }
    return false;
}
```

### 7.3 线程池监控指标

| 指标名称               | 说明      |
|--------------------|---------|
| activeCount        | 当前活跃线程数 |
| queueSize          | 任务队列大小  |
| poolSize           | 当前线程池大小 |
| largestPoolSize    | 历史最大线程数 |
| completedTaskCount | 已完成任务数  |
| totalTaskCount     | 已提交任务总数 |

## 8. 监控告警模块

### 8.1 监控指标

```java
private void monitorSinglePool(String poolName, ThreadPoolTaskExecutor executor) {
    int activeCount = executor.getActiveCount();
    int queueSize = executor.getThreadPoolExecutor().getQueue().size();
    int poolSize = executor.getPoolSize();
    long completedTaskCount = executor.getThreadPoolExecutor().getCompletedTaskCount();

    // 触发告警条件
    if (queueSize > warningThreshold) {
        triggerAlert("TASK_THREAD_POOL_QUEUE_WARNING", msg);
    }
}
```

### 8.2 告警触发条件

| 告警类型   | 触发条件                                       |
|--------|--------------------------------------------|
| 队列积压告警 | queueSize > queueWarningThreshold          |
| 活跃线程告警 | activeCount > activeThreadWarningThreshold |

## 9. 数据库设计

### 9.1 任务配置表 (`pay_task_config`)

| 字段名               | 字段类型         | 描述                                                                 |
|-------------------|--------------|--------------------------------------------------------------------|
| id                | BIGINT(20)   | 自增主键                                                               |
| task_name         | VARCHAR(100) | 任务名称                                                               |
| task_type         | INT(11)      | 任务类型编码（业务方自己定义）                                                    |
| partition_cnt     | INT(11)      | 分片数。用于将任务实例分片，以便多个服务节点可以同时处理同一个task_type下不同分片的任务实例                 |
| schedule_period   | VARCHAR(255) | 调度周期。该任务会在每天的调度周期内进行调度和重试,支持设置多个时间段,如["08:00-10:00","15:00-18:00"] |
| schedule_interval | INT(11)      | 调度间隔(秒)。多长时间调度一次                                                   |
| max_retry_times   | INT(11)      | 最大重试次数                                                             |
| retry_interval    | INT(11)      | 重试间隔(秒)。执行失败后，间隔多长时间再进行重试                                          |
| timeout_seconds   | INT(11)      | 任务超时时间(秒)。如果任务是执行中且持续时间超过该时间，该任务会被再次拉取并执行                          |
| status            | TINYINT(2)   | 任务配置的启用状态: 0-禁用,1-启用。见PayTaskConfigStatusEnum.java                 |
| alert_config      | TEXT         | 告警配置。暂时未用到                                                         |
| extra             | TEXT         | 扩展字段                                                               |
| remark            | VARCHAR(255) | 备注                                                                 |

### 9.2 任务实例表 (`pay_task`)

| 字段名             | 字段类型         | 描述                                                                                                                     |
|-----------------|--------------|------------------------------------------------------------------------------------------------------------------------|
| id              | BIGINT(20)   | 自增主键                                                                                                                   |
| task_type       | INT(11)      | 任务类型编码（业务方自己定义）                                                                                                        |
| partition_id    | INT(11)      | 分片id。用于将任务实例分片，以便多个服务节点可以同时处理同一个task_type下的任务实例                                                                        |
| idempotent_id   | VARCHAR(128) | 幂等id。用来防止重复插入                                                                                                          |
| biz_key         | VARCHAR(128) | 业务标识，如商户ID                                                                                                             |
| plan_time       | DATETIME     | 计划执行时间。达到该时间后任务会被拉取并执行                                                                                                 |
| status          | TINYINT(2)   | 状态: 0-待执行,1-执行中,2-成功,3-失败,4-取消。见PayTaskStatusEnum.java                                                                 |
| step            | VARCHAR(64)  | 任务执行的步骤（非必须，业务方自定义）                                                                                                    |
| retry_cnt       | INT(11)      | 当前重试次数                                                                                                                 |
| next_retry_time | DATETIME     | 下次重试时间                                                                                                                 |
| error_type      | VARCHAR(32)  | 错误类型，见PayTaskErrorTypeEnum.java。 RETRY_SYSTEM-系统异常，RETRY_DB-DB异常，RETRY_REMOTE-远程服务异常，RETRY_BIZ-业务异常，SKIP_BIZ-业务异常且无须重试 |
| error_msg       | VARCHAR(512) | 错误信息                                                                                                                   |
| extra           | TEXT         | 任务扩展参数(JSON格式)                                                                                                         |

## 10. DDL

### 10.1 任务配置表DDL

```sql
CREATE TABLE pay_task_config
(
    id                BIGINT(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '自增主键',
    task_name         VARCHAR(100)        NOT NULL COMMENT '任务名称',
    task_type         INT(11)             NOT NULL COMMENT '任务类型编码',
    partition_cnt     INT(11)             NOT NULL COMMENT '分片数。用于将任务实例分片，以便多个服务节点可以同时处理同一个task_type下不同分片的任务实例',
    schedule_period   VARCHAR(255)        NOT NULL DEFAULT '["00:00-23:59"]' COMMENT '调度周期。该任务会在每天的调度周期内进行调度和重试,支持设置多个时间段',
    schedule_interval INT(11)             NOT NULL COMMENT '调度间隔(秒)。多长时间调度一次',
    max_retry_times   INT(11)             NOT NULL DEFAULT 3 COMMENT '最大重试次数',
    retry_interval    INT(11)             NOT NULL COMMENT '重试间隔(秒)。执行失败后，间隔多长时间再进行重试',
    timeout_seconds   INT(11)             NOT NULL COMMENT '任务超时时间(秒)。如果任务是执行中且持续时间超过配置的超时时间，该任务会被再次执行',
    status            TINYINT(2)          NOT NULL DEFAULT 1 COMMENT '状态: 0-禁用,1-启用',
    alert_config      TEXT                         DEFAULT NULL COMMENT '告警配置。{"alertType":["MESSAGE","VOICE"],"MESSAGE":{"alarmUrl":"http://xxx/api/alert/message","title":"自动D0结算异常-飞书告警"},"VOICE":{"alarmUrl":"http://xxx/api/alert/voice","title":"自动D0结算异常-电话告警"}}',
    extra             TEXT COMMENT '扩展字段',
    remark            VARCHAR(255) COMMENT '备注',
    version           BIGINT(20)          NOT NULL DEFAULT 0 COMMENT '乐观锁版本号',
    ctime             DATETIME            NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    mtime             DATETIME            NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    deleted           TINYINT(2)          NOT NULL DEFAULT 0 COMMENT '是否删除: 0-否,1-是',
    PRIMARY KEY (`id`),
    UNIQUE INDEX uniq_task_type (task_type)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='通用任务SDK-配置表'
```

### 10.2 任务实例表DDL

```sql
CREATE TABLE pay_task
(
    id              BIGINT(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '自增主键',
    task_type       INT(11)             NOT NULL COMMENT '任务类型编码',
    partition_id    INT(11)             NOT NULL COMMENT '分片id。用于将任务实例分片，以便多个服务节点可以同时处理同一个task_type下的任务实例',
    idempotent_id   VARCHAR(128)        NOT NULL COMMENT '幂等id。用来防止重复插入',
    biz_key         VARCHAR(128) COMMENT '业务标识，如商户ID',
    plan_time       DATETIME            NOT NULL COMMENT '计划执行时间',
    status          TINYINT(2)          NOT NULL DEFAULT 0 COMMENT '状态: 0-待执行,1-执行中,2-成功,3-失败,4-取消',
    step            VARCHAR(64)                  DEFAULT NULL COMMENT '任务执行的步骤',
    retry_cnt       INT(11)             NOT NULL DEFAULT 0 COMMENT '当前重试次数',
    next_retry_time DATETIME COMMENT '下次重试时间',
    error_type      VARCHAR(32) COMMENT '错误类型 RETRY_SYSTEM-系统异常，RETRY_DB-DB异常，RETRY_REMOTE-远程服务异常，RETRY_BIZ-业务异常，SKIP_BIZ-业务异常且无须重试',
    error_msg       VARCHAR(512) COMMENT '错误信息',
    extra           TEXT COMMENT '任务扩展参数(JSON格式)',
    version         BIGINT(20)          NOT NULL DEFAULT 0 COMMENT '版本号',
    ctime           DATETIME            NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    mtime           DATETIME            NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    deleted         TINYINT             NOT NULL DEFAULT 0 COMMENT '是否删除: 0-否,1-是',
    PRIMARY KEY (`id`),
    UNIQUE INDEX uniq_idem (idempotent_id),
    INDEX idx_status_id (status, id),
    INDEX idx_biz_key (biz_key),
    INDEX idx_status_type_time (status, task_type, partition_id, plan_time)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='通用任务SDK-实例表'
```

### 11. 全局参数配置

| 参数                                                              | 默认值             | 必须配置                             | 说明                                                    |
|-----------------------------------------------------------------|-----------------|----------------------------------|-------------------------------------------------------|
| `pay.common.config.task.enable`                                 | true            | 否                                | 是否启动通用任务功能                                            |
| `pay.common.config.task.global-lock-prefix`                     | -               | <span style="color:red">是</span> | 分布式锁前缀。同一个redis实例可能会被多个项目共用，所以要加前缀防止项目之间的锁冲突          |
| `pay.common.config.task.trace-id-key`                           | trace_id        | 否                                | 链路追踪id的key名字，默认是: trace_id                            |
| `pay.common.config.task.task-config.table-name`                 | pay_task_config | 否                                | 任务配置表的表名。可自定义, 默认为pay_task_config                     |
| `pay.common.config.task.table-name`                             | pay_task        | 否                                | 任务实例表的表名。可自定义, 默认为pay_task                            |
| `pay.common.config.task.biz-columns`                            | -               | 否                                | 业务方扩展字段，默认无须配置。如果业务方需要在pay_task增加自定义的字段，可以在这里配置       |
| `pay.common.config.task.mapper.log.enable`                      | true            | 否                                | 控制是否打印sql日志，默认为true                                   |
| `pay.common.config.task.normal.scan-interval`                   | 30              | 否                                | 普通任务扫描周期(秒)                                           |
| `pay.common.config.task.thread-pool.queue-capacity`             | 100             | 否                                | 线程池队列长度                                               |
| `pay.common.config.task.task-config.thread-pool.core-size`      | 20              | 否                                | 处理任务配置列表的线程池的核心线程数                                    |
| `pay.common.config.task.task-config.thread-pool.max-size`       | 50              | 否                                | 处理任务配置列表的线程池的最大线程数                                         |
| `pay.common.config.task.biz-global-share.thread-pool.core-size` | 10              | 否                                | 业务的默认全局共享线程池核心线程数                                     |
| `pay.common.config.task.biz-global-share.thread-pool.max-size`  | 50              | 否                                | 业务的默认全局共享线程池最大线程数                                     |
| `pay.common.config.task.retry.scan-interval`                    | 300             | 否                                | 重试任务扫描周期(秒)                                           |
| `pay.common.config.task.batch-size`                             | 1000            | 否                                | 每批处理的任务实例数量                                           |
| `pay.common.config.task.max-iterations`                         | 1000            | 否                                | 分批拉取任务时，最大迭代次数。防止陷入死循环，导致一直拉取数据。默认最多分批拉取1000次后则结束本次任务 |
| `pay.common.config.task.countDownLatch.timeout`                 | 900             | 否                                | 并发处理每批任务实例时(由batchSize控制)，等待全部处理完成的超时时间(秒)。默认15分钟     |
| `pay.common.config.task.monitor.enable`                         | true            | 否                                | 是否启用监控功能（监控线程池的运行状态）                                  |
| `pay.common.config.task.monitor.interval`                       | 120             | 否                                | 监控时间间隔（秒）                                             |
| `pay.common.config.task.monitor.queue-threshold`                | 50              | 否                                | 告警阈值: 线程池的队列的积压任务数                                    |
| `pay.common.config.task.monitor.active-thread-threshold`        | 20              | 否                                | 告警阈值: 线程池的活跃线程数                                       |
| `pay.common.config.task.alarm-notify.url`                       | -               | 否                                | 飞书告警url。不配置的话则不会发送告警消息，但会正常打印告警日志                     |
| `pay.common.config.task.alarm-frequency.reach-max-retry-cnt`    | 1800            | 否                                | 告警频率控制（秒）。达到最大重试次数的告警，多久告警一次，默认半小时                    |
