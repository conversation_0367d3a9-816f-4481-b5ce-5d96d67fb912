# 通用任务SDK-接入指南

## 1. 添加sdk依赖

```xml

<dependencies>
    <!-- 通用任务sdk -->
    <dependency>
        <groupId>com.wosai.pay</groupId>
        <artifactId>pay-common-task</artifactId>
        <version>1.2.7-SNAPSHOT</version>
    </dependency>
</dependencies>
```

如果项目中没有引入redisson、spring-jdbc，则需要添加相关依赖(版本可根据项目调整)

```xml

<dependencies>
    <dependency>
        <groupId>org.springframework</groupId>
        <artifactId>spring-jdbc</artifactId>
        <version>5.3.30</version>
    </dependency>
    <dependency>
        <groupId>org.redisson</groupId>
        <artifactId>redisson</artifactId>
        <version>3.25.0</version>
    </dependency>
</dependencies>
```

## 2. 创建配置类

```java

@Configuration
@Import(PayTaskAutoConfiguration.class)
@ComponentScan("com.wosai.pay.common.task")
public class PayTaskStarter implements PayTaskComponent {
    @Resource
    private RedissonClient redissonClient;
    @Resource
    private JdbcTemplate jdbcTemplate;

    @Override
    public RedissonClient getRedissonClient() {
        return redissonClient;
    }

    @Override
    public JdbcTemplate getJdbcTemplate() {
        return jdbcTemplate;
    }
}
```

## 3. 实现业务策略

```java

@Component
public class SampleTaskStrategy extends AbstractPayTaskStrategy {

    /**
     * 是否必须实现：必须
     * 获取任务类型
     */
    @Override
    public int getPayTaskType() {
        return PayTaskType.SAMPLE_TASK_DEMO;//PayTaskType由各个项目自己定义
    }

    /**
     * 是否必须实现：必须
     * 处理具体业务
     *
     * @param config   任务配置
     * @param task     任务实例
     */
    @Override
    protected void doExecute(PayTaskConfigEntity config, PayTaskEntity task) {
        // 业务逻辑实现
        System.out.println("Processing task: " + task.getId());
    }

    /**
     * 是否必须实现：非必须
     * 构建具体业务处理的分布式锁key，防止并发处理
     * 默认会根据idempotentId作为分布式锁key，见:AbstractPayTaskStrategy.buildBizLockKey
     *
     * @param task
     * @return 分布式锁key
     */
    String buildBizLockKey(PayTaskEntity task);

    /**
     * 是否必须实现：非必须
     *
     * 处理实际业务的任务时，会使用默认的业务线程池(所有任务共享)：AbstractPayTaskStrategy.bizGlobalShareTaskExecutor
     * 如果要使用业务方自己的业务线程池，覆盖getBizThreadPool接口即可
     *
     * @return
     */
    @Override
    public ThreadPoolTaskExecutor getBizThreadPool() {
        return xxx;//根据实际的业务场景，业务方自己创建对应的线程池，需要是ThreadPoolTaskExecutor类型，便于监控
    }

    /**
     * 是否必须实现：非必须
     * 任务完成后是否自动更新任务状态到已完成，默认是true
     * 如果业务方想自己控制任务状态，可以重写此方法，返回false。然后在doExecute中手动处理任务状态
     *
     * @param task
     * @return
     */
    @Override
    public boolean shouldUpdateFinishStatus(PayTaskEntity task) {
        return true;
    }
}
```

## 4. 参数配置

在`application.properties`中添加：

```properties
pay.common.config.task.enable=true //是否启用通用任务模块，默认为true
pay.common.config.task.global-lock-prefix=xxx //分布式锁前缀。如提现服务，可以设置为：withdraw（同一个redis实例可能会被多个项目共用，所以要加前缀防止项目之间的锁冲突）
pay.common.config.task.alarm-notify.url=xxx //飞书告警url。不配置的话则不会发送告警消息，但会正常打印告警日志
```
除了pay.task.global-lock-prefix必须配置外，还有很多默认的参数可以自定义，
详情见《通用任务SDK-系统设计.md》第11章节


## 5. 数据库准备（新建任务配置表和任务实例表）

执行SQL脚本创建任务配置表和任务实例表, SQL脚本详见《通用任务SDK-系统设计.md》第10章节

## 6. 新增任务配置(以结算任务为例)

在`pay_task_config`表中添加一条结算任务：

```sql
INSERT INTO pay_task_config (task_name, task_type, partition_cnt,
                             schedule_period, schedule_interval, max_retry_times, retry_interval,
                             timeout_seconds, status)
VALUES ('自动D1结算任务', 1001, 1, '["00:00-23:59"]', 300, 3, 300, 600, 1);
```

## 7. 创建任务实例

```java

@Service
public class BizXXXService {

    @Autowired
    private PayTaskRepository taskRepository;

    public void createTask(int taskType, String businessKey) {
        PayTaskCreateDTO taskDTO = new PayTaskCreateDTO();
        taskDTO.setTaskType(taskType);
        taskDTO.setIdempotentId(uuid);
        taskDTO.setBizKey(businessKey);
        taskDTO.setPlanTime(new Date());
        taskDTO.setSetp("create_records");//非必须
        taskRepository.create(taskDTO);
    }
}
```

## 8. 管理接口

- 重载配置：`POST /pay-task/manage/reload`
- 查询配置：`GET /pay-task/manage/query`
- 查询线程池运行信息：`GET /pay-task/manage/queryAllThreadPoolRunningInfo`
- 更改线程池配置：`GET /pay-task/manage/updateThreadPoolConfig`

拷贝下面的PayTaskManageController到项目中即可

```java

@Slf4j
@RestController
@RequestMapping("/pay-task/manage")
public class PayTaskManageController {
    @Resource
    private PayTaskService payTaskService;
    @Resource
    private PayTaskRepository taskRepository;

    //查询任务全局配置
    @RequestMapping("/query")
    public PayTaskProperties query() {
        return payTaskService.queryGlobalTaskConfig();
    }

    //更新任务全局配置
    @RequestMapping("/reload")
    public String reload(@RequestBody PayTaskProperties newProperties) {
        try {
            return payTaskService.reload(newProperties);
        } catch (Exception e) {
            log.error("任务组件重新加载异常, error={}", e.getMessage(), e);
            throw new TaskBizException("任务组件重新加载异常: " + e.getMessage());
        }
    }

    //查询所有线程池的运行信息
    @RequestMapping("/queryAllThreadPoolRunningInfo")
    public List<ThreadPoolStatusDTO> queryAllThreadPoolRunningInfo() {
        try {
            return payTaskService.queryAllThreadPoolRunningInfo();
        } catch (Exception e) {
            log.error("查询线程池运行信息异常, error={}", e.getMessage(), e);
            throw new TaskBizException("查询线程池运行信息异常: " + e.getMessage());
        }
    }

    //修改指定线程池的核心线程数和最大线程数
    @RequestMapping("/updateThreadPoolConfig")
    public boolean updateThreadPoolConfig(@RequestBody ThreadPoolConfigDTO threadPoolConfig) {
        try {
            return payTaskService.updateThreadPoolConfig(threadPoolConfig);
        } catch (Exception e) {
            log.error("更新线程池参数异常, error={}", e.getMessage(), e);
            throw new TaskBizException("更新线程池参数异常: " + e.getMessage());
        }
    }

    //手动创建任务实例（可选）
    @RequestMapping("/createTask")
    public Long createTask(@RequestBody PayTaskCreateDTO taskDTO) {
        try {
            log.info("创建任务实例请求参数: {}", JsonUtil.encode(taskDTO));
            return taskRepository.create(taskDTO);
        } catch (Exception e) {
            log.error("创建任务实例异常, taskDTO={}, error={}", JsonUtil.encode(taskDTO), e.getMessage(), e);
            throw new TaskBizException("创建任务实例异常: " + e.getMessage());
        }
    }
}
```

## 9. 注意事项

1. 确保Redis和数据库连接配置正确
2. 任务ID需保证全局唯一性
3. 业务逻辑需实现幂等性
4. 非重试异常应抛出`TaskNonRetryableException`

## 10. 其他
### 10.1 关闭“达到最大重试次数告警”
业务策略类继承AbstractPayTaskStrategy后，重写enableAlarmWhenReachMaxRetryCnt方法，返回false（默认是true), 即可关闭。
关闭后，该业务策略的重试次数达到最大重试次数后，不再发送告警