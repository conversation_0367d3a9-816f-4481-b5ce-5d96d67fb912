# 方案介绍-动态更新SDK全局配置

## 1. 背景
通用任务SDK中的全局配置，需要在不重启应用的情况下动态调整，并保证集群环境下所有节点的参数同步更新

## 2. 设计目标
- ✅ 实现配置变更的集群广播
- ✅ 保证最终一致性

## 3. 架构设计
```mermaid
graph TD
    A[管理接口] --> B[PayTaskServiceImpl]
    B --> C[PayTaskPropertiesDistributor]
    C --> D[Redisson]
    D --> E[节点A]
    D --> F[节点B]
    D --> G[节点C]
```

## 4. 工作流程
```mermaid
sequenceDiagram
    participant Admin as 管理接口
    participant Service as PayTaskServiceImpl
    participant Distributor as PayTaskPropertiesDistributor
    participant Redisson as Redis
    
    Admin->>Service: reload(newConfig)
    Service->>Distributor: publishTaskPropertiesUpdate(newProperties)
    Distributor->>Redisson: 发布事件
    Redisson->>Distributor: 推送事件（所有节点）
    Distributor->>Distributor: 更新内存中的任务配置
    Distributor->>Distributor: 重新初始化任务组件
```

## 5. 核心组件

### 5.1 PayTaskPropertiesDistributor 任务配置管理
```java
public class PayTaskPropertiesDistributor {
    //允许动态更新的配置字段(白名单机制)
    - enable //全局开关，是否启用任务模块，默认为true
    - mapperLogEnable //是否启用Mapper日志打印，默认为true
    - normalScanInterval //普通任务扫描周期，默认30s
    - taskConfigThreadPoolCoreSize //处理任务配置列表的线程池核心线程数，默认20
    - taskConfigThreadPoolMaxSize //处理任务配置列表的线程池最大线程数，默认50
    - bizGlobalShareThreadPoolCoreSize //业务全局共享线程池核心线程数，默认10
    - bizGlobalShareThreadPoolMaxSize  //业务全局共享线程池最大线程数，默认50
    - retryScanInterval //重试任务扫描周期，默认5分钟
    - batchSize //每批处理的任务实例数量，默认1000
    - maxIterations //最大迭代次数，默认1000
    - countDownLatchTimeout //并发处理超时时间，默认15分钟
    - monitorEnable //是否启用线程池监控，默认为true
    - monitorInterval //线程池监控间隔，默认120秒
    - queueWarningThreshold //线程池队列告警阈值，默认50
    - activeThreadWarningThreshold //线程池活跃线程告警阈值，默认20
    - alarmNotifyUrl //告警通知地址
    - alarmFrequencyOfReachMaxRetryCnt //达到最大重试次数的告警频率(秒)，默认半小时
          
    // 关键方法：
    - subscribeTaskPropertiesUpdate() // 订阅配置更新事件
    - publishTaskPropertiesUpdate()   // 发布配置更新事件
    - partialUpdateTaskProperties()   // 部分更新配置(受白名单限制)
    - reinitializeComponents()        // 重新初始化任务组件
}
```

## 6. 关键实现

### 6.1 PayTaskService 动态更新接口
```java
public class PayTaskServiceImpl implements PayTaskService {
    private final PayTaskPropertiesDistributor taskPropertiesDistributor;
    
    @Override
    public synchronized String reload(PayTaskProperties newProperties) {
        // 发布配置更新事件, 触发集群更新
        taskPropertiesDistributor.publishTaskPropertiesUpdate(newProperties);
        return "success";
    }
}
```

## 7. Redis数据结构
- **Topic名称**: `{prefix}:task_properties_update_topic`
- **消息类型**: `PayTaskProperties`序列化对象

## 8. 异常处理
- Redis连接失败：降级使用本地配置，记录错误日志
- 资源释放：实现DisposableBean接口，在销毁时取消订阅

## 9. 注意事项
1. 只有ALLOWED_DYNAMIC_UPDATE_FIELDS白名单中的字段支持动态更新
2. 配置更新是最终一致性的，各节点可能会有短暂不一致
3. 关键操作都有同步锁保护(synchronized)