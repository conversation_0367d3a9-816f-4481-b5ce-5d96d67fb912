package com.wosai.pay.common.state.manager.dto;

public abstract class StateManagerBaseDTO {

    private String processorType;

    private String businessType;

    private Integer subStateType;

    private Boolean enabled;

    private String remark;

    public String getProcessorType() {
        return processorType;
    }

    public void setProcessorType(String processorType) {
        this.processorType = processorType;
    }

    public String getBusinessType() {
        return businessType;
    }

    public void setBusinessType(String businessType) {
        this.businessType = businessType;
    }

    public Integer getSubStateType() {
        return subStateType;
    }

    public void setSubStateType(Integer subStateType) {
        this.subStateType = subStateType;
    }

    public Boolean getEnabled() {
        return enabled;
    }

    public void setEnabled(Boolean enabled) {
        this.enabled = enabled;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }
}
