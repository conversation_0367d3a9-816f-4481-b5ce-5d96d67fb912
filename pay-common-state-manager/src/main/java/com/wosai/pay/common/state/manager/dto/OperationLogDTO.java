package com.wosai.pay.common.state.manager.dto;


public class OperationLogDTO {

    private String platformCode;

    private String operatorUserId;

    private String operatorUserName;

    private String remark;

    private String externalSceneTraceId;

    private String sceneTemplateCode;

    public String getPlatformCode() {
        return platformCode;
    }

    public void setPlatformCode(String platformCode) {
        this.platformCode = platformCode;
    }

    public String getOperatorUserId() {
        return operatorUserId;
    }

    public void setOperatorUserId(String operatorUserId) {
        this.operatorUserId = operatorUserId;
    }

    public String getOperatorUserName() {
        return operatorUserName;
    }

    public void setOperatorUserName(String operatorUserName) {
        this.operatorUserName = operatorUserName;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getExternalSceneTraceId() {
        return externalSceneTraceId;
    }

    public void setExternalSceneTraceId(String externalSceneTraceId) {
        this.externalSceneTraceId = externalSceneTraceId;
    }

    public String getSceneTemplateCode() {
        return sceneTemplateCode;
    }

    public void setSceneTemplateCode(String sceneTemplateCode) {
        this.sceneTemplateCode = sceneTemplateCode;
    }

    public OperationLogDTO() {
    }

    public OperationLogDTO(String platformCode, String operatorUserId, String operatorUserName, String remark, String externalSceneTraceId, String sceneTemplateCode) {
        this.platformCode = platformCode;
        this.operatorUserId = operatorUserId;
        this.operatorUserName = operatorUserName;
        this.remark = remark;
        this.externalSceneTraceId = externalSceneTraceId;
        this.sceneTemplateCode = sceneTemplateCode;
    }

    @Override
    public String toString() {
        return "OperationLogDTO{" +
                "platformCode='" + platformCode + '\'' +
                ", operatorUserId='" + operatorUserId + '\'' +
                ", operatorUserName='" + operatorUserName + '\'' +
                ", remark='" + remark + '\'' +
                ", externalSceneTraceId='" + externalSceneTraceId + '\'' +
                ", sceneTemplateCode='" + sceneTemplateCode + '\'' +
                '}';
    }

}
