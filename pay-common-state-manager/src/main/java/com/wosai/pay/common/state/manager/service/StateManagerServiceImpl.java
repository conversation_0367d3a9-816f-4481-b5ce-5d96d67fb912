package com.wosai.pay.common.state.manager.service;

import com.wosai.pay.common.data.Criteria;
import com.wosai.pay.common.state.manager.constant.StateManagerConstant;
import com.wosai.pay.common.state.manager.dao.CommonStateDao;
import com.wosai.pay.common.state.manager.dto.StateManagerBaseDTO;
import com.wosai.pay.common.state.manager.entity.CommonStateEntity;
import com.wosai.pay.common.state.manager.dto.LogParamsDTO;
import com.wosai.pay.common.state.manager.result.StateDicResult;
import com.wosai.pay.common.state.manager.result.StateManagerResult;
import com.wosai.pay.common.state.manager.service.processor.StateManagerProcessor;
import com.wosai.pay.common.state.manager.service.processor.StateManagerProcessorRegistry;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Transactional;


import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Logger;

/**
 * 状态服务实现类
 * 实现了状态的查询和更新逻辑，支持不同业务类型的自定义处理
 */
public class StateManagerServiceImpl implements StateManagerService {

    private static final Logger LOGGER = Logger.getLogger(StateManagerServiceImpl.class.getName());

    private final CommonStateDao commonStateDao;

    public StateManagerServiceImpl(CommonStateDao commonStateDao) {
        this.commonStateDao = commonStateDao;
    }

    /**
     * 注册一个状态处理器
     *
     * @param processor 要注册的处理器
     * @return 注册是否成功
     */
    public boolean registerProcessor(StateManagerProcessor<?, ?> processor) {
        return StateManagerProcessorRegistry.register(processor);
    }


    @Override
    @Transactional(isolation = Isolation.REPEATABLE_READ)
    public <T extends StateManagerBaseDTO, R extends StateManagerResult> Boolean updateState(T updatestateManagerBaseDTO, LogParamsDTO logParamsDto) {
        // 验证业务类型和状态类型
        String biz = updatestateManagerBaseDTO.getBiz();
        Integer type = updatestateManagerBaseDTO.getType();
        StateManagerConfig.validBizAndType(biz, type);

        R oldStateResult = queryState(updatestateManagerBaseDTO);
        // 获取业务类型对应的处理器
        String opType = updatestateManagerBaseDTO.getOpType();
        StateManagerProcessor<StateManagerBaseDTO, StateManagerResult> processor = StateManagerProcessorRegistry.getProcessor(opType);
        if (processor == null) {
            throw new IllegalStateException("未找到实体类型[" + opType + "]对应的处理器");
        }

        // 初始化或获取状态实体
        CommonStateEntity entity = initAndGetStateByBizIdAndEntity(updatestateManagerBaseDTO, processor);
        String originalSubState = entity.getSubStatesBits();
        // 更新子状态
        Map<Integer, Boolean> subStates = entity.getSubStates();
        if (subStates == null) {
            subStates = new HashMap<>();
            entity.setSubStates(subStates);
        }
        subStates.put(type, updatestateManagerBaseDTO.getOpen());
        entity.setSubStates(subStates);
        // 更新总状态和其他信息
        entity.setRemark(updatestateManagerBaseDTO.getRemark());
        entity.setState(StateManagerConstant.COMMON_SWITCH_BASE.equals(entity.getSubStatesBits()));
        if (originalSubState.equals(entity.getSubStatesBits())) {
            return Boolean.TRUE;
        }
        // 更新状态
        commonStateDao.update(entity);

        R curStateResult = queryState(updatestateManagerBaseDTO);

        // 后置处理
        processor.afterStateUpdate(updatestateManagerBaseDTO, oldStateResult, curStateResult, logParamsDto);

        return Boolean.TRUE;
    }

    @Override
    @SuppressWarnings("unchecked")
    public <T extends StateManagerBaseDTO, R extends StateManagerResult> R queryState(T stateManagerBaseDTO) {
        String biz = stateManagerBaseDTO.getBiz();
        String opType = stateManagerBaseDTO.getOpType();
        StateManagerConfig.validBizAndType(biz, null);
        List<StateDicResult.SubStateDic> stateDicList = StateManagerConfig.getStatesList(biz);

        // 获取业务类型对应的处理器
        StateManagerProcessor<StateManagerBaseDTO, StateManagerResult> processor = StateManagerProcessorRegistry.getProcessor(opType);
        if (processor == null) {
            throw new IllegalStateException("未找到实体类型[" + opType + "]对应的处理器");
        }
        CommonStateEntity entity = initAndGetStateByBizIdAndEntity(stateManagerBaseDTO, processor);

        // 创建返回结果
        R result = (R) processor.createResultInstance(stateManagerBaseDTO);

        // 设置基本状态信息
        result.setState(entity.getState());

        // 设置子状态列表
        List<StateManagerResult.SubState> subStateList = new ArrayList<>();
        Map<Integer, Boolean> subStates = entity.getSubStates();

        if (stateDicList != null) {
            for (StateDicResult.SubStateDic stateDic : stateDicList) {
                StateManagerResult.SubState subState = new StateManagerResult.SubState();
                Integer type = stateDic.getType();
                subState.setType(type);
                subState.setDesc(stateDic.getDesc());
                Boolean value = subStates.get(type);
                subState.setValue(value != null ? value : true );
                subStateList.add(subState);
            }
        }

        result.setSubStateList(subStateList);

        return result;
    }

    private <T extends StateManagerBaseDTO, E extends CommonStateEntity> CommonStateEntity initAndGetStateByBizIdAndEntity(T stateManagerBaseDTO, StateManagerProcessor processor) {
        CommonStateEntity entity;
        Criteria criteria = Criteria.where(StateManagerConstant.BIZ).is(stateManagerBaseDTO.getBiz());
        processor.generateCriteria(criteria, stateManagerBaseDTO);
        entity = commonStateDao.filter(criteria).fetchOne();
        if (entity == null) {
            try {
                E newEntity = (E) processor.buildCommonStateEntity(stateManagerBaseDTO);
                newEntity.setBiz(stateManagerBaseDTO.getBiz());
                newEntity.setState(Boolean.TRUE);
                newEntity.setSubStates(null);
                commonStateDao.insert(newEntity);
            } catch (DuplicateKeyException e) {
                LOGGER.info("DuplicateKeyException");
            }
            entity = commonStateDao.filter(criteria).fetchOne();
        }

        return entity;
    }
}
