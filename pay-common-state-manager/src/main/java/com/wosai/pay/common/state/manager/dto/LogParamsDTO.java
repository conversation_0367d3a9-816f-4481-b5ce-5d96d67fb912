package com.wosai.pay.common.state.manager.request;


public class StateLogParamsDto {

    private String platformCode;

    private String opUserId;

    private String opUserName;

    private String remark;

    private String outerSceneTraceId;

    private String sceneTemplateCode;

    public String getPlatformCode() {
        return platformCode;
    }

    public void setPlatformCode(String platformCode) {
        this.platformCode = platformCode;
    }

    public String getOpUserId() {
        return opUserId;
    }

    public void setOpUserId(String opUserId) {
        this.opUserId = opUserId;
    }

    public String getOpUserName() {
        return opUserName;
    }

    public void setOpUserName(String opUserName) {
        this.opUserName = opUserName;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getOuterSceneTraceId() {
        return outerSceneTraceId;
    }

    public void setOuterSceneTraceId(String outerSceneTraceId) {
        this.outerSceneTraceId = outerSceneTraceId;
    }

    public String getSceneTemplateCode() {
        return sceneTemplateCode;
    }

    public void setSceneTemplateCode(String sceneTemplateCode) {
        this.sceneTemplateCode = sceneTemplateCode;
    }

    public StateLogParamsDto() {
    }

    public StateLogParamsDto(String platformCode, String opUserId, String opUserName, String remark, String outerSceneTraceId, String sceneTemplateCode) {
        this.platformCode = platformCode;
        this.opUserId = opUserId;
        this.opUserName = opUserName;
        this.remark = remark;
        this.outerSceneTraceId = outerSceneTraceId;
        this.sceneTemplateCode = sceneTemplateCode;
    }

    @Override
    public String toString() {
        return "OpLogCreateRequest{" +
                "platformCode='" + platformCode + '\'' +
                ", opUserId='" + opUserId + '\'' +
                ", opUserName='" + opUserName + '\'' +
                ", remark='" + remark + '\'' +
                ", outerSceneTraceId='" + outerSceneTraceId + '\'' +
                ", sceneTemplateCode='" + sceneTemplateCode + '\'' +
                '}';
    }

}
