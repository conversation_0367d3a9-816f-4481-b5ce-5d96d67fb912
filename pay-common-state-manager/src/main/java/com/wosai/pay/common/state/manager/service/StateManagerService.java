package com.wosai.pay.common.state.manager.service;

import com.wosai.pay.common.state.manager.dto.StateManagerBaseDTO;
import com.wosai.pay.common.state.manager.dto.LogParamsDTO;
import com.wosai.pay.common.state.manager.result.StateManagerResult;
import com.wosai.pay.common.state.manager.service.processor.StateManagerProcessor;

public interface StateManagerService {

    /**
     * 更新业务的值（记录日志）
     *
     * @param <T> StateChangeBaseBean 的子类类型
     * @param updatestateManagerBaseDTO 更新状态请求，必须是 StateChangeBaseBean 的子类
     * @param logParamsDto 操作日志请求
     * @return 是否操作成功
     */
    <T extends StateManagerBaseDTO, R extends StateManagerResult> Boolean updateState(T updatestateManagerBaseDTO, LogParamsDTO logParamsDto);

    /**
     * 根据业务查询 状态值
     *
     * @param <T> stateManagerBaseDTO 的子类类型
     * @param <R> StateManagerResult 的子类类型
     * @param stateManagerBaseDTO 查询状态请求，必须是 stateManagerBaseDTO 的子类
     * @return 业务详情，返回 TradeStateResult 的子类
     */
    <T extends StateManagerBaseDTO, R extends StateManagerResult> R queryState(T stateManagerBaseDTO);

    /**
     * 注册一个状态处理器
     *
     * @param processor 要注册的处理器
     * @return 注册是否成功
     */
    boolean registerProcessor(StateManagerProcessor<?, ?> processor);

}
