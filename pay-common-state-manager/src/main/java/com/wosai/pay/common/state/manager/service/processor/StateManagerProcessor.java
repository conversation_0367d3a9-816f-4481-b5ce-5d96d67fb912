package com.wosai.pay.common.state.manager.service.processor;

import com.wosai.pay.common.data.Criteria;
import com.wosai.pay.common.state.manager.entity.CommonStateEntity;
import com.wosai.pay.common.state.manager.dto.StateManagerBaseDTO;
import com.wosai.pay.common.state.manager.dto.OperationLogDTO;
import com.wosai.pay.common.state.manager.result.StateManagerResult;

/**
 * 状态处理器接口
 * 不同业务类型可以实现此接口来自定义状态处理逻辑
 */
public interface StateManagerProcessor<T extends StateManagerBaseDTO, R extends StateManagerResult> {

    /**
     * 获取此处理器支持的实体类型
     * @return 业务类型
     */
    String getSupportedEntityType();


    void generateCriteria(Criteria criteria, T stateManagerBaseDTO);


    <E extends CommonStateEntity> E buildCommonStateEntity(T stateManagerBaseDTO);


    /**
     * 创建状态查询结果实例
     * @param stateManagerBaseDTO 状态查询请求
     * @return 状态查询结果
     */
    R createResultInstance(T stateManagerBaseDTO);

    /**
     * 在状态更新后执行的操作
     * @param stateManagerBaseDTO 状态更新请求
     * @param previousState 修改前状态
     * @param currentState 修改后状态
     */
     void afterStateUpdate(T stateManagerBaseDTO, R previousState, R currentState, OperationLogDTO operationLogRequest);
}