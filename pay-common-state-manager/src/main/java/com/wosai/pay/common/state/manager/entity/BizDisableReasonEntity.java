package com.wosai.pay.common.state.manager.entity;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.wosai.pay.common.data.VersionedRecord;

public class BizDisableReasonEntity extends VersionedRecord<Long> {
    /**
     * 业务类型
     */
    @JsonProperty("business_type" )
    private String businessType;

    /**
     * 子状态类型
     */
    @JsonProperty("sub_state_type" )
    private Integer subStateType;

    /**
     * 子状态描述
     */
    @JsonProperty("description" )
    private String description;


    public String getBusinessType() {
        return businessType;
    }

    public void setBusinessType(String businessType) {
        this.businessType = businessType;
    }

    public Integer getSubStateType() {
        return subStateType;
    }

    public void setSubStateType(Integer subStateType) {
        this.subStateType = subStateType;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public BizDisableReasonEntity() {
    }

    public BizDisableReasonEntity(String businessType, Integer subStateType, String description) {
        this.businessType = businessType;
        this.subStateType = subStateType;
        this.description = description;
    }
}
