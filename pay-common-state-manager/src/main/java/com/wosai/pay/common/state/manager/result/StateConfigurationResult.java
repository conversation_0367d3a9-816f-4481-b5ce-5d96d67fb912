package com.wosai.pay.common.state.manager.result;

import java.util.List;

public class StateConfigurationResult {
    /**
     * 业务类型
     */
    private String businessType;
    /**
     * 描述
     */
    private String description;

    /**
     * 子状态配置列表
     */
    private List<SubStateConfiguration> subStateConfigurations;


    public StateConfigurationResult(String businessType, String description, List<SubStateConfiguration> subStateConfigurations) {
        this.businessType = businessType;
        this.description = description;
        this.subStateConfigurations = subStateConfigurations;
    }

    public String getBusinessType() {
        return businessType;
    }

    public void setBusinessType(String businessType) {
        this.businessType = businessType;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public List<SubStateConfiguration> getSubStateConfigurations() {
        return subStateConfigurations;
    }

    public void setSubStateConfigurations(List<SubStateConfiguration> subStateConfigurations) {
        this.subStateConfigurations = subStateConfigurations;
    }

    public static class SubStateConfiguration {
        /**
         * 子状态类型
         */
        private Integer subStateType;
        /**
         * 子状态描述
         */
        private String description;

        public SubStateConfiguration(Integer subStateType, String description) {
            this.subStateType = subStateType;
            this.description = description;
        }

        public Integer getSubStateType() {
            return subStateType;
        }

        public void setSubStateType(Integer subStateType) {
            this.subStateType = subStateType;
        }

        public String getDescription() {
            return description;
        }

        public void setDescription(String description) {
            this.description = description;
        }

        @Override
        public String toString() {
            return "SubStateConfiguration{" +
                    "subStateType=" + subStateType +
                    ", description='" + description + '\'' +
                    '}';
        }
    }

    @Override
    public String toString() {
        return "StateConfigurationResult{" +
                "businessType='" + businessType + '\'' +
                ", description='" + description + '\'' +
                ", subStateConfigurations=" + subStateConfigurations +
                '}';
    }
}
