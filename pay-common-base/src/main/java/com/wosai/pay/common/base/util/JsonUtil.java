package com.wosai.pay.common.base.util;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.wosai.pay.common.base.exception.JsonException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import java.io.IOException;


public class JsonUtil {
    private static Logger log = LoggerFactory.getLogger(JsonUtil.class);
    public static ObjectMapper objectMapper;

    static {
        objectMapper = new ObjectMapper();
        objectMapper.disable(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES);
        //允许出现特殊字符和转义符
        objectMapper.configure(JsonParser.Feature.ALLOW_UNQUOTED_CONTROL_CHARS, true);
    }

    @Deprecated
    /**
     * 使用jsonStringToObject代替
     */
    public static <T> T jsonStrToObject(String jsonStr, Class<T> valueType) {
        if (jsonStr == null || valueType == null) {
            return null;
        }
        T t = null;
        try {
            t = objectMapper.readValue(jsonStr, valueType);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        return t;

    }

    @Deprecated
    /**
     * 使用objectToJsonString代替
     */
    public static <T> String toJsonStr(T obj) {
        if (obj == null) {
            return null;
        }
        try {
            return objectMapper.writeValueAsString(obj);
        } catch (IOException e) {
            log.error(e.getMessage(), e);
        }
        return null;
    }

    public static <T> T jsonStringToObject(String jsonStr, Class<T> valueType) throws JsonException {
        if (jsonStr == null || valueType == null) {
            return null;
        }
        try {
            return objectMapper.readValue(jsonStr, valueType);
        } catch (Exception e) {
            throw new JsonException(e.getMessage(), e);
        }
    }

    public static <T> T jsonBytesToObject(byte [] jsonBytes, Class<T> valueType) throws JsonException {
        if (jsonBytes == null || valueType == null) {
            return null;
        }
        try {
            return objectMapper.readValue(jsonBytes, valueType);
        } catch (Exception e) {
            throw new JsonException(e.getMessage(), e);
        }
    }

    public static <T> T jsonStringToObject(String jsonStr, TypeReference<T> typeReference) throws JsonException {
        if (jsonStr == null || typeReference == null) {
            return null;
        }
        try {
            return objectMapper.readValue(jsonStr, typeReference);
        } catch (Exception e) {
            throw new JsonException(e.getMessage(), e);
        }
    }

    public static <T> String objectToJsonString(T obj) throws JsonException {
        if (obj == null) {
            return null;
        }
        try {
            return objectMapper.writeValueAsString(obj);
        } catch (IOException e) {
            throw new JsonException(e.getMessage(), e);
        }
    }


}