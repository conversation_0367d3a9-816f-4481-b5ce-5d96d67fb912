package com.wosai.pay.common.base.util;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;

import java.io.IOException;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;

/**
 * <AUTHOR>
 * @description
 * @date 2025/1/9
 */
public class LocalDateTimeFormatter {
    private static final DateTimeFormatter DATE_TIME = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    private static final DateTimeFormatter LOCAL_TIME = DateTimeFormatter.ofPattern("HH:mm");

    public static class LocalDateTimeSerializer extends JsonSerializer<LocalDateTime> {
        @Override
        public void serialize(LocalDateTime dateTime, JsonGenerator gen, SerializerProvider provider)
                throws IOException {
            if (dateTime == null) {
                return;
            }
            gen.writeString(DATE_TIME.format(dateTime));
        }

    }

    public static class LocalDateTimeDeserializer extends JsonDeserializer<LocalDateTime> {
        @Override
        public LocalDateTime deserialize(JsonParser parser, DeserializationContext ctxt) throws IOException {
            String text = parser.getText();
            if (text == null || text.isEmpty()) {
                return null;
            }

            return LocalDateTime.parse(text, DATE_TIME);
        }
    }

    public static class LocalTimeSerializer extends JsonSerializer<LocalTime> {
        @Override
        public void serialize(LocalTime localTime, JsonGenerator gen, SerializerProvider serializers) throws IOException {
            if (localTime == null) {
                return;
            }
            gen.writeString(LOCAL_TIME.format(localTime));
        }
    }

    public static class LocalTimeDeserializer extends JsonDeserializer<LocalTime> {
        @Override
        public LocalTime deserialize(JsonParser parser, DeserializationContext ctx) throws IOException {
            String text = parser.getText();
            if (text == null || text.isEmpty()) {
                return null;
            }

            return LocalTime.parse(text, LOCAL_TIME);
        }
    }
}
