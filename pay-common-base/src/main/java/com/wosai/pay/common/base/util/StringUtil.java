package com.wosai.pay.common.base.util;

import java.util.*;
import java.util.stream.Collectors;

public class StringUtil {

    @SafeVarargs
    public static <T> String join(String delimiter, T... fields) {
        return join(delimiter, null, null, fields);
    }

    public static String joinC(String delimiter, Collection<?> fields) {
        return joinC(delimiter, null, null, fields);
    }

    public static String join(String delimiter, String prefix, String postfix, Object... fields) {
        return joinC(delimiter, prefix, postfix, Arrays.asList(fields));
    }

    public static String joinC(
            String delimiter,
            String prefix,
            String postfix,
            Collection<?> fields
    ) {
        StringBuilder sb = new StringBuilder();
        for (Object field : fields) {
            sb.append(delimiter);
            if (prefix != null) {
                sb.append(prefix);
            }
            sb.append(field);
            if (postfix != null) {
                sb.append(postfix);
            }
        }
        return sb.substring(delimiter.length());
    }

    public static String repeat(String sep, String symbol, int count) {
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < count; ++i) {
            if (sb.length() > 0) {
                sb.append(sep);
            }
            sb.append(symbol);
        }
        return sb.toString();
    }

    public static boolean isEmpty(String str) {
        return str == null || str.length() == 0;
    }

    public static boolean isNotEmpty(String str) {
        return !isEmpty(str);
    }

    public static boolean isBlank(String s) {
        return s == null || s.isEmpty() || s.trim().isEmpty();
    }

    public static boolean isNotBlank(String str) {
        return !isBlank(str);
    }

    public static String map2String(Map<?, ?> map) {
        if (map == null) {
            return "" + null;
        }
        return "{" + map.entrySet()
                .stream()
                .map(entry -> {
                    String s = "";
                    if (entry.getKey() instanceof String) {
                        s += "\"" + entry.getKey() + "\"";
                    } else {
                        s += entry.getKey();
                    }
                    s += ": ";
                    if (entry.getValue() instanceof String) {
                        s += "\"" + entry.getValue() + "\"";
                    } else {
                        s += entry.getValue();
                    }
                    return s;
                })
                .collect(Collectors.joining(", "))
                + "}";
    }

    public static String list2String(List<?> list) {
        if (list == null) {
            return "" + null;
        }
        return "[" + list.stream()
                .map(entry -> {
                    if (entry instanceof String) {
                        return "\"" + entry + "\"";
                    }
                    return "" + entry;
                })
                .collect(Collectors.joining(", "))
                + "]";
    }


    public static String trim(String str) {
        return str == null ? null : str.trim();
    }

    public static String trimToNull(String str) {
        String ts = trim(str);
        return isEmpty(ts) ? null : ts;
    }

    public static String trimToEmpty(String str) {
        return str == null ? "" : str.trim();
    }

    public static boolean equals(String str1, String str2) {
        return str1 == null ? str2 == null : str1.equals(str2);
    }

    public static boolean equalsIgnoreCase(String str1, String str2) {
        return str1 == null ? str2 == null : str1.equalsIgnoreCase(str2);
    }

    public static String defaultString(String str) {
        return str == null ? "" : str;
    }

    public static String defaultString(String str, String defaultStr) {
        return str == null ? defaultStr : str;
    }

    public static String defaultIfEmpty(String str, String defaultStr) {
        return isEmpty(str) ? defaultStr : str;
    }

}
