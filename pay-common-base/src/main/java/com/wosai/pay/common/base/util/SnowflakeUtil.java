package com.wosai.pay.common.base.util;

import java.net.InetAddress;
import java.util.concurrent.ThreadLocalRandom;

/**
 * <AUTHOR>
 * @description 雪花算法ID生成器（集成workerId生成）
 * @date 2025-06-27
 */
public class SnowflakeUtil {
    private static volatile SnowflakeUtil instance;
    private final long epoch = 1288834974657L;
    private final long workerIdBits = 5L;
    private final long datacenterIdBits = 5L;
    private final long sequenceBits = 12L;
    private final long maxWorkerId = -1L ^ (-1L << workerIdBits);
    private final long maxDatacenterId = -1L ^ (-1L << datacenterIdBits);
    private final long workerIdShift = sequenceBits;
    private final long datacenterIdShift = sequenceBits + workerIdBits;
    private final long timestampShift = sequenceBits + workerIdBits + datacenterIdBits;

    private final long workerId;
    private final long datacenterId;
    private long sequence = 0L;
    private long lastTimestamp = -1L;

    private SnowflakeUtil() {
        this.workerId = generateWorkerId();
        this.datacenterId = generateDatacenterId();
    }

    /**
     * 获取id
     *
     * @return
     */
    public static long nextId() {
        return getInstance().generateId();
    }

    /**
     * 获取id（字符串形式）
     *
     * @return
     */
    public static String nextIdStr() {
        return Long.toString(nextId());
    }

    private static SnowflakeUtil getInstance() {
        if (instance == null) {
            synchronized (SnowflakeUtil.class) {
                if (instance == null) {
                    instance = new SnowflakeUtil();
                }
            }
        }
        return instance;
    }

    /**
     * 基于IP生成workerId
     */
    private long generateWorkerId() {
        try {
            String ip = InetAddress.getLocalHost().getHostAddress();
            if ("127.0.0.1".equals(ip)) {
                return getFallbackWorkerId();
            }
            return Math.abs(ip.hashCode()) % (maxWorkerId + 1);
        } catch (Exception e) {
            return getFallbackWorkerId();
        }
    }

    /**
     * 基于主机名生成datacenterId
     */
    private long generateDatacenterId() {
        try {
            String hostName = InetAddress.getLocalHost().getHostName();
            return Math.abs(hostName.hashCode()) % (maxDatacenterId + 1);
        } catch (Exception e) {
            return ThreadLocalRandom.current().nextInt((int) maxDatacenterId + 1);
        }
    }

    private long getFallbackWorkerId() {
        return ThreadLocalRandom.current().nextInt((int) maxWorkerId + 1);
    }

    private synchronized long generateId() {
        long timestamp = timeGen();
        if (timestamp < lastTimestamp) {
            throw new RuntimeException("时钟回拨异常");
        }
        if (lastTimestamp == timestamp) {
            sequence = (sequence + 1) & ((1 << sequenceBits) - 1);
            if (sequence == 0) {
                timestamp = tilNextMillis(lastTimestamp);
            }
        } else {
            sequence = 0L;
        }
        lastTimestamp = timestamp;
        return ((timestamp - epoch) << timestampShift)
                | (datacenterId << datacenterIdShift)
                | (workerId << workerIdShift)
                | sequence;
    }

    protected long tilNextMillis(long lastTimestamp) {
        long timestamp = timeGen();
        while (timestamp <= lastTimestamp) {
            timestamp = timeGen();
        }
        return timestamp;
    }

    protected long timeGen() {
        return System.currentTimeMillis();
    }
}