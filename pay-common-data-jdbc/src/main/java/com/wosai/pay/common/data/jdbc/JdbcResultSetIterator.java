package com.wosai.pay.common.data.jdbc;

import com.wosai.pay.common.data.PersistenceHelper;
import com.wosai.pay.common.data.Record;
import com.wosai.pay.common.data.exception.DaoCriticalException;
import com.wosai.pay.common.data.exception.DaoSystemException;
import org.springframework.jdbc.core.ColumnMapRowMapper;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.Iterator;
import java.util.Map;

public class JdbcResultSetIterator<T extends Record> implements Iterator<T> {

    private ResultSet rs;
    private boolean hasNext;
    private int row = 0;
    private ColumnMapRowMapper rowMapper = new ColumnMapRowMapper();
    private Class<T> clazz;
    private PersistenceHelper persistenceHelper;

    public JdbcResultSetIterator(
            ResultSet rs,
            Class<T> clazz,
            PersistenceHelper persistenceHelper
    ) {
        this.rs = rs;
        this.clazz = clazz;
        this.persistenceHelper = persistenceHelper;
        try {
            this.hasNext = rs.next();
        } catch (SQLException e) {
            this.hasNext = false;
        }
    }

    @Override
    public boolean hasNext() {
        return hasNext;
    }

    @Override
    public T next() {
        try {
            Map<String, Object> m = rowMapper.mapRow(rs, row++);
            this.hasNext = rs.next();
            return persistenceHelper.postRead(m, clazz);
        } catch (SQLException e) {
            throw new DaoSystemException("Unexpected exception iterating the result set", e);
        } catch (Exception e) {
            throw new DaoCriticalException("You may have a bug: unable to create instance of " + clazz.getName(), e);
        }
    }

}
