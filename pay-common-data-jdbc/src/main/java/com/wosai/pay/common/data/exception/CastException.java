package com.wosai.pay.common.data.exception;

import com.wosai.pay.common.base.exception.BaseException;

public class CastException extends BaseException {

    private Object from;
    private Class<?> toClazz;

    public CastException(Object from, Class<?> toClazz) {
        super("cannot cast " + from.getClass().getName() + " to " + toClazz.getName());
        this.from = from;
        this.toClazz = toClazz;
    }

    public Object from() {
        return from;
    }

    public Class<?> toClazz() {
        return toClazz;
    }

}
