package com.wosai.pay.common.data.util;

import java.util.Arrays;
import java.util.Collections;
import java.util.LinkedHashSet;
import java.util.Set;

public class SetUtil {

    @SafeVarargs
    public static <T> Set<T> copyInclusive(Set<T> from, T... elements) {
        Set<T> to = new LinkedHashSet<>();
        for (T elem : elements) {
            if (from.contains(elem)) {
                to.add(elem);
            }
        }
        return to;
    }

    @SafeVarargs
    public static <T> Set<T> copyExclusive(Set<T> from, T... elements) {
        Set<T> to = new LinkedHashSet<>(from);
        for (T elem : elements) {
            to.remove(elem);
        }
        return to;
    }

    @SafeVarargs
    public static <T> Set<T> hashSet(T... elements) {
        return new LinkedHashSet<>(Arrays.asList(elements));
    }

    @SafeVarargs
    public static <T> void addAll(Set<T> s, T... elements) {
        Collections.addAll(s, elements);
    }

}
