package com.wosai.pay.common.data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.List;

public class Criteria implements Serializable {

    public static final String KEY_AND = "$and";
    public static final String KEY_OR = "$or";
    public static final String KEY_GT = "$gt";
    public static final String KEY_GTE = "$gte";
    public static final String KEY_LT = "$lt";
    public static final String KEY_LTE = "$lte";
    public static final String KEY_NE = "$ne";
    public static final String KEY_IN = "$in";
    public static final String KEY_NIN = "$nin";
    public static final String KEY_LIKE = "$like";
    public static final String KEY_ELEM_MATCH = "$elemMatch";

    private String key;
    private Object value;
    private List<Criteria> chain;

    public Criteria() {
        this(new ArrayList<>(), null);
    }

    public Criteria(String key) {
        this(new ArrayList<>(), key);
    }

    public Criteria(String key, Object value) {
        this(key);
        this.value = value;
    }

    private Criteria(List<Criteria> chain, String key) {
        this.chain = chain;
        this.chain.add(this);
        this.key = key;
    }

    public static Criteria where(String key) {
        return new Criteria(key);
    }

    public static Criteria and(Criteria... criteria) {
        return new Criteria(KEY_AND, Arrays.asList(criteria));
    }

    public static Criteria and(Collection<Criteria> criteria) {
        return new Criteria(KEY_AND, criteria);
    }

    public static Criteria or(Criteria... criteria) {
        return or(Arrays.asList(criteria));
    }

    public static Criteria or(Collection<Criteria> criteria) {
        return new Criteria(KEY_OR, criteria);
    }

    public Criteria is(Object value) {
        this.value = value;
        return this;
    }

    public Criteria gt(Object value) {
        if (this.value instanceof Criteria) {
            ((Criteria) this.value).with(KEY_GT).is(value);
        } else {
            this.value = new Criteria(KEY_GT, value);
        }
        return this;
    }

    public Criteria ge(Object value) {
        if (this.value instanceof Criteria) {
            ((Criteria) this.value).with(KEY_GTE).is(value);
        } else {
            this.value = new Criteria(KEY_GTE, value);
        }
        return this;
    }

    public Criteria lt(Object value) {
        if (this.value instanceof Criteria) {
            ((Criteria) this.value).with(KEY_LT).is(value);
        } else {
            this.value = new Criteria(KEY_LT, value);
        }
        return this;
    }

    public Criteria le(Object value) {
        if (this.value instanceof Criteria) {
            ((Criteria) this.value).with(KEY_LTE).is(value);
        } else {
            this.value = new Criteria(KEY_LTE, value);
        }
        return this;
    }

    public Criteria ne(Object value) {
        if (this.value instanceof Criteria) {
            ((Criteria)this.value).with(KEY_NE).is(value);
        } else {
            this.value = new Criteria(KEY_NE, value);
        }
        return this;
    }

    public Criteria elemMatch(Object value) {
        this.value = new Criteria(KEY_ELEM_MATCH, value);
        return this;
    }

    public Criteria like(Object value) {
        this.value = new Criteria(KEY_LIKE, value);
        return this;
    }

    public Criteria in(Object... values) {
        this.value = new Criteria(KEY_IN, Arrays.asList(values));
        return this;
    }

    public Criteria in(Collection<?> values) {
        this.value = new Criteria(KEY_IN, values);
        return this;
    }

    public Criteria nin(Object... values) {
        this.value = new Criteria(KEY_NIN, Arrays.asList(values));
        return this;
    }

    public Criteria nin(Collection<?> values) {
        this.value = new Criteria(KEY_NIN, values);
        return this;
    }

    public Criteria with(String field) {
        if (field == null || field.isEmpty()) {
            throw new IllegalArgumentException("Field name must not be empty");
        }
        if (this.key == null) {
            this.key = field;
            return this;
        } else {
            return new Criteria(this.chain, field);
        }
    }

    public Criteria withAnd(Criteria... criteria) {
        this.chain.add(new Criteria(KEY_AND, Arrays.asList(criteria)));
        return this;
    }

    public Criteria withAnd(Collection<Criteria> criteria) {
        this.chain.add(new Criteria(KEY_AND, criteria));
        return this;
    }

    public Criteria withOr(Criteria... criteria) {
        this.chain.add(new Criteria(KEY_OR, Arrays.asList(criteria)));
        return this;
    }

    public Criteria withOr(Collection<Criteria> criteria) {
        this.chain.add(new Criteria(KEY_OR, criteria));
        return this;
    }

    public List<Criteria> chain() {
        return this.chain;
    }

    public String key() {
        return key;
    }

    public Object value() {
        return value;
    }

    public Criteria join(Criteria criteria) {
        for (Criteria c : criteria.chain) {
            c.chain = this.chain;
            this.chain.add(c);
        }
        return criteria;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("{");

        for (Criteria c : this.chain()) {
            sb.append(c.key()).append(":").append(c.value()).append(",");
        }

        sb.append("}");
        return sb.toString();
    }

}
