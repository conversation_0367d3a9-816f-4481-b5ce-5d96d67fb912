package com.wosai.pay.common.data;

public abstract class VersionedRecord<ID> extends Record<ID> {

    public static final String VERSION = "version";
    public static final String CTIME = "ctime";
    public static final String MTIME = "mtime";

    private Long version;
    private Long ctime;
    private Long mtime;

    public Long getVersion() {
        return version;
    }

    public void setVersion(Long version) {
        this.version = version;
    }

    public Long getCtime() {
        return ctime;
    }

    public void setCtime(Long ctime) {
        this.ctime = ctime;
    }

    public Long getMtime() {
        return mtime;
    }

    public void setMtime(Long mtime) {
        this.mtime = mtime;
    }
}
