package com.wosai.pay.common.data;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.wosai.pay.common.data.exception.PersistenceException;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

public class Jackson2PersistenceHelper implements PersistenceHelper {

    private final ObjectMapper om;

    public Jackson2PersistenceHelper() {
        om = new ObjectMapper();
        om.setPropertyNamingStrategy(PropertyNamingStrategy.SNAKE_CASE);
        om.configure(com.fasterxml.jackson.databind.DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
    }

    public Object fromJsonBytes(byte[] bytes, Class<?> clazz) {
        try {
            return om.readValue(bytes, clazz);
        } catch (IOException e) {
            throw new PersistenceException(String.format("unable to read %s from json bytes.", clazz.getName()), e);
        }
    }

    public byte[] toJsonBytes(Object value) {
        try {
            return om.writeValueAsBytes(value);
        } catch (JsonProcessingException e) {
            throw new PersistenceException(String.format("unable to write %s to json bytes.", value.getClass().getName()), e);
        }
    }

    public String toJsonString(Object value) {
        try {
            return om.writeValueAsString(value);
        } catch (JsonProcessingException e) {
            throw new PersistenceException(String.format("unable to write %s to json string.", value.getClass().getName()), e);
        }
    }

    @Override
    public <T extends Record<?>> T postRead(Map<String, Object> row, Class<T> clazz) {
        // Convert column names to lowercase to match Jackson's expectations
        // TODO 性能优化
        Map<String, Object> normalizedRow = new HashMap<>();
        for (Map.Entry<String, Object> entry : row.entrySet()) {
            String field = entry.getKey().toLowerCase();
            Object value = entry.getValue();
            if (value instanceof byte[]) {
                value = fromJsonBytes((byte[]) value, Map.class);
            }
            normalizedRow.put(field, value);
        }
        return om.convertValue(normalizedRow, clazz);
    }

    @Override
    public Map<String, Object> preWrite(Record record) {
        Map<String,Object> row = om.convertValue(record, Map.class);
        for (String field : row.keySet()) {
            Object value = row.get(field);
            if (value instanceof Map) {
                row.put(field, toJsonBytes(value));
            }
        }
        return row;
    }

}
