package com.wosai.pay.common.data;


import java.util.Iterator;
import java.util.List;
import java.util.OptionalInt;

public interface Filter<R extends Record> {

    int ASC = 1;
    int DESC = -1;

    Iterator<R> iterator();

    List<R> fetchAll();

    R fetchOne();

    long count();

    Filter<R> offset(int offset);

    OptionalInt offset();

    Filter<R> limit(int limit);

    OptionalInt limit();

    Filter<R> orderBy(String fieldName, int dir);

    Filter<R> orderBy(List<OrderByField> fields);

    class OrderByField {

        private String name;
        // 1: ASC；-1：DESC
        private int dir;

        public OrderByField(String name, int dir) {
            this.name = name;
            this.dir = dir;
        }

        public String name() {
            return name;
        }

        public int dir() {
            return dir;
        }

        @Override
        public String toString() {
            return name + ":" + dir;
        }

    }

}
