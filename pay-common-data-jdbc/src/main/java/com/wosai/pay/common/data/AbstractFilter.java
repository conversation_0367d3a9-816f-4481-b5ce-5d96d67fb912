package com.wosai.pay.common.data;


import java.util.*;

public abstract class AbstractFilter<B extends Record> implements Filter<B> {

    protected OptionalInt offset = OptionalInt.empty();
    protected OptionalInt limit = OptionalInt.empty();
    protected List<OrderByField> orderBy;

    @Override
    public AbstractFilter<B> offset(int offset) {
        this.offset = OptionalInt.of(offset);
        return this;
    }

    @Override
    public OptionalInt offset() {
        return offset;
    }

    @Override
    public AbstractFilter<B> limit(int limit) {
        this.limit = OptionalInt.of(limit);
        return this;
    }

    @Override
    public OptionalInt limit() {
        return limit;
    }

    @Override
    public AbstractFilter<B> orderBy(String fieldName, int dir) {
        orderBy = Collections.singletonList(new OrderByField(fieldName, dir));
        return this;
    }

    @Override
    public AbstractFilter<B> orderBy(List<OrderByField> fields) {
        orderBy = fields;
        return this;
    }

}
