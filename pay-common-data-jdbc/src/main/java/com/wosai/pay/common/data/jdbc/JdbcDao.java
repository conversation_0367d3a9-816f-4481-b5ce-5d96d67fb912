package com.wosai.pay.common.data.jdbc;


import com.wosai.pay.common.base.util.MapUtil;
import com.wosai.pay.common.base.util.StringUtil;
import com.wosai.pay.common.data.*;
import com.wosai.pay.common.data.exception.*;
import com.wosai.pay.common.data.util.SetUtil;
import org.springframework.dao.DataAccessException;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.dao.TransientDataAccessException;
import org.springframework.jdbc.core.ColumnMapRowMapper;
import org.springframework.jdbc.core.JdbcOperations;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.jdbc.core.namedparam.MapSqlParameterSource;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;
import org.springframework.jdbc.support.GeneratedKeyHolder;
import org.springframework.jdbc.support.KeyHolder;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.*;
import java.util.regex.Pattern;

public abstract class JdbcDao<ID, R extends Record<ID>> implements Dao<ID, R> {
    public static final String MYSQL_DB_OBJECT_QUOTE = "`";

    private final String tableName;
    private final NamedParameterJdbcTemplate namedParameterJdbcTemplate;
    private String dbObjectQuote = "";
    private final PersistenceHelper persistenceHelper;
    private final Class<R> entityClass;

    public JdbcDao(
            String tableName,
            Class<R> entityClass,
            NamedParameterJdbcTemplate namedParameterJdbcTemplate,
            PersistenceHelper persistenceHelper

    ) {
        this(tableName, entityClass, MYSQL_DB_OBJECT_QUOTE, namedParameterJdbcTemplate, persistenceHelper);
    }

    /* Constructors */

    public JdbcDao(
            String tableName,
            Class<R> entityClass,
            String dbObjectQuote,
            NamedParameterJdbcTemplate namedParameterJdbcTemplate,
            PersistenceHelper persistenceHelper
    ) {
        this.tableName = tableName;
        this.entityClass = entityClass;
        this.dbObjectQuote = dbObjectQuote;
        this.namedParameterJdbcTemplate = namedParameterJdbcTemplate;
        this.persistenceHelper = persistenceHelper;
    }



    /* APIs */

    @Override
    public void insert(R record) {
        makeInsert(record);
        Map<String, Object> insert = persistenceHelper.preWrite(record);
        
        // Handle auto-increment ID: exclude ID if it's null
        boolean isAutoIncrement = record.getId() == null;
        if (isAutoIncrement) {
            insert.remove(primaryKeyColumnName());
        }
        
        String sql = buildInsertSql(insert);
        
        if (isAutoIncrement) {
            // Use generated key for auto-increment
            ID generatedId = doInsertWithGeneratedKey(sql, insert);
            // Set the generated ID back to the record
            record.setId(generatedId);
        } else {
            // Original behavior for explicit ID
            checkPrimaryKey(record);
            doUpdate(sql, insert);
        }
    }

    @Override
    public R get(ID key) {
        checkPrimaryKeyIsNotNull(key);

        String sql = buildSelectSql();

        return doGet(sql, key, entityClass);
    }

    @Override
    public R get(ID key, String... columns) {
        checkPrimaryKeyIsNotNull(key);

        Set<String> includes = SetUtil.hashSet(columns);
        SetUtil.addAll(includes, selectIncludes());

        String sql = buildSelectSql(includes.toArray(new String[includes.size()]));
        return doGet(sql, key, entityClass);
    }

    @Override
    public Filter<R> filter(Criteria criteria) {
        return new JdbcFilter(criteria, null);
    }

    @Override
    public Filter<R> filter(Criteria criteria, String... columns) {
        Set<String> includes = SetUtil.hashSet(columns);
        SetUtil.addAll(includes, selectIncludes());
        return new JdbcFilter(criteria, includes);
    }

    @Override
    public void update(R record) {
        doUpdate(record);
    }

    @Override
    public void update(R record, String... columns) {
        doUpdate(record, columns);
    }

    private  void doUpdate(R record, String... columns) {
        checkPrimaryKey(record);
        makeUpdate(record);
        Map<String, Object> data = persistenceHelper.preWrite(record);
        Map<String, Object> update = data;
        if(columns.length != 0){
            update = MapUtil.copyInclusive(data, columns);
            update = MapUtil.copyExclusive(update, updateExcludes());
        }
        String sql = buildUpdateSql(update, data);
        doPostUpdate(doUpdate(sql, data), update);
        updateOriginalRecordAfterUpdate(record);
    }

    /**
     *  Reload the record after update to reflect changes
     * @param record
     */
    protected void updateOriginalRecordAfterUpdate(R record) {
    }



    @Override
    public void delete(ID key) {
        checkPrimaryKeyIsNotNull(key);
        String sql = buildDeleteSql();
        int rowsAffected = doUpdate(sql, MapUtil.hashMap(primaryKeyColumnName(), key));
        possibleDeleteExceptions(rowsAffected);
    }

    /* Internals */


    protected String primaryKeyColumnName() {
        return Record.ID;
    }

    protected  void checkPrimaryKeyIsNotNull(ID id) {
        if(id == null) {
            throw new DaoMissingPrimaryKey("primary key column is null.");
        }
    }

    protected  void checkPrimaryKey(R record) {
        if(record == null || record.getId() == null) {
            throw new DaoMissingPrimaryKey("primary key column is null.");
        }
    }

    protected String[] selectIncludes() {
        return new String[] { primaryKeyColumnName() };
    }

    protected String quote(String name) {
        return String.format(
                "%s%s%s",
                dbObjectQuote,
                name,
                dbObjectQuote
        );
    }

    /* Internals - Insert */

    protected  void makeInsert(R record) {
        insertSystemColumns(record);
    }

    protected String[] insertExcludes() {
        return new String[] {};
    }

    protected  void insertSystemColumns(R record) {

    }

    protected String buildInsertSql(Map<String, Object> data) {
        Set<String> keys = data.keySet();
        String columnList = StringUtil.joinC(", ", dbObjectQuote, dbObjectQuote, keys);
        String valuePlaceHolderList = StringUtil.joinC(", ", ":", null, keys);

        return String.format(
                "insert into %s ( %s ) values ( %s )",
                quote(tableName),
                columnList,
                valuePlaceHolderList
        );
    }

    /* Internals - Get */

    protected  R doGet(String sql, ID key, Class<R> clazz) {
        try {
            List<Map<String, Object>> results = namedParameterJdbcTemplate.queryForList(
                    sql,
                    MapUtil.hashMap(primaryKeyColumnName(), key)
            );
            int size = results.size();
            if (size == 1) {
                Map<String, Object> m = results.get(0);
                return persistenceHelper.postRead(m, clazz);
            }else if(size > 1){
                throw new DaoIntegrityViolation("Multiple records found for primary key");
            }else{
                return null;
            }
        } catch (TransientDataAccessException ex) {
            throw new DaoSystemException("Transient data access problem", ex);
        } catch (DataAccessException ex) {
            throw new DaoCriticalException("You may have a bug", ex);
        } catch (Exception e){
            throw new DaoCriticalException("You may have a bug: unable to create an instance of " + clazz.getName(), e);
        }
    }

    protected String buildSelectSql() {
        return String.format("select * from %s where %s", quote(tableName), byPkWhereClause());
    }

    protected String buildSelectSql(String... columns) {
        String selectColumnList = StringUtil.join(
                ", ",
                dbObjectQuote,
                dbObjectQuote,
                (Object[])columns
        );
        return String.format(
                "select %s from %s where %s",
                selectColumnList,
                quote(tableName),
                byPkWhereClause()
        );
    }

    protected String byPkWhereClause() {
        String pk = primaryKeyColumnName();
        return String.format("%s = :%s", quote(pk), pk);
    }

    /* Internals - Update */

    protected  void makeUpdate(R record) {
        updateSystemColumns(record);
    }


    protected String[] updateExcludes() {
        return new String[] { primaryKeyColumnName() };
    }

    protected  void  updateSystemColumns(R record) {

    }

    protected String buildUpdateSql(Map<String, Object> update, Map<String, Object> data) {
        String setList = StringUtil.joinC(", ", makeSetList(update));
        return String.format(
                "update %s set %s where %s",
                quote(tableName),
                setList,
                updateWhereClause(update, data)
        );
    }

    protected int doUpdate(String sql, Map<String, Object> update) {
        try {
            return namedParameterJdbcTemplate.update(sql, update);
        } catch(TransientDataAccessException ex) {
            throw new DaoSystemException("Unexpected transient database exception", ex);
        } catch(DataIntegrityViolationException ex) {
            throw new DaoIntegrityViolation("Integrity violation", ex);
        } catch(DataAccessException ex) {
            throw new DaoCriticalException("You may have a bug", ex);
        }
    }

    protected void doPostUpdate(int rowsAffected, Map<String, Object> update) {
        if (rowsAffected < 1) {
            List<Map<String, Object>> updateCandidates = namedParameterJdbcTemplate.queryForList(
                    buildSelectSql(selectIncludes()),
                    update
            );
            possibleUpdateExceptions(updateCandidates, update);
        }
    }

    protected void possibleUpdateExceptions(
            List<Map<String, Object>> updateCandidates,
            Map<String, Object> update
    ) {
        if (updateCandidates.size() == 0) {
            throw new DaoRecordNotExists("Record for this primary key does not exist");
        }
    }

    protected void possibleDeleteExceptions(int rowsAffected) {
        if (rowsAffected < 1) {
            throw new DaoRecordNotExists("Record for this primary key does not exist");
        }
    }

    protected List<String> makeSetList(Map<String, Object> data) {
        List<String> setList = new ArrayList<>();
        Set<String> excludes = new HashSet<>(Arrays.asList(updateExcludes()));
        for (String key : data.keySet()) {
            if (!excludes.contains(key)) {
                setList.add(String.format("%s = :%s", quote(key), key));
            }
        }
        setComputedSystemColumns(setList);
        return setList;
    }

    protected void setComputedSystemColumns(List<String> setList) {

    }

    protected String updateWhereClause(Map<String, Object> update, Map<String, Object> data) {
        String pk = primaryKeyColumnName();
        MapUtil.addKeysIfNotExist(data, update, pk);
        return String.format("%s = :%s", quote(pk), pk);
    }
    
    protected ID doInsertWithGeneratedKey(String sql, Map<String, Object> parameters) {
        try {
            KeyHolder keyHolder = new GeneratedKeyHolder();
            namedParameterJdbcTemplate.update(sql, new MapSqlParameterSource(parameters), keyHolder);
            return (ID) keyHolder.getKey();
        } catch (Exception e) {
            throw new DaoSystemException("Failed to insert record with generated key", e);
        }
    }

    /* Internals - Delete */

    protected String buildDeleteSql() {
        return String.format(
                "delete from %s where %s = :%s",
                quote(tableName),
                quote(primaryKeyColumnName()),
                primaryKeyColumnName()
        );
    }

    public class JdbcFilter extends AbstractFilter<R> {

        private JdbcOperations jdbcOperations;
        private Set<String> includes;
        private Criteria criteria;
        private WhereClause whereClause;

        public JdbcFilter(Criteria criteria, Set<String> includes) {
            this.jdbcOperations = namedParameterJdbcTemplate.getJdbcOperations();
            this.includes = includes;
            this.criteria = criteria;
            this.whereClause = buildWhereClause(criteria);
        }

        private WhereClause buildJointClause(String op, List<Criteria> criteria) {
            StringBuilder sb = new StringBuilder();
            List<Object> args = new ArrayList<>();

            for (Criteria c : criteria) {
                WhereClause subClause = buildWhereClauseForKeyValue(c.key(), c.value());
                if (subClause == null) {
                    if ("or".equals(op)) {
                        return null;
                    } else {
                        continue;
                    }
                }

                if (sb.length() > 0) {
                    sb.append(" ").append(op).append(" ");
                }
                sb.append("(").append(subClause.getClause()).append(")");
                args.addAll(subClause.getArgs());
            }
            return new WhereClause(sb.toString(), args);
        }

        private WhereClause buildWhereClauseForKeyValue(final String key, final Object value) {
            switch (key) {
                case Criteria.KEY_AND:
                    return buildJointClause("and", (List<Criteria>) value);
                case Criteria.KEY_OR:
                    return buildJointClause("or", (List<Criteria>) value);
                case Criteria.KEY_GT:
                    return new WhereClause(" > ? ", Collections.singletonList(value));
                case Criteria.KEY_GTE:
                    return new WhereClause(" >= ? ", Collections.singletonList(value));
                case Criteria.KEY_LT:
                    return new WhereClause(" < ? ", Collections.singletonList(value));
                case Criteria.KEY_LTE:
                    return new WhereClause(" <= ? ", Collections.singletonList(value));
                case Criteria.KEY_ELEM_MATCH:
                    return null;
                case Criteria.KEY_LIKE:
                    return new WhereClause(" like ? ", Collections.singletonList(value));
                case Criteria.KEY_IN: {
                    List<Object> args = new ArrayList<>((Collection<?>) value);
                    return new WhereClause(
                            " in ("
                                    + StringUtil.repeat(",", "?", ((Collection<?>) value).size())
                                    + ")",
                            args
                    );
                }
                case Criteria.KEY_NIN: {
                    List<Object> args = new ArrayList<>((Collection<?>) value);
                    return new WhereClause(
                            " not in ("
                                    + StringUtil.repeat(",", "?", ((Collection<?>) value).size())
                                    + ")",
                            args
                    );
                }
                case Criteria.KEY_NE: {
                    if (value == null) {
                        return new WhereClause(" is not null ", Collections.emptyList());
                    } else {
                        return new WhereClause(" <> ? ", Collections.singletonList(value));
                    }
                }
                default: {
                    if (value instanceof Pattern) {
                        // TODO: ?
                        return null;
                    } else if (value instanceof Criteria) {
                        Criteria c = (Criteria) value;
                        WhereClause clause = buildWhereClauseForKeyValue(c.key(), c.value());
                        // FIXME: fix NPE
                        return new WhereClause(
                                quote(key) + clause.getClause(),
                                clause.getArgs()
                        );
                    } else {
                        if (value == null) {
                            return new WhereClause(
                                    quote(key) + " is null ",
                                    Collections.emptyList()
                            );
                        } else {
                            return new WhereClause(
                                    quote(key) + " = ? ",
                                    Collections.singletonList(value)
                            );
                        }
                    }
                }
            }
        }

        private WhereClause buildWhereClause(Criteria criteria) {
            if (criteria == null) {
                return null;
            }
            int sz = criteria.chain().size();
            if (sz > 1) {
                return buildJointClause("and", criteria.chain());
            } else if (sz == 1) {
                if (criteria.key() == null) {
                    return null;
                }
                return buildWhereClauseForKeyValue(criteria.key(), criteria.value());
            } else {
                return null;
            }
        }

        private class WhereClause {

            private String clause;
            List<Object> args;

            WhereClause(String clause, List<Object> args) {
                this.clause = clause;
                this.args = args;
            }

            public String getClause() {
                return clause;
            }

            public void setClause(String clause) {
                this.clause = clause;
            }

            public List<Object> getArgs() {
                return args;
            }

            public void setArgs(List<Object> args) {
                this.args = args;
            }

        }

        protected String buildCountQuery() {
            StringBuilder sb = new StringBuilder();
            sb.append("select count(")
                    .append(primaryKeyColumnName())
                    .append(") from ")
                    .append(quote(tableName));
            if (whereClause != null) {
                sb.append(" where ").append(whereClause.getClause());
            }
            return sb.toString();
        }

        protected String buildQuery(boolean fetchOne) {
            StringBuilder sb = new StringBuilder();
            sb.append("select ");
            if (includes != null) {
                String[] cols = new String[includes.size()];
                int c = 0;
                for (String prop : includes) {
                    cols[c] = quote(prop);
                    c++;
                }
                sb.append(StringUtil.join(", ", cols));
            } else {
                sb.append(" * ");
            }
            sb.append(" from ").append(quote(tableName));
            if (whereClause != null) {
                sb.append(" where ").append(whereClause.getClause());
            }

            if (this.orderBy != null && !this.orderBy.isEmpty()) {
                sb.append(" order by ");
                boolean firstField = true;
                for (OrderByField obf : this.orderBy) {
                    if (firstField) {
                        firstField = false;
                    } else {
                        sb.append(", ");
                    }

                    sb.append(quote(obf.name()));
                    sb.append(" ");
                    sb.append(obf.dir() == 1 ? "asc" : "desc");
                }
            }

            if (fetchOne) {
                sb.append(" limit 1");
            } else {
                if (limit().isPresent()) {
                    if (offset().isPresent()) {
                        sb.append(" limit ")
                                .append(offset().getAsInt())
                                .append(", ")
                                .append(limit().getAsInt());
                    } else {
                        sb.append(" limit ").append(limit().getAsInt());
                    }
                } else {
                    // TODO have a default limit?
                }
            }
            return sb.toString();
        }

        @Override
        public Iterator<R> iterator() {
            String sql = buildQuery(false);
            try {
                return jdbcOperations.query(
                        sql,
                        whereClause == null ? null : whereClause.getArgs().toArray(),
                        rs -> {
                            return new JdbcResultSetIterator<>(rs, entityClass, persistenceHelper);
                        }
                );
            } catch (TransientDataAccessException ex) {
                throw new DaoSystemException("Unexpected transient data access problem", ex);
            } catch (DataAccessException ex) {
                throw new DaoCriticalException("You may have a bug", ex);
            }
        }

        @Override
        public List<R> fetchAll() {
            String sql = buildQuery(false);
            return doFetch(sql);
        }

        @Override
        public R fetchOne() {
            String sql = buildQuery(true);
            List<R> resultList = doFetch(sql);

            if (resultList.isEmpty()) {
                return null;
            } else {
                return resultList.get(0);
            }
        }

        protected List<R> doFetch(String sql) {
            try {
                RowMapper<R> rowMapper = new RowMapper<R>() {
                    ColumnMapRowMapper cmrm = new ColumnMapRowMapper();

                    @Override
                    public R mapRow(ResultSet rs, int rowNum) throws SQLException {
                        Map<String, Object> m = cmrm.mapRow(rs, rowNum);
                        return persistenceHelper.postRead(m, entityClass);
                    }
                };
                return jdbcOperations.query(
                        sql,
                        whereClause == null ? null : whereClause.getArgs().toArray(),
                        rowMapper
                );
            } catch (TransientDataAccessException ex) {
                throw new DaoSystemException("Unexpected transient data access problem", ex);
            } catch (DataAccessException ex) {
                throw new DaoCriticalException("You may have a bug", ex);
            }catch (Exception e){
                throw new DaoCriticalException("You may have a bug: unable to create instance of " + entityClass.getName(), e);
            }
        }

        @Override
        public long count() {
            String sql = buildCountQuery();
            try {
                Number number = jdbcOperations.queryForObject(
                        sql,
                        whereClause == null ? null : whereClause.getArgs().toArray(),
                        Integer.class
                );
                return number != null ? number.intValue() : 0;
            } catch (TransientDataAccessException ex) {
                throw new DaoSystemException("Unexpected transient data access problem", ex);
            } catch (DataAccessException ex) {
                throw new DaoCriticalException("You may have a bug", ex);
            }
        }

    }

}
