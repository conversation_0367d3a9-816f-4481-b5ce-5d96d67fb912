package com.wosai.pay.common.data.jdbc;

import com.wosai.pay.common.base.util.MapUtil;
import com.wosai.pay.common.data.PersistenceHelper;
import com.wosai.pay.common.data.VersionedRecord;
import com.wosai.pay.common.data.exception.DaoRecordVersionMismatch;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;

import java.util.List;
import java.util.Map;

public class JdbcVersionedRecordDao<ID, R extends VersionedRecord<ID>> extends JdbcDao<ID, R> {

    public JdbcVersionedRecordDao(String tableName, Class<R> entityClass, String dbObjectQuote, NamedParameterJdbcTemplate namedParameterJdbcTemplate, PersistenceHelper persistenceHelper) {
        super(tableName, entityClass, dbObjectQuote, namedParameterJdbcTemplate, persistenceHelper);
    }

    @Override
    protected String[] insertExcludes() {
        return new String[] {
                VersionedRecord.CTIME,
                VersionedRecord.MTIME,
                VersionedRecord.VERSION
        };
    }


    @Override
    protected void insertSystemColumns(R record) {
        long now = System.currentTimeMillis();
        record.setCtime(now);
        record.setMtime(now);
        record.setVersion(1L);
    }

    @Override
    protected String[] updateExcludes() {
        return new String[] {
                primaryKeyColumnName(),
                VersionedRecord.CTIME,
                VersionedRecord.MTIME,
                VersionedRecord.VERSION
        };
    }



    @Override
    protected void  updateSystemColumns(R record) {
        record.setMtime(System.currentTimeMillis());
    }

    @Override
    protected void setComputedSystemColumns(List<String> setList) {
        setList.add(
                String.format(
                        "%s = %s + 1",
                        quote(VersionedRecord.VERSION),
                        quote(VersionedRecord.VERSION)
                )
        );
    }

    @Override
    protected String updateWhereClause(Map<String, Object> update, Map<String, Object> data) {
        String pk = primaryKeyColumnName();
        MapUtil.addKeysIfNotExist(data, update, pk, VersionedRecord.VERSION);
        if (data.containsKey(VersionedRecord.VERSION)) {
            return String.format(
                    "%s = :%s and %s = :%s",
                    quote(pk),
                    pk,
                    quote(VersionedRecord.VERSION),
                    VersionedRecord.VERSION
            );
        } else {
            return String.format("%s = :%s", quote(pk), pk);
        }
    }

    @Override
    protected void updateOriginalRecordAfterUpdate(R record) {
        R reloaded = get(record.getId());
        if (reloaded != null) {
            record.setCtime(reloaded.getCtime());
            record.setMtime(reloaded.getMtime());
            record.setVersion(reloaded.getVersion());
        }
    }

    @Override
    protected String[] selectIncludes() {
        return new String[] {
                primaryKeyColumnName(),
                VersionedRecord.CTIME,
                VersionedRecord.MTIME,
                VersionedRecord.VERSION
        };
    }

    @Override
    protected void possibleUpdateExceptions(List<Map<String, Object>> updateCandidates, Map<String, Object> update) {
        super.possibleUpdateExceptions(updateCandidates, update);
        if (update.containsKey(VersionedRecord.VERSION) && !updateCandidates.isEmpty()) {
            Number baseVersion = (Number)update.get(VersionedRecord.VERSION);
            Number actualVersion = (Number)updateCandidates.get(0).get(VersionedRecord.VERSION);
            if (actualVersion == null || actualVersion.longValue() != baseVersion.longValue()) {
                throw new DaoRecordVersionMismatch("the actual record version differs from your base " + baseVersion);
            }
        }
    }
}
