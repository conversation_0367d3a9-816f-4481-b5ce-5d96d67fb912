package com.wosai.pay.common.data.jdbc;

import com.wosai.pay.common.data.Criteria;
import com.wosai.pay.common.data.exception.DaoMissingPrimaryKey;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.util.List;

import static org.junit.Assert.*;

/**
 * Comprehensive test class for auto-increment ID support
 */
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(classes = TestConfig.class)
public class AutoIncrementDaoTest {

    @Autowired
    private AutoIncrementUserDao autoIncrementUserDao;

    @Autowired
    private NamedParameterJdbcTemplate jdbcTemplate;

    @Before
    public void setUp() {
        // Create table with auto-increment ID
        jdbcTemplate.getJdbcOperations().execute("DROP TABLE auto_increment_user IF EXISTS");
        jdbcTemplate.getJdbcOperations().execute(
                "CREATE TABLE auto_increment_user (" +
                "    id BIGINT GENERATED BY DEFAULT AS IDENTITY (START WITH 1 INCREMENT BY 1) PRIMARY KEY," +
                "    name VARCHAR(100)," +
                "    email VARCHAR(100)," +
                "    age INTEGER," +
                "    ctime BIGINT," +
                "    mtime BIGINT," +
                "    version BIGINT" +
                ")"
        );
    }

    @After
    public void tearDown() {
        jdbcTemplate.getJdbcOperations().execute("DROP TABLE auto_increment_user IF EXISTS");
    }

    @Test
    public void testInsert_WithAutoIncrementId_ShouldGenerateId() {
        // Given
        AutoIncrementUser user = new AutoIncrementUser("Alice", "<EMAIL>", 25);
        assertNull("ID should be null initially", user.getId());

        // When
        autoIncrementUserDao.insert(user);

        // Then
        assertNotNull("ID should be generated", user.getId());
        assertTrue("ID should be positive", user.getId() > 0);
        
        // Verify the record was actually inserted
        AutoIncrementUser retrieved = autoIncrementUserDao.get(user.getId());
        assertNotNull("Retrieved record should not be null", retrieved);
        assertEquals("Name should match", "Alice", retrieved.getName());
        assertEquals("Email should match", "<EMAIL>", retrieved.getEmail());
        assertEquals("Age should match", Integer.valueOf(25), retrieved.getAge());
    }

    @Test
    public void testInsert_MultipleAutoIncrementIds_ShouldGenerateSequentialIds() {
        // Given
        AutoIncrementUser user1 = new AutoIncrementUser("User1", "<EMAIL>", 20);
        AutoIncrementUser user2 = new AutoIncrementUser("User2", "<EMAIL>", 21);
        AutoIncrementUser user3 = new AutoIncrementUser("User3", "<EMAIL>", 22);

        // When
        autoIncrementUserDao.insert(user1);
        autoIncrementUserDao.insert(user2);
        autoIncrementUserDao.insert(user3);

        // Then
        assertNotNull("User1 should have generated ID", user1.getId());
        assertNotNull("User2 should have generated ID", user2.getId());
        assertNotNull("User3 should have generated ID", user3.getId());
        
        assertTrue("IDs should be sequential", user2.getId() == user1.getId() + 1);
        assertTrue("IDs should be sequential", user3.getId() == user2.getId() + 1);
    }

    @Test
    public void testInsert_WithExplicitId_ShouldUseProvidedId() {
        // Given
        Long explicitId = 999L;
        AutoIncrementUser user = new AutoIncrementUser("Bob", "<EMAIL>", 30);
        user.setId(explicitId);

        // When
        autoIncrementUserDao.insert(user);

        // Then
        assertEquals("Should use provided ID", explicitId, user.getId());
        
        // Verify the record was inserted with explicit ID
        AutoIncrementUser retrieved = autoIncrementUserDao.get(explicitId);
        assertNotNull("Retrieved record should not be null", retrieved);
        assertEquals("Name should match", "Bob", retrieved.getName());
    }

    @Test
    public void testInsert_CompleteEntity_ShouldHaveAllFields() {
        // Given
        AutoIncrementUser user = new AutoIncrementUser("Charlie", "<EMAIL>", 35);
        
        // When
        autoIncrementUserDao.insert(user);
        
        // Then
        assertNotNull("ID should be generated", user.getId());
        
        AutoIncrementUser retrieved = autoIncrementUserDao.get(user.getId());
        assertEquals("Name should be persisted", "Charlie", retrieved.getName());
        assertEquals("Email should be persisted", "<EMAIL>", retrieved.getEmail());
        assertEquals("Age should be persisted", Integer.valueOf(35), retrieved.getAge());
        assertNotNull("CTime should be set", retrieved.getCtime());
        assertNotNull("MTime should be set", retrieved.getMtime());
        assertEquals("Version should be 1", Long.valueOf(1L), retrieved.getVersion());
    }

    @Test
    public void testUpdate_AutoIncrementUser_ShouldWorkCorrectly() {
        // Given
        AutoIncrementUser user = new AutoIncrementUser("David", "<EMAIL>", 28);
        autoIncrementUserDao.insert(user);
        
        Long originalId = user.getId();
        assertNotNull("ID should be generated", originalId);

        // When
        user.setName("David Updated");
        user.setEmail("<EMAIL>");
        autoIncrementUserDao.update(user);

        // Then
        AutoIncrementUser retrieved = autoIncrementUserDao.get(originalId);
        assertEquals("Name should be updated", "David Updated", retrieved.getName());
        assertEquals("Email should be updated", "<EMAIL>", retrieved.getEmail());
        assertEquals("Age should remain unchanged", Integer.valueOf(28), retrieved.getAge());
        assertEquals("Version should be incremented", Long.valueOf(2L), retrieved.getVersion());
    }

    @Test
    public void testGet_NonExistingAutoIncrementUser_ShouldReturnNull() {
        // When
        AutoIncrementUser retrieved = autoIncrementUserDao.get(9999L);

        // Then
        assertNull("Non-existent record should return null", retrieved);
    }

    @Test(expected = DaoMissingPrimaryKey.class)
    public void testGet_WithNullId_ShouldThrowException() {
        // When
        autoIncrementUserDao.get(null);
    }

    @Test
    public void testDelete_AutoIncrementUser_ShouldWork() {
        // Given
        AutoIncrementUser user = new AutoIncrementUser("Eve", "<EMAIL>", 27);
        autoIncrementUserDao.insert(user);
        
        Long id = user.getId();
        assertNotNull("ID should be generated", id);

        // When
        autoIncrementUserDao.delete(id);

        // Then
        assertNull("Record should be deleted", autoIncrementUserDao.get(id));
    }

    @Test
    public void testInsert_NullFields_ShouldWork() {
        // Given
        AutoIncrementUser user = new AutoIncrementUser(null, null, null);

        // When
        autoIncrementUserDao.insert(user);

        // Then
        assertNotNull("ID should be generated", user.getId());
        
        AutoIncrementUser retrieved = autoIncrementUserDao.get(user.getId());
        assertNull("Name should be null", retrieved.getName());
        assertNull("Email should be null", retrieved.getEmail());
        assertNull("Age should be null", retrieved.getAge());
    }

    @Test
    public void testInsert_EmptyStringFields_ShouldWork() {
        // Given
        AutoIncrementUser user = new AutoIncrementUser("", "", 0);

        // When
        autoIncrementUserDao.insert(user);

        // Then
        assertNotNull("ID should be generated", user.getId());
        
        AutoIncrementUser retrieved = autoIncrementUserDao.get(user.getId());
        assertEquals("Name should be empty string", "", retrieved.getName());
        assertEquals("Email should be empty string", "", retrieved.getEmail());
        assertEquals("Age should be 0", Integer.valueOf(0), retrieved.getAge());
    }

    @Test
    public void testInsert_WithZeroAge_ShouldWork() {
        // Given
        AutoIncrementUser user = new AutoIncrementUser("ZeroAge", "<EMAIL>", 0);

        // When
        autoIncrementUserDao.insert(user);

        // Then
        assertNotNull("ID should be generated", user.getId());
        
        AutoIncrementUser retrieved = autoIncrementUserDao.get(user.getId());
        assertEquals("Age should be 0", Integer.valueOf(0), retrieved.getAge());
    }

    @Test
    public void testInsert_WithNegativeAge_ShouldWork() {
        // Given
        AutoIncrementUser user = new AutoIncrementUser("NegativeAge", "<EMAIL>", -1);

        // When
        autoIncrementUserDao.insert(user);

        // Then
        assertNotNull("ID should be generated", user.getId());
        
        AutoIncrementUser retrieved = autoIncrementUserDao.get(user.getId());
        assertEquals("Age should be -1", Integer.valueOf(-1), retrieved.getAge());
    }

    @Test
    public void testInsert_WithUnicodeCharacters_ShouldWork() {
        // Given
        AutoIncrementUser user = new AutoIncrementUser("中文测试", "测试@例子.公司", 25);

        // When
        autoIncrementUserDao.insert(user);

        // Then
        assertNotNull("ID should be generated", user.getId());
        
        AutoIncrementUser retrieved = autoIncrementUserDao.get(user.getId());
        assertEquals("Unicode name should be persisted", "中文测试", retrieved.getName());
        assertEquals("Unicode email should be persisted", "测试@例子.公司", retrieved.getEmail());
    }

    @Test
    public void testInsert_Concurrent_ShouldGenerateUniqueIds() {
        // Given
        int count = 5;
        AutoIncrementUser[] users = new AutoIncrementUser[count];
        
        // When
        for (int i = 0; i < count; i++) {
            users[i] = new AutoIncrementUser("User" + i, "user" + i + "@example.com", 20 + i);
            autoIncrementUserDao.insert(users[i]);
        }

        // Then
        java.util.Set<Long> ids = new java.util.HashSet<Long>();
        for (AutoIncrementUser user : users) {
            assertNotNull("Each user should have ID", user.getId());
            assertTrue("ID should be positive", user.getId() > 0);
            ids.add(user.getId());
        }
        
        assertEquals("All IDs should be unique", count, ids.size());
    }

    @Test
    public void testInsertAndDelete_MultipleTimes_ShouldWork() {
        // Given
        int count = 3;
        
        // When
        for (int i = 0; i < count; i++) {
            AutoIncrementUser user = new AutoIncrementUser("Cycle" + i, "cycle" + i + "@example.com", 20 + i);
            autoIncrementUserDao.insert(user);
            
            // Verify
            assertNotNull("User " + i + " should have ID", user.getId());
            
            AutoIncrementUser retrieved = autoIncrementUserDao.get(user.getId());
            assertNotNull("User " + i + " should be retrievable", retrieved);
            
            // Delete
            autoIncrementUserDao.delete(user.getId());
            
            // Verify deletion
            assertNull("User " + i + " should be deleted", autoIncrementUserDao.get(user.getId()));
        }
    }

    @Test
    public void testFilter_WithMultipleConditions_ShouldWork() {
        // Given
        AutoIncrementUser user1 = new AutoIncrementUser("FilterTest1", "<EMAIL>", 25);
        AutoIncrementUser user2 = new AutoIncrementUser("FilterTest2", "<EMAIL>", 30);
        AutoIncrementUser user3 = new AutoIncrementUser("FilterTest3", "<EMAIL>", 35);
        
        autoIncrementUserDao.insert(user1);
        autoIncrementUserDao.insert(user2);
        autoIncrementUserDao.insert(user3);

        // When
        List<AutoIncrementUser> results = autoIncrementUserDao.filter(Criteria.where("age").is(30))
                .fetchAll();

        // Then
        assertEquals("Should find 1 user with age 30", 1, results.size());
        assertEquals("Should be user2", "FilterTest2", results.get(0).getName());
    }

    @Test
    public void testCount_WithAutoIncrementUsers_ShouldWork() {
        // Given
        AutoIncrementUser user1 = new AutoIncrementUser("Count1", "<EMAIL>", 25);
        AutoIncrementUser user2 = new AutoIncrementUser("Count2", "<EMAIL>", 30);
        
        autoIncrementUserDao.insert(user1);
        autoIncrementUserDao.insert(user2);

        // When
        long count = autoIncrementUserDao.filter(Criteria.where("age").is(30))
                .count();

        // Then
        assertEquals("Should have 1 user with age 30", 1, count);
    }
}