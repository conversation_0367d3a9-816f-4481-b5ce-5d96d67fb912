package com.wosai.pay.common.data.jdbc;

import com.wosai.pay.common.data.Jackson2PersistenceHelper;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;

/**
 * Test DAO for auto-increment ID scenarios
 */
public class AutoIncrementUserDao extends JdbcVersionedRecordDao<Long, AutoIncrementUser> {
    
    private static final String TABLE_NAME = "auto_increment_user";
    
    public AutoIncrementUserDao(NamedParameterJdbcTemplate namedParameterJdbcTemplate) {
        super(TABLE_NAME, AutoIncrementUser.class, "", namedParameterJdbcTemplate, new Jackson2PersistenceHelper());
    }
}