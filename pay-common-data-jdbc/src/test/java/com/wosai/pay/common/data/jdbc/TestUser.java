package com.wosai.pay.common.data.jdbc;

import com.wosai.pay.common.data.VersionedRecord;

import java.util.Map;

/**
 * Test entity class for JdbcVersionedRecordDao testing
 */
public class TestUser extends VersionedRecord<String> {
    
    private String name;
    private String email;
    private Integer age;
    private String status;
    private Long lastLoginTime;
    private ExtraInfo extra;
    private Map<String, Object> loginInfo;
    
    public TestUser() {
    }
    
    public TestUser(String id, String name, String email, Integer age, String status) {
        setId(id);
        this.name = name;
        this.email = email;
        this.age = age;
        this.status = status;
    }
    
    public TestUser(String id, String name, String email, Integer age, String status, Long lastLoginTime) {
        setId(id);
        this.name = name;
        this.email = email;
        this.age = age;
        this.status = status;
        this.lastLoginTime = lastLoginTime;
    }
    
    public TestUser(String id, String name, String email, Integer age, String status, Long lastLoginTime, ExtraInfo extra) {
        setId(id);
        this.name = name;
        this.email = email;
        this.age = age;
        this.status = status;
        this.lastLoginTime = lastLoginTime;
        this.extra = extra;
    }
    
    public TestUser(String id, String name, String email, Integer age, String status, Long lastLoginTime, ExtraInfo extra, Map<String, Object> loginInfo) {
        setId(id);
        this.name = name;
        this.email = email;
        this.age = age;
        this.status = status;
        this.lastLoginTime = lastLoginTime;
        this.extra = extra;
        this.loginInfo = loginInfo;
    }
    
    public String getName() {
        return name;
    }
    
    public void setName(String name) {
        this.name = name;
    }
    
    public String getEmail() {
        return email;
    }
    
    public void setEmail(String email) {
        this.email = email;
    }
    
    public Integer getAge() {
        return age;
    }
    
    public void setAge(Integer age) {
        this.age = age;
    }
    
    public String getStatus() {
        return status;
    }
    
    public void setStatus(String status) {
        this.status = status;
    }
    
    public Long getLastLoginTime() {
        return lastLoginTime;
    }
    
    public void setLastLoginTime(Long lastLoginTime) {
        this.lastLoginTime = lastLoginTime;
    }
    
    public ExtraInfo getExtra() {
        return extra;
    }
    
    public void setExtra(ExtraInfo extra) {
        this.extra = extra;
    }
    
    public Map<String, Object> getLoginInfo() {
        return loginInfo;
    }
    
    public void setLoginInfo(Map<String, Object> loginInfo) {
        this.loginInfo = loginInfo;
    }
    
    @Override
    public String toString() {
        return "TestUser{" +
                "id='" + getId() + '\'' +
                ", name='" + name + '\'' +
                ", email='" + email + '\'' +
                ", age=" + age +
                ", status='" + status + '\'' +
                ", lastLoginTime=" + lastLoginTime +
                ", extra=" + extra +
                ", loginInfo=" + loginInfo +
                ", version=" + getVersion() +
                ", ctime=" + getCtime() +
                ", mtime=" + getMtime() +
                '}';
    }
}
