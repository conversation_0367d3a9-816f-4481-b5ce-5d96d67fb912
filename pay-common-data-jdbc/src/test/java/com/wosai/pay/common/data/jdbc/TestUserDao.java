package com.wosai.pay.common.data.jdbc;

import com.wosai.pay.common.data.Jackson2PersistenceHelper;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;

/**
 * Test DAO implementation for TestUser entity
 */
public class TestUserDao extends JdbcVersionedRecordDao<String, TestUser> {
    
    private static final String TABLE_NAME = "test_user";
    
    public TestUserDao(NamedParameterJdbcTemplate namedParameterJdbcTemplate) {
        super(TABLE_NAME, TestUser.class, "", namedParameterJdbcTemplate, new Jackson2PersistenceHelper());
    }
}
