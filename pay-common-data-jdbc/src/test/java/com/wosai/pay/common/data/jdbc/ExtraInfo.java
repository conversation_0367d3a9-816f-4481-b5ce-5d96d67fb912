package com.wosai.pay.common.data.jdbc;

import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * Extra information for TestUser containing school and class details
 */
public class ExtraInfo {
    
    private String schoolName;
    
    @JsonProperty("class_count")
    private Integer classCount;
    
    public ExtraInfo() {
    }
    
    public ExtraInfo(String schoolName, Integer classCount) {
        this.schoolName = schoolName;
        this.classCount = classCount;
    }
    
    public String getSchoolName() {
        return schoolName;
    }
    
    public void setSchoolName(String schoolName) {
        this.schoolName = schoolName;
    }
    
    public Integer getClassCount() {
        return classCount;
    }
    
    public void setClassCount(Integer classCount) {
        this.classCount = classCount;
    }
    
    @Override
    public String toString() {
        return "ExtraInfo{" +
                "schoolName='" + schoolName + '\'' +
                ", classCount=" + classCount +
                '}';
    }
}