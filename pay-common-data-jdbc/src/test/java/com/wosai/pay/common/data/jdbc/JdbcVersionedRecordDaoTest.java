package com.wosai.pay.common.data.jdbc;

import com.wosai.pay.common.data.Criteria;
import com.wosai.pay.common.data.Filter;
import com.wosai.pay.common.data.exception.DaoRecordVersionMismatch;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.util.*;

import static org.junit.Assert.*;

/**
 * Comprehensive test class for JdbcVersionedRecordDao using HSQLDB embedded database
 */
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(classes = TestConfig.class)
public class JdbcVersionedRecordDaoTest {

    @Autowired
    private TestUserDao testUserDao;

    @Autowired
    private NamedParameterJdbcTemplate jdbcTemplate;

    private TestUser testUser1;
    private TestUser testUser2;

    @Before
    public void setUp() {
        // Drop and recreate table to ensure schema is updated
        jdbcTemplate.getJdbcOperations().execute("DROP TABLE test_user IF EXISTS");
        jdbcTemplate.getJdbcOperations().execute(
                "CREATE TABLE test_user (" +
                "    id VARCHAR(36) NOT NULL PRIMARY KEY," +
                "    name VARCHAR(100)," +
                "    email VARCHAR(100)," +
                "    age INTEGER," +
                "    status VARCHAR(20)," +
                "    last_login_time BIGINT," +
                "    extra BLOB," +
                "    login_info BLOB," +
                "    ctime BIGINT," +
                "    mtime BIGINT," +
                "    version BIGINT" +
                ")"
        );

        // Clean up any existing data
        jdbcTemplate.update("DELETE FROM test_user", (java.util.Map<String, Object>) null);

        // Create test data
        ExtraInfo extra1 = new ExtraInfo("Harvard University", 12);
        ExtraInfo extra2 = new ExtraInfo("Stanford University", 8);
        
        Map<String, Object> loginInfo1 = new HashMap<String, Object>() {{
            put("last_login_ip", "*************");
            put("login_count", 15);
            put("last_device", "iPhone 13");
            put("session_duration", 3600);
            put("is_mobile", true);
        }};
        
        Map<String, Object> loginInfo2 = new HashMap<String, Object>() {{
            put("last_login_ip", "*********");
            put("login_count", 8);
            put("last_device", "MacBook Pro");
            put("session_duration", 7200);
            put("is_mobile", false);
        }};
        
        testUser1 = new TestUser(
                UUID.randomUUID().toString(),
                "John Doe",
                "<EMAIL>",
                30,
                "ACTIVE",
                System.currentTimeMillis() - 86400000L, // 1 day ago
                extra1,
                loginInfo1
        );

        testUser2 = new TestUser(
                UUID.randomUUID().toString(),
                "Jane Smith",
                "<EMAIL>",
                25,
                "INACTIVE",
                System.currentTimeMillis() - 172800000L, // 2 days ago
                extra2,
                loginInfo2
        );
    }

    @After
    public void tearDown() {
        // Clean up after each test
        jdbcTemplate.update("DELETE FROM test_user", (java.util.Map<String, Object>) null);
    }

    @Test
    public void testInsert_ShouldSetSystemColumns() {
        // Given
        assertNull("Version should be null before insert", testUser1.getVersion());
        assertNull("Ctime should be null before insert", testUser1.getCtime());
        assertNull("Mtime should be null before insert", testUser1.getMtime());

        long beforeInsert = System.currentTimeMillis();

        // When
        testUserDao.insert(testUser1);

        // Then
        long afterInsert = System.currentTimeMillis();

        assertNotNull("Version should be set after insert", testUser1.getVersion());
        assertEquals("Version should be 1 for new record", Long.valueOf(1L), testUser1.getVersion());

        assertNotNull("Ctime should be set after insert", testUser1.getCtime());
        assertTrue("Ctime should be within reasonable range",
                testUser1.getCtime() >= beforeInsert && testUser1.getCtime() <= afterInsert);

        assertNotNull("Mtime should be set after insert", testUser1.getMtime());
        assertTrue("Mtime should be within reasonable range",
                testUser1.getMtime() >= beforeInsert && testUser1.getMtime() <= afterInsert);

        assertEquals("Ctime and Mtime should be equal for new record",
                testUser1.getCtime(), testUser1.getMtime());
    }

    @Test
    public void testGet_ShouldRetrieveRecord() {
        // Given
        testUserDao.insert(testUser1);

        // When
        TestUser retrieved = testUserDao.get(testUser1.getId());

        // Then
        assertNotNull("Retrieved record should not be null", retrieved);
        assertEquals("ID should match", testUser1.getId(), retrieved.getId());
        assertEquals("Name should match", testUser1.getName(), retrieved.getName());
        assertEquals("Email should match", testUser1.getEmail(), retrieved.getEmail());
        assertEquals("Age should match", testUser1.getAge(), retrieved.getAge());
        assertEquals("Status should match", testUser1.getStatus(), retrieved.getStatus());
        assertEquals("Version should match", testUser1.getVersion(), retrieved.getVersion());
        assertEquals("Ctime should match", testUser1.getCtime(), retrieved.getCtime());
        assertEquals("Mtime should match", testUser1.getMtime(), retrieved.getMtime());
    }

    @Test
    public void testGet_WithSpecificColumns() {
        // Given
        testUserDao.insert(testUser1);

        // When
        TestUser retrieved = testUserDao.get(testUser1.getId(), "name", "email");

        // Then
        assertNotNull("Retrieved record should not be null", retrieved);
        assertEquals("ID should be present (always included)", testUser1.getId(), retrieved.getId());
        assertEquals("Name should be present", testUser1.getName(), retrieved.getName());
        assertEquals("Email should be present", testUser1.getEmail(), retrieved.getEmail());
        // Note: Age and Status might be null since they weren't explicitly requested
        // but system columns should always be included
        assertEquals("Version should be present", testUser1.getVersion(), retrieved.getVersion());
        assertEquals("Ctime should be present", testUser1.getCtime(), retrieved.getCtime());
        assertEquals("Mtime should be present", testUser1.getMtime(), retrieved.getMtime());
    }

    @Test
    public void testGet_NonExistentRecord_ShouldReturnNull() {
        // When
        TestUser retrieved = testUserDao.get("non-existent-id");

        // Then
        assertNull("Non-existent record should return null", retrieved);
    }

    @Test
    public void testUpdate_ShouldIncrementVersionAndUpdateMtime() throws InterruptedException {
        // Given
        testUserDao.insert(testUser1);
        Long originalVersion = testUser1.getVersion();
        Long originalCtime = testUser1.getCtime();
        Long originalMtime = testUser1.getMtime();

        // Wait a bit to ensure mtime difference
        Thread.sleep(10);

        // When
        testUser1.setName("John Updated");
        testUser1.setAge(31);
        long beforeUpdate = System.currentTimeMillis();
        testUserDao.update(testUser1);
        long afterUpdate = System.currentTimeMillis();

        // Then
        assertEquals("Version should be incremented",
                Long.valueOf(originalVersion + 1), testUser1.getVersion());
        assertEquals("Ctime should not change", originalCtime, testUser1.getCtime());
        assertTrue("Mtime should be updated", testUser1.getMtime() >= originalMtime);
        assertTrue("Mtime should be within reasonable range",
                testUser1.getMtime() >= beforeUpdate - 1000 && testUser1.getMtime() <= afterUpdate + 1000);

        // Verify in database
        TestUser retrieved = testUserDao.get(testUser1.getId());
        assertEquals("Updated name should be persisted", "John Updated", retrieved.getName());
        assertEquals("Updated age should be persisted", Integer.valueOf(31), retrieved.getAge());
        assertEquals("Version should be incremented in DB",
                Long.valueOf(originalVersion + 1), retrieved.getVersion());
    }

    @Test
    public void testUpdate_WithSpecificColumns() throws InterruptedException {
        // Given
        testUserDao.insert(testUser1);
        String originalName = testUser1.getName();
        Long originalVersion = testUser1.getVersion();

        // Wait a bit to ensure mtime difference
        Thread.sleep(10);

        // When
        testUser1.setName("John Updated");
        testUser1.setAge(31); // This should not be updated since we only specify "name"
        testUserDao.update(testUser1, "name");

        // Then
        TestUser retrieved = testUserDao.get(testUser1.getId());
        assertEquals("Updated name should be persisted", "John Updated", retrieved.getName());
        assertNotEquals("Age should remain unchanged", testUser1.getAge(), retrieved.getAge());
        assertEquals("Version should be incremented",
                Long.valueOf(originalVersion + 1), retrieved.getVersion());
    }

    @Test(expected = DaoRecordVersionMismatch.class)
    public void testUpdate_VersionMismatch_ShouldThrowException() {
        // Given
        testUserDao.insert(testUser1);

        // Simulate concurrent update by manually changing version in database
        jdbcTemplate.update(
                "UPDATE test_user SET version = version + 1 WHERE id = :id",
                java.util.Collections.singletonMap("id", testUser1.getId())
        );

        // When - try to update with old version
        testUser1.setName("John Updated");
        testUserDao.update(testUser1); // This should throw DaoRecordVersionMismatch
    }

    @Test
    public void testDelete_ShouldRemoveRecord() {
        // Given
        testUserDao.insert(testUser1);
        assertNotNull("Record should exist before delete",
                testUserDao.get(testUser1.getId()));

        // When
        testUserDao.delete(testUser1.getId());

        // Then
        assertNull("Record should not exist after delete",
                testUserDao.get(testUser1.getId()));
    }

    @Test
    public void testFilter_WithCriteria() {
        // Given
        testUserDao.insert(testUser1); // ACTIVE status
        testUserDao.insert(testUser2); // INACTIVE status

        // When
        Criteria criteria = Criteria.where("status").is("ACTIVE");
        Filter<TestUser> filter = testUserDao.filter(criteria);
        List<TestUser> activeUsers = filter.fetchAll();

        // Then
        assertEquals("Should find exactly one active user", 1, activeUsers.size());
        assertEquals("Should find the correct user", testUser1.getId(), activeUsers.get(0).getId());
        assertEquals("Status should be ACTIVE", "ACTIVE", activeUsers.get(0).getStatus());
    }

    @Test
    public void testFilter_WithCriteriaAndColumns() {
        // Given
        testUserDao.insert(testUser1);
        testUserDao.insert(testUser2);

        // When
        Criteria criteria = Criteria.where("status").is("INACTIVE");
        Filter<TestUser> filter = testUserDao.filter(criteria, "name", "status");
        List<TestUser> inactiveUsers = filter.fetchAll();

        // Then
        assertEquals("Should find exactly one inactive user", 1, inactiveUsers.size());
        TestUser found = inactiveUsers.get(0);
        assertEquals("Should find the correct user", testUser2.getId(), found.getId());
        assertEquals("Name should be present", testUser2.getName(), found.getName());
        assertEquals("Status should be present", "INACTIVE", found.getStatus());
        // System columns should always be included
        assertNotNull("Version should be present", found.getVersion());
        assertNotNull("Ctime should be present", found.getCtime());
        assertNotNull("Mtime should be present", found.getMtime());
    }

    @Test
    public void testFilter_Count() {
        // Given
        testUserDao.insert(testUser1);
        testUserDao.insert(testUser2);

        // When
        Criteria criteria = Criteria.where("status").is("ACTIVE");
        long count = testUserDao.filter(criteria).count();

        // Then
        assertEquals("Should count exactly one active user", 1L, count);
    }

    @Test
    public void testFilter_NoResults() {
        // Given
        testUserDao.insert(testUser1);
        testUserDao.insert(testUser2);

        // When
        Criteria criteria = Criteria.where("status").is("NON_EXISTENT");
        List<TestUser> results = testUserDao.filter(criteria).fetchAll();

        // Then
        assertTrue("Should return empty list for no matches", results.isEmpty());
    }

    @Test
    public void testMultipleOperations_VersionConsistency() throws InterruptedException {
        // Given
        testUserDao.insert(testUser1);
        assertEquals("Initial version should be 1", Long.valueOf(1L), testUser1.getVersion());

        // When - perform multiple updates
        Thread.sleep(10);
        testUser1.setName("John Updated 1");
        testUserDao.update(testUser1);
        assertEquals("Version should be 2 after first update", Long.valueOf(2L), testUser1.getVersion());

        Thread.sleep(10);
        testUser1.setAge(32);
        testUserDao.update(testUser1);
        assertEquals("Version should be 3 after second update", Long.valueOf(3L), testUser1.getVersion());

        Thread.sleep(10);
        testUser1.setStatus("SUSPENDED");
        testUserDao.update(testUser1);
        assertEquals("Version should be 4 after third update", Long.valueOf(4L), testUser1.getVersion());

        // Then - verify final state
        TestUser retrieved = testUserDao.get(testUser1.getId());
        assertEquals("Final name should be correct", "John Updated 1", retrieved.getName());
        assertEquals("Final age should be correct", Integer.valueOf(32), retrieved.getAge());
        assertEquals("Final status should be correct", "SUSPENDED", retrieved.getStatus());
        assertEquals("Final version should be 4", Long.valueOf(4L), retrieved.getVersion());
    }

    @Test
    public void testConcurrentUpdate_Simulation() {
        // Given
        testUserDao.insert(testUser1);

        // Get two instances of the same record (simulating concurrent access)
        TestUser user1 = testUserDao.get(testUser1.getId());
        TestUser user2 = testUserDao.get(testUser1.getId());

        // When - first update succeeds
        user1.setName("Updated by User 1");
        testUserDao.update(user1);

        // Then - second update should fail due to version mismatch
        user2.setName("Updated by User 2");
        try {
            testUserDao.update(user2);
            fail("Second update should have failed due to version mismatch");
        } catch (DaoRecordVersionMismatch e) {
            assertTrue("Exception message should mention version mismatch",
                    e.getMessage().contains("version"));
        }

        // Verify that only the first update was applied
        TestUser final_user = testUserDao.get(testUser1.getId());
        assertEquals("Only first update should be applied", "Updated by User 1", final_user.getName());
        assertEquals("Version should be 2", Long.valueOf(2L), final_user.getVersion());
    }

    @Test
    public void testInsert_WithLastLoginTime() {
        // Given
        TestUser userWithLoginTime = new TestUser(
                UUID.randomUUID().toString(),
                "Test User",
                "<EMAIL>",
                35,
                "ACTIVE",
                System.currentTimeMillis() - 3600000L // 1 hour ago
        );

        // When
        testUserDao.insert(userWithLoginTime);

        // Then
        assertNotNull("Last login time should be set", userWithLoginTime.getLastLoginTime());
        assertTrue("Last login time should be within reasonable range",
                Math.abs(userWithLoginTime.getLastLoginTime() - (System.currentTimeMillis() - 3600000L)) < 1000);

        // Verify persistence
        TestUser retrieved = testUserDao.get(userWithLoginTime.getId());
        assertNotNull("Retrieved user should not be null", retrieved);
        assertEquals("Last login time should match", 
                userWithLoginTime.getLastLoginTime(), retrieved.getLastLoginTime());
    }

    @Test
    public void testUpdate_LastLoginTime() {
        // Given
        testUserDao.insert(testUser1);
        Long originalLoginTime = testUser1.getLastLoginTime();
        Long newLoginTime = System.currentTimeMillis();

        // When
        testUser1.setLastLoginTime(newLoginTime);
        testUserDao.update(testUser1);

        // Then
        assertEquals("Last login time should be updated", newLoginTime, testUser1.getLastLoginTime());

        // Verify in database
        TestUser retrieved = testUserDao.get(testUser1.getId());
        assertEquals("Last login time should be persisted", newLoginTime, retrieved.getLastLoginTime());
    }

    @Test
    public void testInsert_WithExtraInfo() {
        // Given
        ExtraInfo extraInfo = new ExtraInfo("MIT", 15);
        TestUser userWithExtra = new TestUser(
                UUID.randomUUID().toString(),
                "Bob Wilson",
                "<EMAIL>",
                28,
                "ACTIVE",
                System.currentTimeMillis() - 7200000L, // 2 hours ago
                extraInfo
        );

        // When
        testUserDao.insert(userWithExtra);

        // Then
        assertNotNull("Extra info should be set", userWithExtra.getExtra());
        assertEquals("School name should match", "MIT", userWithExtra.getExtra().getSchoolName());
        assertEquals("Class count should match", Integer.valueOf(15), userWithExtra.getExtra().getClassCount());

        // Verify persistence
        TestUser retrieved = testUserDao.get(userWithExtra.getId());
        assertNotNull("Retrieved user should not be null", retrieved);
        assertNotNull("Extra info should be persisted", retrieved.getExtra());
        assertEquals("School name should be persisted", "MIT", retrieved.getExtra().getSchoolName());
        assertEquals("Class count should be persisted", Integer.valueOf(15), retrieved.getExtra().getClassCount());
    }

    @Test
    public void testUpdate_ExtraInfo() {
        // Given
        testUserDao.insert(testUser1);
        ExtraInfo originalExtra = testUser1.getExtra();
        ExtraInfo newExtra = new ExtraInfo("Yale University", 10);

        // When
        testUser1.setExtra(newExtra);
        testUserDao.update(testUser1);

        // Then
        assertNotNull("Extra info should be updated", testUser1.getExtra());
        assertEquals("Updated school name should match", "Yale University", testUser1.getExtra().getSchoolName());
        assertEquals("Updated class count should match", Integer.valueOf(10), testUser1.getExtra().getClassCount());

        // Verify in database
        TestUser retrieved = testUserDao.get(testUser1.getId());
        assertNotNull("Extra info should be persisted", retrieved.getExtra());
        assertEquals("School name should be updated in DB", "Yale University", retrieved.getExtra().getSchoolName());
        assertEquals("Class count should be updated in DB", Integer.valueOf(10), retrieved.getExtra().getClassCount());
    }

    @Test
    public void testGet_WithExtraInfo() {
        // Given
        testUserDao.insert(testUser1);

        // When
        TestUser retrieved = testUserDao.get(testUser1.getId());

        // Then
        assertNotNull("Retrieved record should not be null", retrieved);
        assertEquals("ID should match", testUser1.getId(), retrieved.getId());
        assertEquals("Name should match", testUser1.getName(), retrieved.getName());
        assertNotNull("Extra info should be retrieved", retrieved.getExtra());
        assertEquals("School name should match", "Harvard University", retrieved.getExtra().getSchoolName());
        assertEquals("Class count should match", Integer.valueOf(12), retrieved.getExtra().getClassCount());
    }

    @Test
    public void testInsert_WithLoginInfo() {
        // Given
        Map<String, Object> loginInfo = new HashMap<String, Object>() {{
            put("last_login_ip", "************");
            put("login_count", 25);
            put("last_device", "Samsung Galaxy S21");
            put("session_duration", 1800);
            put("is_mobile", true);
            put("login_methods", new ArrayList<String>() {{
                add("password");
                add("fingerprint");
            }});
        }};
        
        TestUser userWithLogin = new TestUser(
                UUID.randomUUID().toString(),
                "Alice Johnson",
                "<EMAIL>",
                27,
                "ACTIVE",
                System.currentTimeMillis() - 3600000L,
                new ExtraInfo("MIT", 15),
                loginInfo
        );

        // When
        testUserDao.insert(userWithLogin);

        // Then
        assertNotNull("Login info should be set", userWithLogin.getLoginInfo());
        assertEquals("Login IP should match", "************", userWithLogin.getLoginInfo().get("last_login_ip"));
        assertEquals("Login count should match", 25, userWithLogin.getLoginInfo().get("login_count"));
        assertEquals("Device should match", "Samsung Galaxy S21", userWithLogin.getLoginInfo().get("last_device"));
        assertTrue("Should be mobile", (Boolean) userWithLogin.getLoginInfo().get("is_mobile"));
        assertNotNull("Login methods should exist", userWithLogin.getLoginInfo().get("login_methods"));

        // Verify persistence
        TestUser retrieved = testUserDao.get(userWithLogin.getId());
        assertNotNull("Login info should be persisted", retrieved.getLoginInfo());
        assertEquals("Login IP should be persisted", "************", retrieved.getLoginInfo().get("last_login_ip"));
        assertEquals("Login count should be persisted", 25, retrieved.getLoginInfo().get("login_count"));
    }

    @Test
    public void testUpdate_LoginInfo() {
        // Given
        testUserDao.insert(testUser1);
        Map<String, Object> originalLogin = testUser1.getLoginInfo();
        Map<String, Object> newLoginInfo = new HashMap<String, Object>() {{
            put("last_login_ip", "*************");
            put("login_count", 30);
            put("last_device", "iPad Pro");
            put("session_duration", 2400);
            put("is_mobile", true);
        }};

        // When
        testUser1.setLoginInfo(newLoginInfo);
        testUserDao.update(testUser1);

        // Then
        assertNotNull("Login info should be updated", testUser1.getLoginInfo());
        assertEquals("Updated IP should match", "*************", testUser1.getLoginInfo().get("last_login_ip"));
        assertEquals("Updated count should match", 30, testUser1.getLoginInfo().get("login_count"));

        // Verify in database
        TestUser retrieved = testUserDao.get(testUser1.getId());
        assertNotNull("Login info should be persisted", retrieved.getLoginInfo());
        assertEquals("IP should be updated in DB", "*************", retrieved.getLoginInfo().get("last_login_ip"));
        assertEquals("Count should be updated in DB", 30, retrieved.getLoginInfo().get("login_count"));
    }

    @Test
    public void testGet_WithLoginInfo() {
        // Given
        testUserDao.insert(testUser1);

        // When
        TestUser retrieved = testUserDao.get(testUser1.getId());

        // Then
        assertNotNull("Retrieved record should not be null", retrieved);
        assertNotNull("Login info should be retrieved", retrieved.getLoginInfo());
        assertEquals("Login IP should match", "*************", retrieved.getLoginInfo().get("last_login_ip"));
        assertEquals("Login count should match", 15, retrieved.getLoginInfo().get("login_count"));
        assertEquals("Device should match", "iPhone 13", retrieved.getLoginInfo().get("last_device"));
        assertEquals("Session duration should match", 3600, retrieved.getLoginInfo().get("session_duration"));
        assertEquals("Mobile flag should match", true, retrieved.getLoginInfo().get("is_mobile"));
    }

    @Test
    public void testUpdate_AllFields() {
        // Given
        testUserDao.insert(testUser1);
        
        // When
        testUser1.setName("Updated Name");
        testUser1.setEmail("<EMAIL>");
        testUser1.setAge(35);
        testUser1.setStatus("SUSPENDED");
        testUser1.setLastLoginTime(System.currentTimeMillis());
        testUser1.setExtra(new ExtraInfo("Yale University", 10));
        Map<String, Object> newLoginAll = new HashMap<String, Object>() {{
            put("last_login_ip", "***********");
            put("login_count", 100);
            put("last_device", "Windows PC");
            put("session_duration", 4800);
            put("is_mobile", false);
        }};
        testUser1.setLoginInfo(newLoginAll);
        testUserDao.update(testUser1);

        // Then
        TestUser retrieved = testUserDao.get(testUser1.getId());
        assertEquals("Name should be updated", "Updated Name", retrieved.getName());
        assertEquals("Email should be updated", "<EMAIL>", retrieved.getEmail());
        assertEquals("Age should be updated", Integer.valueOf(35), retrieved.getAge());
        assertEquals("Status should be updated", "SUSPENDED", retrieved.getStatus());
        assertEquals("Extra school should be updated", "Yale University", retrieved.getExtra().getSchoolName());
        assertEquals("Login IP should be updated", "***********", retrieved.getLoginInfo().get("last_login_ip"));
    }
}
