package com.wosai.pay.common.data.jdbc;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;
import org.springframework.jdbc.datasource.embedded.EmbeddedDatabase;
import org.springframework.jdbc.datasource.embedded.EmbeddedDatabaseBuilder;
import org.springframework.jdbc.datasource.embedded.EmbeddedDatabaseType;

import javax.sql.DataSource;

/**
 * Test configuration for HSQLDB embedded database
 */
@Configuration
public class TestConfig {

    @Bean
    public DataSource dataSource() {
        EmbeddedDatabase db = new EmbeddedDatabaseBuilder()
                .setType(EmbeddedDatabaseType.HSQL)
                .ignoreFailedDrops(true)
                .build();
        return db;
    }

    @Bean
    public NamedParameterJdbcTemplate namedParameterJdbcTemplate(DataSource dataSource) {
        return new NamedParameterJdbcTemplate(dataSource);
    }

    @Bean
    public TestUserDao testUserDao(NamedParameterJdbcTemplate namedParameterJdbcTemplate) {
        return new TestUserDao(namedParameterJdbcTemplate);
    }
    
    @Bean
    public AutoIncrementUserDao autoIncrementUserDao(NamedParameterJdbcTemplate namedParameterJdbcTemplate) {
        return new AutoIncrementUserDao(namedParameterJdbcTemplate);
    }
}
