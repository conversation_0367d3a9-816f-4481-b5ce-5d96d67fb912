# 数据库访问层系统设计文档

## 1. 概述

支付组数据库访问层模块是一个基于Spring JDBC的高度抽象化的数据访问层框架，专为支付系统设计，提供了统一的CRUD操作接口、版本控制、乐观锁机制以及灵活的条件查询能力。

### 1.1 设计目标
- **简化数据访问**：通过抽象化的DAO模式，减少重复的数据访问代码
- **统一规范**：提供一致的数据访问接口和行为规范
- **版本控制**：支持数据版本管理，实现乐观锁机制
- **灵活查询**：支持复杂的条件查询和分页功能
- **扩展性**：易于扩展和定制，适应不同业务场景

### 1.2 核心特性
- 自动ID生成支持（自增ID）
- 版本化记录管理（VersionedRecord）
- 乐观锁机制
- 灵活的条件查询（Criteria API）
- 字段级操作控制
- 系统字段自动维护（创建时间、修改时间、版本号）

## 2. 系统架构

### 2.1 架构概览

```
┌─────────────────────────────────────────────────────────────┐
│                    业务应用层                                │
├─────────────────────────────────────────────────────────────┤
│                    DAO接口层                                │
│  ┌─────────────┐  ┌──────────────────┐  ┌──────────────┐   │
│  │   JdbcDao   │  │ JdbcVersionedDao │  │  CustomDao   │   │
│  └─────────────┘  └──────────────────┘  └──────────────┘   │
├─────────────────────────────────────────────────────────────┤
│                  核心抽象层                                 │
│  ┌─────────────┐  ┌──────────────┐  ┌─────────────────┐    │
│  │    Dao      │  │    Record    │  │  Persistence    │    │
│  │  Interface  │  │   Entity     │  │    Helper       │    │
│  └─────────────┘  └──────────────┘  └─────────────────┘    │
├─────────────────────────────────────────────────────────────┤
│                    数据访问层                               │
│  ┌─────────────────────────────────────────────────────┐   │
│  │              Spring JDBC Template                   │   │
│  └─────────────────────────────────────────────────────┘   │
├─────────────────────────────────────────────────────────────┤
│                    数据库层                                │
└─────────────────────────────────────────────────────────────┘
```

### 2.2 核心组件

#### 2.2.1 JdbcDao（基础DAO实现）
- **职责**：提供基础的CRUD操作实现
- **特点**：
  - 支持自动ID生成
  - 字段级操作控制
  - 灵活的条件查询
  - 系统字段自动维护

#### 2.2.2 JdbcVersionedRecordDao（版本化DAO）
- **职责**：扩展基础DAO，提供版本控制功能
- **特点**：
  - 乐观锁机制
  - 版本号自动管理
  - 创建时间/修改时间自动维护
  - 版本冲突检测

#### 2.2.3 Criteria API（查询条件构建器）
- **职责**：提供灵活的条件查询能力
- **支持的操作**：
  - 等值查询（=）
  - 范围查询（>, >=, <, <=）
  - 模糊查询（LIKE）
  - 包含查询（IN, NOT IN）
  - 空值查询（IS NULL, IS NOT NULL）
  - 逻辑组合（AND, OR）

## 3. 数据模型设计

### 3.1 实体类结构

#### 3.1.1 Record（基础记录类）
```java
public abstract class Record<ID> {
    private ID id;  // 主键字段
}
```

#### 3.1.2 VersionedRecord（版本化记录类）
```java
public abstract class VersionedRecord<ID> extends Record<ID> {
    private Long version;   // 版本号
    private Long ctime;     // 创建时间
    private Long mtime;     // 修改时间
}
```

### 3.2 系统字段规范

| 字段名 | 类型 | 说明 | 自动维护 |
|--------|------|------|----------|
| id | ID | 主键ID | 部分场景 |
| version | Long | 版本号 | 是 |
| ctime | Long | 创建时间（时间戳） | 是 |
| mtime | Long | 修改时间（时间戳） | 是 |

## 4. 功能设计

### 4.1 CRUD操作

#### 4.1.1 插入操作（Insert）
- **支持自动ID生成**：当ID为null时，自动生成ID
- **系统字段填充**：自动设置ctime、mtime、version
- **字段过滤**：支持插入时排除指定字段

#### 4.1.2 查询操作（Query）
- **主键查询**：通过ID获取单条记录
- **字段选择**：支持指定查询字段，减少数据传输
- **条件查询**：通过Criteria API构建复杂查询条件

#### 4.1.3 更新操作（Update）
- **字段级更新**：支持只更新指定字段
- **版本检查**：版本化记录自动进行版本冲突检查
- **系统字段更新**：自动更新mtime、version

#### 4.1.4 删除操作（Delete）
- **主键删除**：通过ID删除单条记录
- **存在性检查**：删除前检查记录是否存在

### 4.2 条件查询（Filter）

#### 4.2.1 查询构建器
支持链式调用构建复杂查询：
```java
List<TestUser> users = userDao.filter(Criteria.where("age", 25)
    .and("status", "active")
    .or(Criteria.where("name").like("张%")))
    .orderBy("ctime", DESC)
    .limit(10)
    .fetchAll(TestUser.class);
```

#### 4.2.2 分页查询
- **offset/limit**：支持传统分页
- **游标分页**：支持大数据集分页

### 4.3 版本控制机制

#### 4.3.1 乐观锁实现
- **版本号机制**：每次更新自动增加版本号
- **冲突检测**：更新时检查版本号是否变化
- **冲突处理**：抛出`DaoRecordVersionMismatch`异常

#### 4.3.2 版本冲突解决策略
```java
try {
    userDao.update(user);
} catch (DaoRecordVersionMismatch e) {
    // 重新获取最新数据
    user = userDao.get(user.getId());
    // 重新应用变更
    user.setStatus("newStatus");
    userDao.update(user);
}
```

## 5. 扩展机制

### 5.1 自定义DAO实现
通过继承`JdbcDao`或`JdbcVersionedRecordDao`实现自定义DAO：

```java
public class UserDao extends JdbcVersionedRecordDao<String, User> {
    public UserDao(NamedParameterJdbcTemplate template) {
        super("user_table", User.class, "`", template, new Jackson2PersistenceHelper());
    }
    
    // 自定义查询方法
    public List<User> findByEmail(String email) {
        return filter(Criteria.where("email", email)).fetchAll();
    }
}
```

### 5.2 自定义持久化助手
支持自定义对象-关系映射：

```java
public class CustomPersistenceHelper implements PersistenceHelper {
    // 自定义序列化/反序列化逻辑
}
```

### 5.3 数据库方言支持
- **MySQL**：使用反引号`` ` ``作为标识符引用符
- **PostgreSQL**：使用双引号`"`作为标识符引用符
- **Oracle**：通过构造函数配置不同的引用符

## 6. 异常处理设计

### 6.1 异常层次结构
```
DaoException (抽象基类)
├── DaoClientException (客户端异常)
├── DaoSystemException (系统异常)
├── DaoCriticalException (严重异常)
├── DaoIntegrityViolation (完整性约束异常)
├── DaoRecordNotExists (记录不存在异常)
├── DaoRecordVersionMismatch (版本冲突异常)
└── DaoMissingPrimaryKey (主键缺失异常)
```

### 6.2 异常处理策略
- **可恢复异常**：如版本冲突，可重试处理
- **不可恢复异常**：如严重系统错误，需要人工干预
- **业务异常**：如数据完整性违反，需要业务逻辑处理

## 7. 性能优化

### 7.1 查询优化
- **字段选择**：支持指定查询字段，减少数据传输
- **索引利用**：通过合理的查询条件利用数据库索引
- **分页查询**：避免大数据集全表扫描

### 7.2 批量操作
- **批量插入**：支持批量数据插入
- **批量更新**：支持条件批量更新
- **批量删除**：支持条件批量删除

### 7.3 缓存策略
- **应用级缓存**：结合业务需求实现缓存策略
- **查询结果缓存**：对频繁查询的结果进行缓存

## 8. 安全设计

### 8.1 SQL注入防护
- **参数化查询**：所有SQL操作使用参数化查询
- **输入验证**：对用户输入进行严格的验证和过滤
- **SQL注入检测**：内置SQL注入攻击检测机制

### 8.2 数据权限控制
- **字段级权限**：支持字段级别的访问控制
- **行级权限**：支持基于用户的数据行访问控制
- **审计日志**：记录所有数据变更操作

## 9. 监控与运维

### 9.1 性能监控
- **查询耗时监控**：记录SQL执行时间
- **慢查询检测**：识别执行时间过长的查询
- **异常统计**：统计各类异常的发生频率

### 9.2 日志记录
- **操作日志**：记录所有CRUD操作
- **错误日志**：记录异常信息和堆栈
- **审计日志**：记录数据变更历史

## 10. 部署与配置

### 10.1 环境要求
- **Java版本**：Java 8+
- **Spring版本**：Spring 5.3.30+
- **数据库**：MySQL 5.7+、PostgreSQL 9.6+、Oracle 11g+

### 10.2 依赖配置
```xml
<dependency>
    <groupId>com.wosai.pay</groupId>
    <artifactId>pay-common-data-jdbc</artifactId>
    <version>1.2.7-SNAPSHOT</version>
</dependency>
```

### 10.3 数据源配置
```java
@Configuration
public class DataConfig {
    @Bean
    public NamedParameterJdbcTemplate jdbcTemplate(DataSource dataSource) {
        return new NamedParameterJdbcTemplate(dataSource);
    }
    
    @Bean
    public UserDao userDao(NamedParameterJdbcTemplate jdbcTemplate) {
        return new UserDao(jdbcTemplate);
    }
}
```