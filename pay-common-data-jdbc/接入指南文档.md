# 数据库访问层模块接入指南

## 1. 快速开始

### 1.1 环境准备

#### 1.1.1 系统要求
- Java 8 或更高版本
- Maven 3.6+ 或 Gradle 6+
- Spring Framework 5.3.30+
- 数据库：MySQL 5.7+ / PostgreSQL 9.6+ / Oracle 11g+

#### 1.1.2 添加依赖

**Maven 依赖：**
```xml
<!-- 主依赖 -->
<dependency>
    <groupId>com.wosai.pay</groupId>
    <artifactId>pay-common-data-jdbc</artifactId>
    <version>1.2.7-SNAPSHOT</version>
</dependency>

<!-- Spring JDBC -->
<dependency>
    <groupId>org.springframework</groupId>
    <artifactId>spring-jdbc</artifactId>
    <version>5.3.30</version>
</dependency>

<!-- 数据库驱动（根据实际数据库选择） -->
<dependency>
    <groupId>mysql</groupId>
    <artifactId>mysql-connector-java</artifactId>
    <version>8.0.33</version>
</dependency>
```

**Gradle 依赖：**
```gradle
implementation 'com.wosai.pay:pay-common-data-jdbc:1.2.7-SNAPSHOT'
implementation 'org.springframework:spring-jdbc:5.3.30'
implementation 'mysql:mysql-connector-java:8.0.33'
```

### 1.2 基础配置

#### 1.2.1 Spring 配置
```java
@Configuration
public class DatabaseConfig {
    
    @Bean
    public DataSource dataSource() {
        DriverManagerDataSource dataSource = new DriverManagerDataSource();
        dataSource.setDriverClassName("com.mysql.cj.jdbc.Driver");
        dataSource.setUrl("*****************************************?useSSL=false&serverTimezone=UTC");
        dataSource.setUsername("username");
        dataSource.setPassword("password");
        return dataSource;
    }
    
    @Bean
    public NamedParameterJdbcTemplate namedParameterJdbcTemplate(DataSource dataSource) {
        return new NamedParameterJdbcTemplate(dataSource);
    }
}
```

#### 1.2.2 数据库连接池配置（推荐）
```java
@Configuration
public class DatabaseConfig {
    
    @Bean
    public DataSource dataSource() {
        HikariConfig config = new HikariConfig();
        config.setJdbcUrl("*****************************************");
        config.setUsername("username");
        config.setPassword("password");
        config.setDriverClassName("com.mysql.cj.jdbc.Driver");
        config.setMaximumPoolSize(10);
        config.setMinimumIdle(5);
        config.setConnectionTimeout(30000);
        config.setIdleTimeout(600000);
        config.setMaxLifetime(1800000);
        
        return new HikariDataSource(config);
    }
}
```

## 2. 实体类设计

### 2.1 基础实体类

#### 2.1.1 普通实体（无版本控制）
```java
import com.wosai.pay.common.data.Record;

public class User extends Record<Long> {
    private String username;
    private String email;
    private Integer age;
    private String status;
    
    // 构造函数
    public User() {}
    
    public User(String username, String email, Integer age) {
        this.username = username;
        this.email = email;
        this.age = age;
        this.status = "active";
    }
    
    // Getters and Setters
    public String getUsername() { return username; }
    public void setUsername(String username) { this.username = username; }
    
    public String getEmail() { return email; }
    public void setEmail(String email) { this.email = email; }
    
    public Integer getAge() { return age; }
    public void setAge(Integer age) { this.age = age; }
    
    public String getStatus() { return status; }
    public void setStatus(String status) { this.status = status; }
}
```

#### 2.1.2 版本化实体（推荐）
```java
import com.wosai.pay.common.data.VersionedRecord;

public class User extends VersionedRecord<Long> {
    private String username;
    private String email;
    private Integer age;
    private String status;
    private String phone;
    private String address;
    
    // 构造函数和getters/setters同上
}
```

### 2.2 复杂对象支持

#### 2.2.1 JSON字段映射
```java
public class User extends VersionedRecord<Long> {
    private String username;
    private String email;
    private Map<String, Object> extraInfo;  // JSON字段
    
    // getters/setters
}
```

#### 2.2.2 嵌套对象
```java
public class User extends VersionedRecord<Long> {
    private String username;
    private Profile profile;  // 嵌套对象
    
    // getters/setters
}

public class Profile {
    private String avatar;
    private String bio;
    private Map<String, String> preferences;
    
    // getters/setters
}
```

## 3. DAO实现

### 3.1 基础DAO实现

#### 3.1.1 普通DAO
```java
import com.wosai.pay.common.data.jdbc.JdbcDao;
import com.wosai.pay.common.data.Jackson2PersistenceHelper;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;

public class UserDao extends JdbcDao<Long, User> {
    
    private static final String TABLE_NAME = "users";
    
    public UserDao(NamedParameterJdbcTemplate namedParameterJdbcTemplate) {
        super(TABLE_NAME, User.class, "`", namedParameterJdbcTemplate, new Jackson2PersistenceHelper());
    }
    
    // 可以添加自定义查询方法
    public List<User> findByEmail(String email) {
        return filter(Criteria.where("email", email)).fetchAll();
    }
    
    public List<User> findActiveUsers() {
        return filter(Criteria.where("status", "active")).fetchAll();
    }
}
```

#### 3.1.2 版本化DAO（推荐）
```java
import com.wosai.pay.common.data.jdbc.JdbcVersionedRecordDao;

public class UserDao extends JdbcVersionedRecordDao<Long, User> {
    
    private static final String TABLE_NAME = "users";
    
    public UserDao(NamedParameterJdbcTemplate namedParameterJdbcTemplate) {
        super(TABLE_NAME, User.class, "`", namedParameterJdbcTemplate, new Jackson2PersistenceHelper());
    }
}
```

### 3.2 Spring Boot集成

#### 3.2.1 自动配置
```java
@SpringBootApplication
@EnableConfigurationProperties
public class Application {
    public static void main(String[] args) {
        SpringApplication.run(Application.class, args);
    }
}

@Configuration
public class DaoConfig {
    
    @Bean
    public UserDao userDao(NamedParameterJdbcTemplate namedParameterJdbcTemplate) {
        return new UserDao(namedParameterJdbcTemplate);
    }
    
    @Bean
    public ProductDao productDao(NamedParameterJdbcTemplate namedParameterJdbcTemplate) {
        return new ProductDao(namedParameterJdbcTemplate);
    }
}
```

## 4. 数据库表设计

### 4.1 基础表结构

#### 4.1.1 普通表
```sql
CREATE TABLE users (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    username VARCHAR(50) NOT NULL UNIQUE,
    email VARCHAR(100) NOT NULL UNIQUE,
    age INT,
    status VARCHAR(20) DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_status ON users(status);
```

#### 4.1.2 版本化表（推荐）
```sql
CREATE TABLE users (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    username VARCHAR(50) NOT NULL UNIQUE,
    email VARCHAR(100) NOT NULL UNIQUE,
    age INT,
    status VARCHAR(20) DEFAULT 'active',
    phone VARCHAR(20),
    address TEXT,
    extra_info JSON,
    version BIGINT DEFAULT 1,
    ctime BIGINT NOT NULL,
    mtime BIGINT NOT NULL,
    
    INDEX idx_users_email (email),
    INDEX idx_users_status (status),
    INDEX idx_users_ctime (ctime)
);
```

### 4.2 JSON字段支持

#### 4.2.1 MySQL 5.7+ JSON字段 BLOB字段
```sql
CREATE TABLE users (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    username VARCHAR(50) NOT NULL,
    extra_info JSON,
    roles JSON,
    version BIGINT DEFAULT 1,
    ctime BIGINT NOT NULL,
    mtime BIGINT NOT NULL
);
```

#### 4.2.2 PostgreSQL JSONB字段
```sql
CREATE TABLE users (
    id SERIAL PRIMARY KEY,
    username VARCHAR(50) NOT NULL,
    extra_info JSONB,
    roles JSONB,
    version BIGINT DEFAULT 1,
    ctime BIGINT NOT NULL,
    mtime BIGINT NOT NULL
);
```

## 5. CRUD操作示例

### 5.1 插入操作

#### 5.1.1 单个插入
```java
@Service
public class UserService {
    
    @Autowired
    private UserDao userDao;
    
    public User createUser(CreateUserRequest request) {
        User user = new User();
        user.setUsername(request.getUsername());
        user.setEmail(request.getEmail());
        user.setAge(request.getAge());
        user.setStatus("active");
        
        userDao.insert(user);
        return user;
    }
}
```

#### 5.1.2 批量插入
```java
public void batchCreateUsers(List<CreateUserRequest> requests) {
    requests.forEach(request -> {
        User user = new User();
        user.setUsername(request.getUsername());
        user.setEmail(request.getEmail());
        user.setAge(request.getAge());
        userDao.insert(user);
    });
}
```

### 5.2 查询操作

#### 5.2.1 主键查询
```java
public User getUser(Long userId) {
    return userDao.get(userId);
}

// 指定字段查询
public User getUserBasicInfo(Long userId) {
    return userDao.get(userId, "username", "email", "status");
}
```

#### 5.2.2 条件查询
```java
// 简单条件查询
public List<User> getActiveUsers() {
    return userDao.filter(Criteria.where("status", "active"))
                  .fetchAll();
}

// 复杂条件查询
public List<User> searchUsers(String keyword, Integer minAge, Integer maxAge) {
    return userDao.filter(Criteria.where("status", "active")
                                  .and("age", Criteria.gte(minAge))
                                  .and("age", Criteria.lte(maxAge))
                                  .and(Criteria.where("username").like("%" + keyword + "%")
                                               .or("email").like("%" + keyword + "%")))
                  .orderBy("ctime", Direction.DESC)
                  .limit(20)
                  .fetchAll();
}

// 分页查询
public Page<User> getUsersPaged(int pageNum, int pageSize) {
    long total = userDao.filter(Criteria.where("status", "active")).count();
    List<User> users = userDao.filter(Criteria.where("status", "active"))
                                .offset((pageNum - 1) * pageSize)
                                .limit(pageSize)
                                .fetchAll();
    return new PageImpl<>(users, PageRequest.of(pageNum - 1, pageSize), total);
}
```

#### 5.2.3 查询统计
```java
public long countActiveUsers() {
    return userDao.filter(Criteria.where("status", "active")).count();
}

public long countUsersByAgeRange(int minAge, int maxAge) {
    return userDao.filter(Criteria.where("age", Criteria.gte(minAge))
                                  .and("age", Criteria.lte(maxAge)))
                  .count();
}
```

### 5.3 更新操作

#### 5.3.1 全字段更新
```java
public User updateUser(Long userId, UpdateUserRequest request) {
    User user = userDao.get(userId);
    if (user == null) {
        throw new UserNotFoundException("User not found: " + userId);
    }
    
    user.setUsername(request.getUsername());
    user.setEmail(request.getEmail());
    user.setAge(request.getAge());
    
    userDao.update(user);
    return user;
}
```

#### 5.3.2 部分字段更新
```java
public User updateUserEmail(Long userId, String newEmail) {
    User user = userDao.get(userId);
    if (user == null) {
        throw new UserNotFoundException("User not found: " + userId);
    }
    
    user.setEmail(newEmail);
    userDao.update(user, "email");  // 只更新email字段
    return user;
}
```

#### 5.3.3 状态更新（版本控制）
```java
public User activateUser(Long userId) {
    User user = userDao.get(userId);
    if (user == null) {
        throw new UserNotFoundException("User not found: " + userId);
    }
    
    user.setStatus("active");
    try {
        userDao.update(user, "status");
    } catch (DaoRecordVersionMismatch e) {
        // 版本冲突，重新获取最新数据再更新
        user = userDao.get(userId);
        user.setStatus("active");
        userDao.update(user, "status");
    }
    
    return user;
}
```

### 5.4 删除操作
```java
public void deleteUser(Long userId) {
    try {
        userDao.delete(userId);
    } catch (DaoRecordNotExists e) {
        throw new UserNotFoundException("User not found: " + userId);
    }
}
```

## 6. 高级特性

### 6.1 自定义字段映射

#### 6.1.1 自定义持久化助手
```java
public class CustomPersistenceHelper implements PersistenceHelper {
    private final ObjectMapper objectMapper = new ObjectMapper();
    
    @Override
    public Map<String, Object> preWrite(Object entity) {
        Map<String, Object> map = new HashMap<>();
        try {
            String json = objectMapper.writeValueAsString(entity);
            map = objectMapper.readValue(json, Map.class);
        } catch (JsonProcessingException e) {
            throw new RuntimeException("Failed to serialize entity", e);
        }
        
        // 自定义字段处理
        if (entity instanceof User) {
            User user = (User) entity;
            if (user.getExtraInfo() != null) {
                map.put("extra_info", objectMapper.writeValueAsString(user.getExtraInfo()));
            }
        }
        
        return map;
    }
    
    @Override
    public <T> T postRead(Map<String, Object> data, Class<T> clazz) {
        try {
            // 处理JSON字段
            if (data.containsKey("extra_info") && data.get("extra_info") instanceof String) {
                String json = (String) data.get("extra_info");
                data.put("extra_info", objectMapper.readValue(json, Map.class));
            }
            
            return objectMapper.convertValue(data, clazz);
        } catch (JsonProcessingException e) {
            throw new RuntimeException("Failed to deserialize data", e);
        }
    }
}
```

### 6.2 自定义查询方法

#### 6.2.1 在DAO中添加自定义方法
```java
public class UserDao extends JdbcVersionedRecordDao<Long, User> {
    
    public UserDao(NamedParameterJdbcTemplate namedParameterJdbcTemplate) {
        super("users", User.class, "`", namedParameterJdbcTemplate, new Jackson2PersistenceHelper());
    }
    
    // 自定义查询：根据邮箱查找用户
    public User findByEmail(String email) {
        return filter(Criteria.where("email", email)).fetchOne();
    }
    
    // 自定义查询：根据用户名模糊搜索
    public List<User> searchByUsername(String username) {
        return filter(Criteria.where("username").like("%" + username + "%"))
                  .fetchAll();
    }
    
    // 自定义查询：获取特定状态的用户
    public List<User> findByStatus(String status, int limit) {
        return filter(Criteria.where("status", status))
                  .limit(limit)
                  .fetchAll();
    }
    
    // 自定义查询：获取最近注册的用户
    public List<User> findRecentlyRegistered(int days) {
        long cutoffTime = System.currentTimeMillis() - (days * 24 * 60 * 60 * 1000L);
        return filter(Criteria.where("ctime", Criteria.gte(cutoffTime)))
                  .orderBy("ctime", Direction.DESC)
                  .fetchAll();
    }
}
```

### 6.3 事务管理

#### 6.3.1 Spring事务注解
```java
@Service
public class UserService {
    
    @Autowired
    private UserDao userDao;
    
    @Autowired
    private EmailService emailService;
    
    @Transactional
    public User registerUser(CreateUserRequest request) {
        // 创建用户
        User user = new User();
        user.setUsername(request.getUsername());
        user.setEmail(request.getEmail());
        user.setStatus("pending");
        userDao.insert(user);
        
        // 发送验证邮件
        emailService.sendVerificationEmail(user.getEmail());
        
        return user;
    }
    
    @Transactional
    public void transferUser(Long fromUserId, Long toUserId, String resource) {
        User fromUser = userDao.get(fromUserId);
        User toUser = userDao.get(toUserId);
        
        if (fromUser == null || toUser == null) {
            throw new UserNotFoundException("User not found");
        }
        
        // 更新两个用户的状态
        fromUser.setStatus("transferred");
        toUser.setStatus("received");
        
        userDao.update(fromUser);
        userDao.update(toUser);
    }
}
```

## 7. 异常处理

### 7.1 全局异常处理
```java
@RestControllerAdvice
public class GlobalExceptionHandler {
    
    @ExceptionHandler(DaoRecordNotExists.class)
    public ResponseEntity<ErrorResponse> handleRecordNotFound(DaoRecordNotExists e) {
        ErrorResponse error = new ErrorResponse("RECORD_NOT_FOUND", e.getMessage());
        return ResponseEntity.status(HttpStatus.NOT_FOUND).body(error);
    }
    
    @ExceptionHandler(DaoRecordVersionMismatch.class)
    public ResponseEntity<ErrorResponse> handleVersionMismatch(DaoRecordVersionMismatch e) {
        ErrorResponse error = new ErrorResponse("VERSION_MISMATCH", "数据已被其他用户修改，请刷新后重试");
        return ResponseEntity.status(HttpStatus.CONFLICT).body(error);
    }
    
    @ExceptionHandler(DaoIntegrityViolation.class)
    public ResponseEntity<ErrorResponse> handleIntegrityViolation(DaoIntegrityViolation e) {
        ErrorResponse error = new ErrorResponse("INTEGRITY_VIOLATION", "数据完整性约束违反");
        return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(error);
    }
}
```

### 7.2 业务异常处理
```java
@Service
public class UserService {
    
    @Autowired
    private UserDao userDao;
    
    public User updateUserWithRetry(Long userId, UserUpdateRequest request) {
        int maxRetries = 3;
        for (int i = 0; i < maxRetries; i++) {
            try {
                User user = userDao.get(userId);
                if (user == null) {
                    throw new UserNotFoundException("User not found: " + userId);
                }
                
                // 应用更新
                user.setUsername(request.getUsername());
                user.setEmail(request.getEmail());
                
                userDao.update(user);
                return user;
                
            } catch (DaoRecordVersionMismatch e) {
                if (i == maxRetries - 1) {
                    throw new OptimisticLockingFailureException("更新失败：数据冲突");
                }
                // 重试前等待
                try {
                    Thread.sleep(100);
                } catch (InterruptedException ie) {
                    Thread.currentThread().interrupt();
                    throw new RuntimeException("更新中断", ie);
                }
            }
        }
        throw new RuntimeException("更新失败");
    }
}
```

## 8. 测试指南

### 8.1 单元测试

#### 8.1.1 使用内存数据库测试
```java
@RunWith(SpringRunner.class)
@ContextConfiguration(classes = TestConfig.class)
public class UserDaoTest {
    
    @Autowired
    private UserDao userDao;
    
    @Before
    public void setUp() {
        // 初始化测试数据
    }
    
    @Test
    public void testCreateUser() {
        User user = new User();
        user.setUsername("testuser");
        user.setEmail("<EMAIL>");
        user.setAge(25);
        
        userDao.insert(user);
        assertNotNull(user.getId());
    }
    
    @Test
    public void testGetUser() {
        User user = userDao.get(1L);
        assertNotNull(user);
        assertEquals("testuser", user.getUsername());
    }
    
    @Test
    public void testUpdateUser() {
        User user = userDao.get(1L);
        user.setEmail("<EMAIL>");
        
        userDao.update(user);
        
        User updated = userDao.get(1L);
        assertEquals("<EMAIL>", updated.getEmail());
    }
    
    @Test
    public void testDeleteUser() {
        userDao.delete(1L);
        
        User user = userDao.get(1L);
        assertNull(user);
    }
    
    @Test
    public void testSearchUsers() {
        List<User> users = userDao.filter(Criteria.where("age", Criteria.gte(18)))
                                    .fetchAll(User.class);
        assertFalse(users.isEmpty());
    }
}
```

#### 8.1.2 测试配置
```java
@Configuration
public class TestConfig {
    
    @Bean
    public DataSource dataSource() {
        return new EmbeddedDatabaseBuilder()
                .setType(EmbeddedDatabaseType.HSQL)
                .addScript("classpath:schema.sql")
                .addScript("classpath:test-data.sql")
                .build();
    }
    
    @Bean
    public NamedParameterJdbcTemplate namedParameterJdbcTemplate(DataSource dataSource) {
        return new NamedParameterJdbcTemplate(dataSource);
    }
    
    @Bean
    public UserDao userDao(NamedParameterJdbcTemplate namedParameterJdbcTemplate) {
        return new UserDao(namedParameterJdbcTemplate);
    }
}
```

### 8.2 集成测试
```java
@RunWith(SpringRunner.class)
@SpringBootTest
@Transactional
public class UserServiceIntegrationTest {
    
    @Autowired
    private UserService userService;
    
    @Test
    public void testUserRegistrationFlow() {
        CreateUserRequest request = new CreateUserRequest();
        request.setUsername("integrationtest");
        request.setEmail("<EMAIL>");
        request.setAge(30);
        
        User user = userService.createUser(request);
        assertNotNull(user);
        assertNotNull(user.getId());
        assertEquals("integrationtest", user.getUsername());
        
        // 验证用户可以被查询到
        User found = userService.getUser(user.getId());
        assertEquals(user.getId(), found.getId());
    }
}
```

## 9. 性能优化

### 9.1 索引优化
```sql
-- 常用查询字段索引
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_status_age ON users(status, age);
CREATE INDEX idx_users_ctime ON users(ctime);

-- 复合索引
CREATE INDEX idx_users_status_ctime ON users(status, ctime);
```

### 9.2 查询优化
```java
// 只查询需要的字段
public List<UserSummary> getUserSummaries() {
    return userDao.filter(Criteria.where("status", "active"))
                  .fetchAll();  // 使用DTO减少数据传输
}

// 分页查询
public Page<User> getUsersPaged(int page, int size) {
    return userDao.filter(Criteria.where("status", "active"))
                  .orderBy("ctime", Direction.DESC)
                  .offset(page * size)
                  .limit(size)
                  .fetchAll();
}
```

### 9.3 批量操作优化
```java
@Transactional
public void batchUpdateUserStatus(List<Long> userIds, String newStatus) {
    userIds.forEach(userId -> {
        User user = userDao.get(userId);
        if (user != null) {
            user.setStatus(newStatus);
            userDao.update(user, "status");
        }
    });
}
```

## 10. 常见问题与解决方案

### 10.1 版本冲突处理
```java
// 解决方案1：自动重试
public void updateWithAutoRetry(User user) {
    int maxRetries = 3;
    for (int i = 0; i < maxRetries; i++) {
        try {
            userDao.update(user);
            break;
        } catch (DaoRecordVersionMismatch e) {
            if (i == maxRetries - 1) throw e;
            user = userDao.get(user.getId()); // 重新获取最新数据
            // 重新应用变更
            user.setStatus("updated");
        }
    }
}

// 解决方案2：用户确认
public UpdateResult updateWithUserConfirmation(Long userId, UserUpdateRequest request) {
    User current = userDao.get(userId);
    User original = request.getOriginalVersion();
    
    if (!current.getVersion().equals(original.getVersion())) {
        return UpdateResult.conflict(current, original);
    }
    
    // 应用更新
    current.setUsername(request.getUsername());
    userDao.update(current);
    
    return UpdateResult.success(current);
}
```

### 10.2 大数据集处理
```java
// 使用流式处理
public void processLargeUserSet() {
    Iterator<User> iterator = userDao.filter(Criteria.where("status", "active"))
                                      .iterator(User.class);
    
    while (iterator.hasNext()) {
        User user = iterator.next();
        // 处理每个用户
        processUser(user);
    }
}

// 分页处理
public void processUsersInBatches(int batchSize) {
    long total = userDao.filter(Criteria.where("status", "active")).count();
    
    for (int offset = 0; offset < total; offset += batchSize) {
        List<User> batch = userDao.filter(Criteria.where("status", "active"))
                                   .offset(offset)
                                   .limit(batchSize)
                                   .fetchAll(User.class);
        
        processBatch(batch);
    }
}
```

### 10.3 连接池配置问题
```yaml
# application.yml
spring:
  datasource:
    url: *****************************************
    username: username
    password: password
    driver-class-name: com.mysql.cj.jdbc.Driver
    hikari:
      maximum-pool-size: 20
      minimum-idle: 5
      connection-timeout: 30000
      idle-timeout: 600000
      max-lifetime: 1800000
      leak-detection-threshold: 60000
```

## 11. 监控与运维

### 11.1 应用监控
```java
@Component
public class DaoMetrics {
    
    private final MeterRegistry meterRegistry;
    
    public DaoMetrics(MeterRegistry meterRegistry) {
        this.meterRegistry = meterRegistry;
    }
    
    public void recordQuery(String operation, long duration) {
        meterRegistry.timer("dao.operation.duration", "operation", operation)
                    .record(duration, TimeUnit.MILLISECONDS);
    }
    
    public void recordError(String operation, String errorType) {
        meterRegistry.counter("dao.operation.errors", 
                            "operation", operation, 
                            "error", errorType)
                    .increment();
    }
}
```

### 11.2 日志配置
```xml
<!-- logback-spring.xml -->
<configuration>
    <appender name="DAO" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>logs/dao.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>logs/dao.%d{yyyy-MM-dd}.log</fileNamePattern>
            <maxHistory>30</maxHistory>
        </rollingPolicy>
        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n</pattern>
        </encoder>
    </appender>
    
    <logger name="com.wosai.pay.common.data.jdbc" level="DEBUG" additivity="false">
        <appender-ref ref="DAO"/>
    </logger>
</configuration>
```