package com.wosai.pay.common.sensitive.apollo;

import com.ctrip.framework.apollo.Config;
import com.ctrip.framework.apollo.ConfigService;
import com.wosai.pay.common.base.util.JsonUtil;
import com.wosai.pay.common.sensitive.apollo.exception.SensitiveApolloException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.lang.reflect.Field;
import java.lang.annotation.Annotation;
import javax.annotation.Nullable;

public class SensitivePropertiesLoader<T> {
    private final static Logger log = LoggerFactory.getLogger(SensitivePropertiesLoader.class);

    public static Config config = ConfigService.getAppConfig();
    private static final String KEY_SENSITIVE_PROPERTIES = "sensitive_properties";
    private T sensitiveProperties;  // 保存业务类使用的配置
    private final Class<T> clazz;
    private static volatile SensitivePropertiesLoader<?> instance;

    private SensitivePropertiesLoader(Class<T> clazz) {
        this.clazz = clazz;
        init();
    }

    private void init() {
        loadSensitiveProperties();
        config.addChangeListener(listener -> {
            if (listener.isChanged(KEY_SENSITIVE_PROPERTIES)) {
                loadSensitiveProperties();
            }
        });
    }

    public void loadSensitiveProperties() {
        try {
            this.sensitiveProperties = buildSensitiveProperties();
        } catch (SensitiveApolloException e) {
            log.error("敏感信息配置加载失败：" + e.getMessage(), e);
            throw new RuntimeException(e);
        }
    }

    private T buildSensitiveProperties() throws SensitiveApolloException {
        String property = config.getProperty(KEY_SENSITIVE_PROPERTIES, "{}");
        T newSensitiveProperties;
        try {
            newSensitiveProperties = JsonUtil.jsonStringToObject(property, clazz);
            validateSensitiveProperties(newSensitiveProperties);
        } catch (Exception e) {
            if (e instanceof SensitiveApolloException) {
                throw (SensitiveApolloException) e;
            } else {
                throw new SensitiveApolloException(e.getMessage(), e);
            }
        }

        return newSensitiveProperties;
    }

    public static <T> T getSensitiveProperties(Class<T> clazz) {
        return getInstance(clazz).sensitiveProperties;
    }

    /**
     * 方法功能：用于业务类注册自己的回调方法，在Apollo配置更新后执行注册的方法
     * 代码流程：1.判断是否已经有instance实例，只有instance实例才可以加载配置。
     *         2.添加监听器，当配置发生变化时，首先加载最新的配置，然后再执行回调方法。
     * @param runnable
     */
    public static void addListener(Runnable runnable) {
        if (instance == null){
            log.error("listener回调方法执行失败，请先初始化加载器");
            return;
        }
        config.addChangeListener(listener -> {
            if (listener.isChanged(KEY_SENSITIVE_PROPERTIES)) {
                try {
                    instance.loadSensitiveProperties();
                    runnable.run();
                } catch (Exception e) {
                    log.error("listener回调方法执行失败：" + e.getMessage(), e);
                }
            }
        });
    }


    private static <T> SensitivePropertiesLoader<T> getInstance(Class<T> clazz) {
        if (instance == null) {
            synchronized (SensitivePropertiesLoader.class) {
                if (instance == null) {
                    instance = new SensitivePropertiesLoader<>(clazz);
                }
            }
        }
        return (SensitivePropertiesLoader<T>) instance;
    }


    private void validateSensitiveProperties(T sensitiveProperties) throws SensitiveApolloException {
        if (sensitiveProperties == null) {
            throw new SensitiveApolloException("敏感属性对象不能为空");
        }
        try {
            for (Field field : sensitiveProperties.getClass().getDeclaredFields()) {
                field.setAccessible(true); // 允许访问私有字段
                Object value = field.get(sensitiveProperties);

                // 检查字段是否具有 @Nullable 注解
                boolean isNullable = false;
                for (Annotation annotation : field.getAnnotations()) {
                    if (annotation.annotationType().equals(Nullable.class)) {
                        isNullable = true;
                        break;
                    }
                }

                if (!isNullable && value == null) {
                    throw new SensitiveApolloException("敏感属性字段： " + field.getName() + " 不能为空");
                }
            }
        } catch (IllegalAccessException e) {
            throw new SensitiveApolloException("检查敏感属性时出错：" + e.getMessage(), e);
        }
    }
}
