package example;

import com.wosai.pay.common.sensitive.apollo.SensitivePropertiesLoader;

public class SensitivePropertiesLoaderExample {
    // 客户端应用
    Client client;

    // 加载配置
    SensitiveProperties sensitiveProperties = SensitivePropertiesLoader.getSensitiveProperties(SensitiveProperties.class);

    // 创建客户端应用
    public void buildClient() {
        client = new Client(sensitiveProperties.getUserName(), sensitiveProperties.getPassword());
    }

    //添加回调函数，编写当配置更新时应该进行的操作，如加载最新配置来配置客户端应用
    public void addListenerTest() {
        SensitivePropertiesLoader.addListener(new Runnable() {
            @Override
            public void run() {
                sensitiveProperties = SensitivePropertiesLoader.getSensitiveProperties(SensitiveProperties.class);
                buildClient();
            }
        });
    }
}
