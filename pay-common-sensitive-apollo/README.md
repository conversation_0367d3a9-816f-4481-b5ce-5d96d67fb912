# pay-common-sensitive-apollo

是一个用于加载Apollo中敏感信息的工具包，其核心是SensitivePropertiesLoader类，旨在从 Apollo 配置服务中加载key值配置为"sensitive_properties"的敏感属性。它确保敏感信息在应用程序启动时能够正确加载，并在配置发生变化时动态更新。

## 使用方法

1. 在本地项目中导入maven坐标，请优先使用最新版本。

   ```
   <dependency>
       <groupId>com.wosai.pay</groupId>
       <artifactId>pay-common-sensitive-apollo</artifactId>
       <version>1.1.6</version>
   </dependency>
   ```

2. 在本地新建SensitiveProperties类，用于保存需要获取的敏感信息。SensitivePropertiesLoader在从Apollo加载配置时会检查每个属性是否为null，如有为null的则会抛出SensitiveApolloException异常，因此需要在配置可以为null的属性上添加@Nullable（javax.annotation.Nullable）注解来通知SensitivePropertiesLoader该属性可以为null。

   ```java
   public class SensitiveProperties {
       @Nullable
       private String userName;
       private String password;
   
       public String getUserName() {
           return userName;
       }
   
       public void setUserName(String userName) {
           this.userName = userName;
       }
   
       public String getPassword() {
           return password;
       }
   
       public void setPassword(String password) {
           this.password = password;
       }
   }
   ```

3. 在Apollo中配置key值为"sensitive_properties"，value为JSON格式的数据。

   ```
   {
       "userName": "root",
       "password": "123456"
   }
   ```



4. 业务类获取配置。

   方式一

   使用bean注入。此方式会生成一个SensitiveProperties Bean，读取到的敏感信息配置为加载时的初始配置，当Apollo中配置发生变化时SensitiveProperties Bean中的属性不会动态变化。

   ```java
   @Configuration
   public class SensitivePropertiesConfig {
       @Bean
       public SensitiveProperties sensitiveProperties() {
           SensitiveProperties sensitiveProperties = SensitivePropertiesLoader.getSensitiveProperties(SensitiveProperties.class);
           return sensitiveProperties;
       }
   }
   ```

   然后在使用的地方使用Bean注入的方式注入即可

   ```java
   @Autowired
   SensitiveProperties sensitiveProperties;
   ```

   方式二

   直接使用SensitivePropertiesLoader.getSensitiveProperties（）方法加载。上述代码每次执行时，可加载最新的Apollo配置。

   ```java
   SensitiveProperties sensitiveProperties = SensitivePropertiesLoader.getSensitiveProperties(SensitiveProperties.class);
   ```

4. 注册回调方法（可选）。当Apollo配置发生更新时可执行注册的方法。

   ```java
   SensitivePropertiesLoader.addListener(new Runnable() {
       @Override
       public void run() {
           // 编写需要执行的操作
       }
   });
   ```

