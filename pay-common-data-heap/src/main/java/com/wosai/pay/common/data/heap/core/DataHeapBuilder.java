package com.wosai.pay.common.data.heap.core;

import com.wosai.pay.common.data.heap.processor.EdgeRecordProcessor;
import com.wosai.pay.common.data.heap.config.DataHeapConfig;
import com.wosai.pay.common.data.heap.interfaces.DataLoader;
import com.wosai.pay.common.data.heap.interfaces.EdgeRecordTimeAndIdExtractor;
import javaslang.Tuple2;

import java.lang.reflect.Field;
import java.util.List;
import java.util.Map;
import java.util.function.Function;

/**
 * 数据堆构建器，提供简化的API来创建MultiEntityDataHeap实例
 * 支持链式调用和预设模板，降低接入复杂度
 *
 * @param <T> 数据记录类型
 * @param <P> 查询参数类型
 */
public class DataHeapBuilder<T, P> {
    
    private List<String> entityIds;
    private P queryParams;
    private int currentPage = 1;
    private int lastRecordPage = 0;
    private Long lastRecordTime;
    private DataLoader<T, P> dataLoader;
    private EdgeRecordTimeAndIdExtractor<T> timeExtractor;
    private DataHeapConfig config = DataHeapConfig.defaultConfig();
    private Boolean isAppend = false;
    
    // 用于简化配置的字段
    private String timeFieldName;
    private String idFieldName;
    private Class<T> recordClass;
    
    /**
     * 私有构造函数，通过静态方法创建实例
     */
    private DataHeapBuilder() {}
    
    /**
     * 创建一个新的数据堆构建器
     *
     * @param <T> 数据记录类型
     * @param <P> 查询参数类型
     * @return 数据堆构建器实例
     */
    public static <T, P> DataHeapBuilder<T, P> create() {
        return new DataHeapBuilder<T, P>();
    }
    
    /**
     * 为指定的记录类型创建构建器
     *
     * @param recordClass 记录类型
     * @param <T> 数据记录类型
     * @param <P> 查询参数类型
     * @return 数据堆构建器实例
     */
    public static <T, P> DataHeapBuilder<T, P> forType(Class<T> recordClass) {
        DataHeapBuilder<T, P> builder = new DataHeapBuilder<T, P>();
        builder.recordClass = recordClass;
        return builder;
    }
    
    /**
     * 创建使用Map作为查询参数的构建器（向后兼容）
     *
     * @param <T> 数据记录类型
     * @return 数据堆构建器实例
     */
    public static <T> DataHeapBuilder<T, Map<String, Object>> createWithMapParams() {
        return new DataHeapBuilder<T, Map<String, Object>>();
    }
    
    /**
     * 为指定的记录类型创建使用Map作为查询参数的构建器（向后兼容）
     *
     * @param recordClass 记录类型
     * @param <T> 数据记录类型
     * @return 数据堆构建器实例
     */
    public static <T> DataHeapBuilder<T, Map<String, Object>> forTypeWithMapParams(Class<T> recordClass) {
        DataHeapBuilder<T, Map<String, Object>> builder = new DataHeapBuilder<T, Map<String, Object>>();
        builder.recordClass = recordClass;
        return builder;
    }

    public DataHeapBuilder<T, P> recordClass(Class<T> recordClass) {
        this.recordClass = recordClass;
        return this;
    }

    /**
     * 设置实体ID列表
     *
     * @param entityIds 实体ID列表
     * @return 构建器实例
     */
    public DataHeapBuilder<T, P> entityIds(List<String> entityIds) {
        this.entityIds = entityIds;
        return this;
    }
    
    /**
     * 设置查询参数
     *
     * @param queryParams 查询参数
     * @return 构建器实例
     */
    public DataHeapBuilder<T, P> queryParams(P queryParams) {
        this.queryParams = queryParams;
        return this;
    }
    
    /**
     * 设置当前页码
     *
     * @param currentPage 当前页码
     * @return 构建器实例
     */
    public DataHeapBuilder<T, P> currentPage(int currentPage) {
        this.currentPage = currentPage;
        return this;
    }
    
    /**
     * 设置上一次请求的页码
     *
     * @param lastRecordPage 上一次请求的页码
     * @return 构建器实例
     */
    public DataHeapBuilder<T, P> lastRecordPage(int lastRecordPage) {
        this.lastRecordPage = lastRecordPage;
        return this;
    }
    
    /**
     * 设置边界时间
     *
     * @param lastRecordTime 边界时间
     * @return 构建器实例
     */
    public DataHeapBuilder<T, P> lastRecordTime(Long lastRecordTime) {
        this.lastRecordTime = lastRecordTime;
        return this;
    }
    
    /**
     * 设置数据加载器
     *
     * @param dataLoader 数据加载器
     * @return 构建器实例
     */
    public DataHeapBuilder<T, P> dataLoader(DataLoader<T, P> dataLoader) {
        this.dataLoader = dataLoader;
        return this;
    }
    
    /**
     * 设置时间提取器
     *
     * @param timeExtractor 时间提取器
     * @return 构建器实例
     */
    public DataHeapBuilder<T, P> timeExtractor(EdgeRecordTimeAndIdExtractor<T> timeExtractor) {
        this.timeExtractor = timeExtractor;
        return this;
    }
    
    /**
     * 设置配置
     *
     * @param config 配置
     * @return 构建器实例
     */
    public DataHeapBuilder<T, P> config(DataHeapConfig config) {
        this.config = config;
        return this;
    }
    
    /**
     * 设置是否追加数据
     *
     * @param isAppend 是否追加数据
     * @return 构建器实例
     */
    public DataHeapBuilder<T, P> append(Boolean isAppend) {
        this.isAppend = isAppend;
        return this;
    }
    
    /**
     * 设置时间字段名（用于反射提取时间）
     *
     * @param timeFieldName 时间字段名
     * @return 构建器实例
     */
    public DataHeapBuilder<T, P> timeField(String timeFieldName) {
        this.timeFieldName = timeFieldName;
        return this;
    }
    
    /**
     * 设置ID字段名（用于反射提取ID）
     *
     * @param idFieldName ID字段名
     * @return 构建器实例
     */
    public DataHeapBuilder<T, P> idField(String idFieldName) {
        this.idFieldName = idFieldName;
        return this;
    }
    
    /**
     * 使用函数式接口设置数据加载器
     *
     * @param loadFunction 数据加载函数
     * @return 构建器实例
     */
    public DataHeapBuilder<T, P> withLoader(LoadFunction<T, P> loadFunction) {
        this.dataLoader = new DataLoader<T, P>() {
            @Override
            public Tuple2<Long, List<T>> loadData(List<String> ids, P queryParams, int newPage, Long edgeTime) {
                return loadFunction.load(ids, queryParams, newPage, edgeTime);
            }
        };
        return this;
    }
    
    /**
     * 使用函数式接口设置时间和ID提取器
     *
     * @param timeFunction 时间提取函数
     * @param idFunction ID提取函数
     * @return 构建器实例
     */
    public DataHeapBuilder<T, P> withExtractors(Function<T, Long> timeFunction, Function<T, String> idFunction) {
        this.timeExtractor = new EdgeRecordTimeAndIdExtractor<T>() {
            @Override
            public long getRecordTime(T record) {
                return timeFunction.apply(record);
            }
            
            @Override
            public String getRecordId(T record) {
                return idFunction.apply(record);
            }
        };
        return this;
    }
    
    /**
     * 构建MultiEntityDataHeap实例
     *
     * @return MultiEntityDataHeap实例
     * @throws IllegalStateException 如果必要的参数未设置
     */
    public MultiEntityDataHeap<T, P> build() {
        validateAndPrepare();
        
        return new MultiEntityDataHeap<>(
                entityIds,
                queryParams,
                currentPage,
                lastRecordPage,
                lastRecordTime,
                dataLoader,
                isAppend,
                timeExtractor,
                new EdgeRecordProcessor<>(timeExtractor, config.isThrowOnBoundaryCollapse()),
                config
        );
    }
    
    /**
     * 验证参数并准备构建
     */
    private void validateAndPrepare() {
        if (entityIds == null || entityIds.isEmpty()) {
            throw new IllegalStateException("Entity IDs must be provided");
        }
        
        if (dataLoader == null) {
            throw new IllegalStateException("DataLoader must be provided");
        }
        
        if (timeExtractor == null) {
            // 尝试使用反射创建时间提取器
            if (timeFieldName != null && idFieldName != null && recordClass != null) {
                timeExtractor = createReflectionTimeExtractor();
            } else {
                throw new IllegalStateException("TimeExtractor must be provided or timeField/idField/recordClass must be set");
            }
        }
        
        // 为向后兼容的Map查询参数版本提供默认初始化
        if (queryParams == null && this instanceof DataHeapBuilder) {
            try {
                // 仅当P是Map<String, Object>类型时才初始化
                @SuppressWarnings("unchecked")
                P defaultParams = (P) new java.util.HashMap<String, Object>();
                queryParams = defaultParams;
            } catch (ClassCastException e) {
                // 如果P不是Map类型，则不进行默认初始化
                // 调用者需要显式设置queryParams
            }
        }
    }
    
    /**
     * 使用反射创建时间提取器
     */
    private EdgeRecordTimeAndIdExtractor<T> createReflectionTimeExtractor() {
        return new EdgeRecordTimeAndIdExtractor<T>() {
            @Override
            public long getRecordTime(T record) {
                try {
                    Field timeField = recordClass.getDeclaredField(timeFieldName);
                    timeField.setAccessible(true);
                    Object value = timeField.get(record);
                    if (value instanceof Long) {
                        return (Long) value;
                    } else if (value instanceof Integer) {
                        return ((Integer) value).longValue();
                    } else {
                        throw new IllegalArgumentException("Time field must be Long or Integer type");
                    }
                } catch (Exception e) {
                    throw new RuntimeException("Failed to extract time from field: " + timeFieldName, e);
                }
            }
            
            @Override
            public String getRecordId(T record) {
                try {
                    Field idField = recordClass.getDeclaredField(idFieldName);
                    idField.setAccessible(true);
                    Object value = idField.get(record);
                    return value != null ? value.toString() : null;
                } catch (Exception e) {
                    throw new RuntimeException("Failed to extract ID from field: " + idFieldName, e);
                }
            }
        };
    }
    
    /**
     * 数据加载函数式接口
     */
    @FunctionalInterface
    public interface LoadFunction<T, P> {
        Tuple2<Long, List<T>> load(List<String> ids, P queryParams, int newPage, Long edgeTime);
    }
}