package com.wosai.pay.common.data.heap.utils;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.List;

/**
 * 批处理工具类，提供将大量数据分割成多个小批次的功能
 */
public class BatchUtil {
    
    private BatchUtil() {
        // 工具类禁止实例化
    }
    
    /**
     * 将集合分割成多个子集合，每个子集合的大小不超过指定的批次大小
     *
     * @param <T> 集合元素类型
     * @param collection 要分割的集合
     * @param batchSize 每个批次的最大大小
     * @return 分割后的多个子集合列表
     */
    public static <T> List<List<T>> partition(Collection<T> collection, int batchSize) {
        if (collection == null || collection.isEmpty()) {
            return Collections.emptyList();
        }
        
        if (batchSize <= 0) {
            throw new IllegalArgumentException("批次大小必须大于0");
        }
        
        List<List<T>> result = new ArrayList<>();
        List<T> currentBatch = new ArrayList<>(batchSize);
        
        for (T item : collection) {
            currentBatch.add(item);
            
            if (currentBatch.size() >= batchSize) {
                result.add(new ArrayList<>(currentBatch));
                currentBatch.clear();
            }
        }
        
        // 添加最后一个可能不满的批次
        if (!currentBatch.isEmpty()) {
            result.add(new ArrayList<>(currentBatch));
        }
        
        return result;
    }
    
    /**
     * 将集合分割成指定数量的子集合，尽量使每个子集合大小相同
     *
     * @param <T> 集合元素类型
     * @param collection 要分割的集合
     * @param numParts 要分割成的部分数
     * @return 分割后的多个子集合列表
     */
    public static <T> List<List<T>> partitionByParts(Collection<T> collection, int numParts) {
        if (collection == null || collection.isEmpty()) {
            return Collections.emptyList();
        }
        
        if (numParts <= 0) {
            throw new IllegalArgumentException("分割部分数必须大于0");
        }
        
        // 如果集合小于分割数，直接每个元素一个分组
        if (collection.size() <= numParts) {
            return partition(collection, 1);
        }
        
        // 计算每个分组的大小
        int batchSize = (int) Math.ceil((double) collection.size() / numParts);
        return partition(collection, batchSize);
    }
}
