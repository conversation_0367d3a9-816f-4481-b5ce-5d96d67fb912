package com.wosai.pay.common.data.heap.config;

import com.wosai.pay.common.data.heap.interfaces.RecordComparator;

/**
 * 数据堆配置类，用于设置数据堆操作的各种参数
 */
public class DataHeapConfig {
    
    /**
     * 商户ID批次分组的阈值
     * 当商户ID数量超过此值时，会被拆分为多个批次处理
     * 默认400
     */
    private int idDivideThreshold = 400;

    /**
     * 每批次处理的数据量
     */
    private int batchSize = 20;
    
    /**
     * 超时时间（毫秒）
     * 数据查询超时设置，默认60秒
     */
    private int timeoutMillis = 60000;
    
    /**
     * 是否抛出边界崩溃异常
     * 如果单个时间点的记录数超过批次上限，开启时抛出异常
     * 关闭时返回警告日志但继续执行
     */
    private boolean throwOnBoundaryCollapse = true;
    
    /**
     * 重试次数
     * 当数据加载失败时的最大重试次数
     */
    private int retryCount = 3;
    
    /**
     * 重试间隔（毫秒）
     * 重试之间的等待时间
     */
    private long retryIntervalMillis = 1000;
    
    /**
     * 记录比较器，用于自定义数据记录的排序规则
     * 默认为null，表示使用时间戳倒序排序
     */
    private RecordComparator<?> recordComparator = null;
    
    /**
     * 默认构造函数
     */
    public DataHeapConfig() {
    }
    
    /**
     * 全参构造函数
     */
    public DataHeapConfig(int idDivideThreshold, int batchSize, int timeoutMillis, 
                         boolean throwOnBoundaryCollapse, int retryCount, 
                         long retryIntervalMillis, RecordComparator<?> recordComparator) {
        this.idDivideThreshold = idDivideThreshold;
        this.batchSize = batchSize;
        this.timeoutMillis = timeoutMillis;
        this.throwOnBoundaryCollapse = throwOnBoundaryCollapse;
        this.retryCount = retryCount;
        this.retryIntervalMillis = retryIntervalMillis;
        this.recordComparator = recordComparator;
    }
    
    // Getter 方法
    public int getIdDivideThreshold() {
        return idDivideThreshold;
    }
    
    public int getBatchSize() {
        return batchSize;
    }
    
    public int getTimeoutMillis() {
        return timeoutMillis;
    }
    
    public boolean isThrowOnBoundaryCollapse() {
        return throwOnBoundaryCollapse;
    }
    
    public int getRetryCount() {
        return retryCount;
    }
    
    public long getRetryIntervalMillis() {
        return retryIntervalMillis;
    }
    
    public RecordComparator<?> getRecordComparator() {
        return recordComparator;
    }
    
    // Setter 方法
    public void setIdDivideThreshold(int idDivideThreshold) {
        this.idDivideThreshold = idDivideThreshold;
    }
    
    public void setBatchSize(int batchSize) {
        this.batchSize = batchSize;
    }
    
    public void setTimeoutMillis(int timeoutMillis) {
        this.timeoutMillis = timeoutMillis;
    }
    
    public void setThrowOnBoundaryCollapse(boolean throwOnBoundaryCollapse) {
        this.throwOnBoundaryCollapse = throwOnBoundaryCollapse;
    }
    
    public void setRetryCount(int retryCount) {
        this.retryCount = retryCount;
    }
    
    public void setRetryIntervalMillis(long retryIntervalMillis) {
        this.retryIntervalMillis = retryIntervalMillis;
    }
    
    public void setRecordComparator(RecordComparator<?> recordComparator) {
        this.recordComparator = recordComparator;
    }

    /**
     * 获取默认配置
     */
    public static DataHeapConfig defaultConfig() {
        return new DataHeapConfig();
    }
}
