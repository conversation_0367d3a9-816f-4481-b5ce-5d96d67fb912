package com.wosai.pay.common.data.heap.interfaces;

import javaslang.Tuple2;

import java.util.List;

/**
 * 数据加载器接口，用于从数据源加载指定ID集合的数据
 * 
 * @param <T> 数据记录类型
 * @param <P> 查询参数类型
 */
public interface DataLoader<T, P> {
    
    /**
     * 加载指定ID列表的数据
     *
     * @param ids 需要加载的ID列表
     * @param queryParams 查询参数
     * @param newPage 计算后的新的页码（针对跳页会重新计算页码）
     * @param edgeTime    包含上一次结束时间（游标）
     * @return 加载的数据列表
     */
    Tuple2<Long, List<T>> loadData(List<String> ids, P queryParams, int newPage, Long edgeTime);
}
