package com.wosai.pay.common.data.heap.processor;

import com.wosai.pay.common.data.heap.exception.DataHeapException;
import com.wosai.pay.common.data.heap.interfaces.RecordComparator;
import com.wosai.pay.common.data.heap.interfaces.EdgeRecordTimeAndIdExtractor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Iterator;
import java.util.List;

/**
 * 边界记录处理器，用于处理时间边界上的记录去重和添加
 * 在按时间分页查询时，边界时间点可能有多条记录，需要特殊处理
 * 提供开箱即用的边界记录处理功能
 *
 * @param <T> 数据记录类型
 */
public class EdgeRecordProcessor<T> {
    private static final Logger logger = LoggerFactory.getLogger(EdgeRecordProcessor.class);
    
    private final EdgeRecordTimeAndIdExtractor<T> recordTimeExtractor;
    private final boolean throwOnBoundaryCollapse;
    private RecordComparator<T> recordComparator;
    
    /**
     * 构造函数
     *
     * @param recordTimeExtractor 记录时间提取器
     * @param throwOnBoundaryCollapse 是否在边界崩溃时抛出异常
     */
    public EdgeRecordProcessor(EdgeRecordTimeAndIdExtractor<T> recordTimeExtractor, boolean throwOnBoundaryCollapse) {
        this.recordTimeExtractor = recordTimeExtractor;
        this.throwOnBoundaryCollapse = throwOnBoundaryCollapse;
    }
    
    /**
     * 构造函数
     *
     * @param recordTimeExtractor 记录时间提取器
     * @param throwOnBoundaryCollapse 是否在边界崩溃时抛出异常
     * @param recordComparator 记录比较器
     */
    public EdgeRecordProcessor(EdgeRecordTimeAndIdExtractor<T> recordTimeExtractor, boolean throwOnBoundaryCollapse, RecordComparator<T> recordComparator) {
        this.recordTimeExtractor = recordTimeExtractor;
        this.throwOnBoundaryCollapse = throwOnBoundaryCollapse;
        this.recordComparator = recordComparator;
    }

    /**
     * 移除重复的边界记录
     * 当边界时间上的记录已经在上次查询中处理过时，需要将其从当前批次中移除
     *
     * @param records 当前批次的数据记录
     * @param edgeRecordIds 上次查询时记录的边界记录ID列表
     * @param boundaryTime 边界时间点
     */
    public void removeDuplicatesRecord(List<T> records, List<String> edgeRecordIds, Long boundaryTime) {
        if (records == null || records.isEmpty() || edgeRecordIds == null || boundaryTime == null || edgeRecordIds.isEmpty()) {
            return;
        }
        
        Iterator<T> iterator = records.iterator();
        while (iterator.hasNext()) {
            T record = iterator.next();
            if (recordTimeExtractor.getRecordTime(record) == boundaryTime) {
                String recordId = recordTimeExtractor.getRecordId(record);
                if (edgeRecordIds.contains(recordId)) {
                    iterator.remove();
                }
            }
        }
    }

    /**
     * 检查边界崩溃情况
     * 当单个时间点的记录数超过批次大小时，会导致分页查询失效，需要特殊处理
     *
     * @param records 数据记录列表
     * @param batchSize 批次大小
     * @throws RuntimeException 当检测到边界崩溃时抛出异常
     */
    public void checkBoundaryCollapse(List<T> records, int batchSize) {
        if (records == null || records.isEmpty() || records.size() < batchSize) {
            return;
        }
        
        T firstRecord = records.get(0);
        T lastRecord = records.get(records.size() - 1);
        
        long firstTime = recordTimeExtractor.getRecordTime(firstRecord);
        long lastTime = recordTimeExtractor.getRecordTime(lastRecord);
        
        // 检测边界崩溃
        boolean hasBoundaryCollapse = (firstTime == lastTime);

        if (hasBoundaryCollapse) {
            String message = String.format(
                    "(单毫秒内) 数据记录超过批次上限，批次大小: %d, 时间戳: %d, 排序规则: %s", 
                    records.size(), firstTime, 
                    recordComparator != null ? 
                        (recordComparator.isTimeAscending() ? "正序" : "倒序") : "默认倒序");
            
            logger.error(message);
            
            if (throwOnBoundaryCollapse) {
                throw new DataHeapException.BoundaryCollapseException(message);
            }
        }
    }
}
