package com.wosai.pay.common.data.heap.example;

import com.wosai.pay.common.data.heap.config.DataHeapConfig;
import com.wosai.pay.common.data.heap.core.DataHeapBuilder;
import javaslang.Tuple2;

import java.util.*;

/**
 * 简化的交易查询示例
 * 展示如何使用Builder模式简化MultiEntityDataHeap的使用
 */
public class SimplifiedTransactionQueryExample {
    
    public static void main(String[] args) {
        // 示例1：使用基础Builder模式
        basicBuilderExample();

    }
    
    /**
     * 使用基础Builder模式
     */
    private static void basicBuilderExample() {
        System.out.println("=== 基础Builder模式示例 ===");
        
        // 准备数据
//        TransactionService transactionService = new TransactionService();
        List<String> merchantIds = generateMerchantIds(50);
        Map<String, Object> queryParams = createQueryParams();

        try {
            // 使用Builder模式创建MultiEntityDataHeap - 只需要几行代码！
            DataHeapConfig config = new DataHeapConfig();
            config.setBatchSize(10);

            DataHeapBuilder<Map, Map<String, Object>> builder = DataHeapBuilder.create();
            builder.recordClass(Map.class)
                    .entityIds(merchantIds)
                    .queryParams(queryParams)
                    .dataLoader((List<String> ids, Map<String, Object> params, int page, Long edgeTime) -> {
                        // 简化的数据加载逻辑
                        return new Tuple2<>(0L, Collections.emptyList());
                    })
                    .timeField("createTime")
                    .idField("transactionId")
                    .config(config)  // 使用默认配置
                    .build();


        } catch (Exception e) {
            System.err.println("基础Builder示例执行失败: " + e.getMessage());
        }
    }
    
    // ========== 辅助方法 ==========
    
    private static List<String> generateMerchantIds(int count) {
        List<String> merchantIds = new ArrayList<>();
        for (int i = 1; i <= count; i++) {
            merchantIds.add("MERCHANT_" + String.format("%03d", i));
        }
        return merchantIds;
    }
    
    private static Map<String, Object> createQueryParams() {
        Map<String, Object> params = new HashMap<>();
        params.put("status", "SUCCESS");
        params.put("startTime", System.currentTimeMillis() - 7 * 24 * 60 * 60 * 1000L); // 7天前
        params.put("endTime", System.currentTimeMillis());
        return params;
    }
}