package com.wosai.pay.common.data.heap.core;

import com.wosai.pay.common.data.heap.config.DataHeapConfig;
import com.wosai.pay.common.data.heap.interfaces.DataLoader;
import com.wosai.pay.common.data.heap.interfaces.RecordComparator;
import com.wosai.pay.common.data.heap.interfaces.EdgeRecordTimeAndIdExtractor;
import com.wosai.pay.common.data.heap.processor.EdgeRecordProcessor;
import com.wosai.pay.common.data.heap.utils.BatchUtil;
import javaslang.Tuple2;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 多实体数据堆
 * 支持从多个实体ID批量加载和处理数据
 *
 * @param <T> 数据记录类型
 * @param <P> 查询参数类型
 */
public class MultiEntityDataHeap<T, P> {

    /**
     * 批次状态映射表
     */
    private final Map<List<String>, DataBatchCondition<T, P>> batchConditionMap = new ConcurrentHashMap<>();

    /**
     * 数据总大小
     */
    private long totalCount;

    /**
     * 数据优先队列，用于按时间排序处理记录
     */
    private final PriorityQueue<T> dataQueue;

    /**
     * 记录时间提取器
     */
    private final EdgeRecordTimeAndIdExtractor<T> timeExtractor;

    /**
     * 边界记录处理器
     */
    private EdgeRecordProcessor<T> edgeRecordProcessor;

    /**
     * 数据加载器
     */
    private final DataLoader<T, P> dataLoader;

    /**
     * 堆配置
     */
    private final DataHeapConfig config;
    
    /**
     * 默认批次大小
     */
    private final int defaultBatchSize;
    
    /**
     * 查询参数
     */
    private P queryParams;

    /**
     * 当前请求页码
     */
    private int currentPage;

    /**
     * 最后请求的页码
     */
    private int lastRequestPage = 0;

    private boolean needReverse = false;

    private boolean needCalOffset = false;

    private boolean isAppend = false;


    /**
     * 构造函数
     *
     * @param ids 实体ID列表
     * @param queryParams 查询参数
     * @param lastRecordTime 初始边界时间 正序即开始时间，倒序即结束时间
     * @param dataLoader 数据加载器
     * @param timeExtractor 记录时间提取器
     * @param config 配置参数
     */
    public MultiEntityDataHeap(
            List<String> ids, 
            P queryParams,
            int currentPage,
            int lastRecordPage,
            Long lastRecordTime,
            DataLoader<T, P> dataLoader,
            EdgeRecordTimeAndIdExtractor<T> timeExtractor,
            DataHeapConfig config
    ) {
        this(
            ids, 
            queryParams,
            currentPage,
            lastRecordPage,
            lastRecordTime,
            dataLoader,
            false,
            timeExtractor, 
            new EdgeRecordProcessor<>(timeExtractor, config.isThrowOnBoundaryCollapse()),
            config
        );
    }

    /**
     * 构造函数
     *
     * @param ids 实体ID列表
     * @param queryParams 查询参数
     * @param lastRecordTime 初始边界时间
     * @param dataLoader 数据加载器
     * @param isAppend 是否追加数据
     * @param timeExtractor 记录时间提取器
     * @param edgeRecordProcessor 边界记录处理器
     * @param config 配置参数
     */
    public MultiEntityDataHeap(
            List<String> ids,
            P queryParams,
            int currentPage,
            int lastRecordPage,
            Long lastRecordTime,
            DataLoader<T, P> dataLoader,
            Boolean isAppend,
            EdgeRecordTimeAndIdExtractor<T> timeExtractor,
            EdgeRecordProcessor<T> edgeRecordProcessor,
            DataHeapConfig config
    ) {
        this.dataLoader = dataLoader;
        this.timeExtractor = timeExtractor;
        this.edgeRecordProcessor = edgeRecordProcessor;
        this.config = config;
        this.defaultBatchSize = config.getBatchSize();
        this.queryParams = queryParams;
        this.currentPage = currentPage;
        this.isAppend = isAppend;
        
        // 使用配置中的比较器或默认的倒序比较器
        @SuppressWarnings("unchecked")
        RecordComparator<T> comparator = (RecordComparator<T>) config.getRecordComparator();
        if (comparator == null) {
            comparator = RecordComparator.defaultTimeDesc(timeExtractor);
        }

        // 第一页
        if (currentPage == 1 && lastRecordPage == 0) {
            // 不做特殊处理
        } else if (Math.abs(currentPage - lastRecordPage) == 1) {
            // 连续翻页（相邻页面）
            // 根据翻页方向使用不同的边界条件
            if (currentPage < lastRecordPage) {
                //向前翻页：需要重新设计查询逻辑, 使用上一页的最小时间作为边界, 且排序规则需逆转
                comparator = RecordComparator.reverseComparator(comparator);
                lastRecordTime = lastRecordTime + 1;
                needReverse = true;
            } else {
                // 向后翻页：沿用现有逻辑
                lastRecordTime = lastRecordTime - 1;
            }
            currentPage = 1; //只查询第一页的数据
        } else {
            // 随机跳页
            // lastRecordTime 不起作用，使用原来的开始和结束时间做查询范围
            lastRecordTime = null;

            // 计算查询条件页: page / 组数 向上取整，
            // 计算实际页的偏移offset page % 组数 - 1,如果结果小于0，则取 组数 - 1。修正下标从0开始

            List<List<String>> batches = BatchUtil.partition(ids, config.getIdDivideThreshold());
            currentPage = (int) Math.ceil(1.0 * currentPage / batches.size());
            needCalOffset = true;
        }

        // 更新边界记录处理器，传入比较器
        this.edgeRecordProcessor = new EdgeRecordProcessor<>(timeExtractor, config.isThrowOnBoundaryCollapse(), comparator);
        
        // 创建优先队列，使用指定的比较器
        this.dataQueue = new PriorityQueue<>(comparator::compare);
        
        // 初始化分组和加载数据
        initBatches(ids, queryParams, currentPage, lastRecordTime, isAppend);
        
        // 预加载所有批次数据
        batchConditionMap.forEach((key, value) -> {
            Tuple2<Long, List<T>> records = value.supplementData();
            if (records != null && !records._2().isEmpty()) {
                dataQueue.addAll(records._2());
                // 预加载时计算总大小
                totalCount += records._1();
            }
        });
    }

    /**
     * 初始化批次
     *
     * @param ids 实体ID列表
     * @param queryParams 查询参数
     * @param lastRecordTime 边界时间
     * @param isAppend 是否追加数据
     */
    private void initBatches(List<String> ids, P queryParams, int newpage, Long lastRecordTime, Boolean isAppend) {
        if (ids == null || ids.isEmpty()) {
            return;
        }
        
        // 根据配置的阈值将大量ID分割成多个批次
        List<List<String>> batches = BatchUtil.partition(ids, config.getIdDivideThreshold());
        
        // 获取或创建比较器
        @SuppressWarnings("unchecked")
        RecordComparator<T> comparator = (RecordComparator<T>) config.getRecordComparator();
        if (comparator == null) {
            comparator = RecordComparator.defaultTimeDesc(timeExtractor);
        }

        for (List<String> batch : batches) {
            DataBatchCondition<T, P> condition = new DataBatchCondition<>(
                    batch,
                    queryParams,
                    newpage,
                    lastRecordTime,
                    dataLoader,
                    isAppend,
                    timeExtractor,
                    edgeRecordProcessor,
                    config,
                    comparator
            );
            batchConditionMap.put(batch, condition);
        }
    }

    /**
     * 获取单条数据记录
     *
     * @return 数据记录，如果没有更多数据则返回null
     */
    public T getRecord() {
        if (dataQueue.isEmpty()) {
            return null;
        }
        
        T record = dataQueue.poll();
        if (record == null) {
            return null;
        }
        
        String entityId = timeExtractor.getRecordId(record);
        long recordTime = timeExtractor.getRecordTime(record);
        
        // 查找该记录所属的批次
        List<String> batchKey = findBatchForEntityId(entityId);
        if (batchKey != null) {
            DataBatchCondition<T, P> condition = batchConditionMap.get(batchKey);
            
            // 如果是边界时间记录，添加到边界记录列表
            if (recordTime == condition.getNextEdgeTime()) {
                condition.addEdgeRecord(entityId);
            }
            
            // 如果该批次的计数减少到0，补充更多数据
            if (condition.getCount().decrementAndGet() == 0 && isAppend) {
                Tuple2<Long, List<T>> newRecords = condition.supplementData();
                if (newRecords != null && !newRecords._2().isEmpty()) {
                    dataQueue.addAll(newRecords._2());
                }
            }
        }
        
        return record;
    }
    
    /**
     * 查找实体ID所属的批次
     * 
     * @param entityId 实体ID
     * @return 批次键
     */
    private List<String> findBatchForEntityId(String entityId) {
        for (List<String> batch : batchConditionMap.keySet()) {
            if (batch.contains(entityId)) {
                return batch;
            }
        }
        return null;
    }

    /**
     * 获取多条数据记录
     *
     * @param batchSize 要获取的数据条数
     * @return 数据记录列表
     */
    public List<T> getRecords(int batchSize) {
        if (dataQueue.isEmpty()) {
            return Collections.emptyList();
        }
        
        int size = dataQueue.size();
        int count = Math.min(size, batchSize);
        if (needCalOffset) {
            count = size;
        }

        
        List<T> result = new ArrayList<>(count);
        for (int i = 0; i < count; i++) {
            T record = getRecord();
            if (record != null) {
                result.add(record);
            } else {
                break;
            }
        }

        if (needReverse) {
            Collections.reverse(result);
        }

        if (needCalOffset) {
            // 计算实际页的偏移offset page % 组数 - 1,如果结果小于0，则取 组数 - 1。修正下标从0开始
            int offsetPage = currentPage % batchConditionMap.size() - 1;
            if (offsetPage <= 0){
                offsetPage = batchConditionMap.size() - 1;
            }
            result = result.subList(offsetPage * batchSize, (offsetPage + 1) * batchSize);
        }

        if (!isAppend) {
            clear();
        }
        return result;
    }

    /**
     * 获取多条数据记录，使用默认批次大小
     *
     * @return 数据记录列表
     */
    public List<T> getRecords() {
        return getRecords(defaultBatchSize);
    }
    
    /**
     * 获取当前队列中的记录数量
     *
     * @return 记录数量
     */
    public int size() {
        return dataQueue.size();
    }
    
    /**
     * 检查队列是否为空
     *
     * @return 如果队列为空返回true，否则返回false
     */
    public boolean isEmpty() {
        return dataQueue.isEmpty();
    }
    
    /**
     * 清空队列
     */
    public void clear() {
        dataQueue.clear();
        batchConditionMap.clear();
    }

    /**
     * 获取总记录数
     * 
     * @return 总记录数
     */
    public Long getTotalCount() {
        return this.totalCount;
    }
}
