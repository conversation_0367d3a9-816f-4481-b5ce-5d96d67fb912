package com.wosai.pay.common.data.heap.interfaces;

/**
 * 记录时间提取器接口，用于从数据记录中提取时间戳和唯一ID
 * 
 * @param <T> 数据记录类型
 */
public interface EdgeRecordTimeAndIdExtractor<T> {
    
    /**
     * 获取记录的时间戳
     *
     * @param record 数据记录
     * @return 时间戳，通常是毫秒级时间戳
     */
    long getRecordTime(T record);
    
    /**
     * 获取记录的唯一ID
     *
     * @param record 数据记录
     * @return 唯一ID字符串
     */
    String getRecordId(T record);
}
