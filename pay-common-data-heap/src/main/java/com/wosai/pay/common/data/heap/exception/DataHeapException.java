package com.wosai.pay.common.data.heap.exception;

import com.wosai.pay.common.base.exception.BaseException;

/**
 * 数据堆异常类，用于处理数据堆操作中的各种异常
 */
public class DataHeapException extends BaseException {
    
    /**
     * 创建一个数据堆异常
     *
     * @param message 异常信息
     */
    public DataHeapException(String message) {
        super(message);
    }
    
    /**
     * 创建一个数据堆异常
     *
     * @param message 异常信息
     * @param cause 原始异常
     */
    public DataHeapException(String message, Throwable cause) {
        super(message, cause);
    }
    
    /**
     * 边界崩溃异常，当单个时间点的记录数超过批次上限时抛出
     */
    public static class BoundaryCollapseException extends DataHeapException {
        public BoundaryCollapseException(String message) {
            super(message);
        }
    }
    
    /**
     * 数据加载异常，当多次重试后仍然无法加载数据时抛出
     */
    public static class DataLoadException extends DataHeapException {
        public DataLoadException(String message, Throwable cause) {
            super(message, cause);
        }
    }
    
    /**
     * 配置异常，当配置参数无效时抛出
     */
    public static class ConfigurationException extends DataHeapException {
        public ConfigurationException(String message) {
            super(message);
        }
    }
}
