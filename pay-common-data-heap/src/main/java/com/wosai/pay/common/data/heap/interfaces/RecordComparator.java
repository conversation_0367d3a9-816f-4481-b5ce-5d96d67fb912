package com.wosai.pay.common.data.heap.interfaces;

import java.util.List;

/**
 * 记录比较器接口，用于自定义数据记录的排序规则
 * 
 * @param <T> 数据记录类型
 */
public interface RecordComparator<T> {
    
    /**
     * 比较两条记录的顺序
     * 
     * @param r1 第一条记录
     * @param r2 第二条记录
     * @return 负数表示r1排在r2前面，正数表示r1排在r2后面，0表示相等
     */
    int compare(T r1, T r2);
    
    /**
     * 获取批量记录中的边界记录，用于下一次查询的起始点
     * 
     * @param records 当前批次的数据记录
     * @return 边界记录，如果列表为空则返回null
     */
    default T getBoundaryRecord(List<T> records) {
        if (records == null || records.isEmpty()) {
            return null;
        }
        // 默认实现：返回列表最后一条记录作为边界
        // 对于倒序排序，这是时间最早的记录
        // 对于正序排序，这是时间最晚的记录
        return records.get(records.size() - 1);
    }
    
    /**
     * 判断排序方向是否是时间正序
     * 
     * @return 如果是时间正序返回true，时间倒序返回false
     */
    default boolean isTimeAscending() {
        // 默认为false，表示倒序排序
        return false;
    }
    
    /**
     * 创建默认的时间戳倒序比较器
     * 
     * @param <T> 数据记录类型
     * @param timeExtractor 时间提取器
     * @return 默认比较器
     */
    static <T> RecordComparator<T> defaultTimeDesc(EdgeRecordTimeAndIdExtractor<T> timeExtractor) {
        return (r1, r2) -> -Long.compare(timeExtractor.getRecordTime(r1), timeExtractor.getRecordTime(r2));
    }
    
    /**
     * 创建时间戳正序比较器
     * 
     * @param <T> 数据记录类型
     * @param timeExtractor 时间提取器
     * @return 正序比较器
     */
    static <T> RecordComparator<T> timeAsc(EdgeRecordTimeAndIdExtractor<T> timeExtractor) {
        return new RecordComparator<T>() {
            @Override
            public int compare(T r1, T r2) {
                return Long.compare(timeExtractor.getRecordTime(r1), timeExtractor.getRecordTime(r2));
            }
            
            @Override
            public boolean isTimeAscending() {
                return true;
            }
        };
    }

    /**
     * 创建默认的时间戳倒序比较器
     *
     * @param <T> 数据记录类型
     * @param comparator 比较器
     * @return 默认比较器
     */
    static <T> RecordComparator<T> reverseComparator(RecordComparator<T> comparator) {
        //reverse comparator
        return new RecordComparator<T>() {
            @Override
            public int compare(T r1, T r2) {
                return -comparator.compare(r1, r2);
            }

            @Override
            public boolean isTimeAscending() {
                return !comparator.isTimeAscending();
            }
        };
    }
}
