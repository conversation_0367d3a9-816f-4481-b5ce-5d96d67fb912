package com.wosai.pay.common.data.heap.core;

import com.wosai.pay.common.data.heap.config.DataHeapConfig;
import com.wosai.pay.common.data.heap.exception.DataHeapException;
import com.wosai.pay.common.data.heap.interfaces.DataLoader;
import com.wosai.pay.common.data.heap.interfaces.RecordComparator;
import com.wosai.pay.common.data.heap.interfaces.EdgeRecordTimeAndIdExtractor;
import com.wosai.pay.common.data.heap.processor.EdgeRecordProcessor;
import javaslang.Tuple2;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 数据批次条件，管理每个批次的数据加载条件及状态
 *
 * @param <T> 数据记录类型
 * @param <P> 查询参数类型
 */
public class DataBatchCondition<T, P> {
    
    private static final Logger log = LoggerFactory.getLogger(DataBatchCondition.class);

    /**
     * 要加载数据的ID列表
     */
    private List<String> ids;

    /**
     * 查询参数
     */
    private P queryParams;

    /**
     * 新的页码
     */
    private int newPage;

    /**
     * 下一次查询的边界时间
     */
    private Long nextEdgeTime;
    
    /**
     * 下一次查询的边界记录ID
     */
    private String nextEdgeRecordId;
    
    /**
     * 边界记录ID列表
     */
    private List<String> edgeRecordList = new ArrayList<>();
    
    /**
     * 该批次当前加载的记录数量
     */
    private AtomicInteger count = new AtomicInteger(0);
    
    /**
     * 数据加载器
     */
    private DataLoader<T, P> dataLoader;
    
    /**
     * 记录时间提取器
     */
    private EdgeRecordTimeAndIdExtractor<T> timeExtractor;
    
    /**
     * 边界记录处理器
     */
    private EdgeRecordProcessor<T> edgeRecordProcessor;
    
    /**
     * 配置
     */
    private DataHeapConfig config;
    
    /**
     * 记录比较器
     */
    private RecordComparator<T> recordComparator;
    
    // Getter 方法
    public List<String> getIds() {
        return ids;
    }
    
    public P getQueryParams() {
        return queryParams;
    }
    
    public int getNewPage() {
        return newPage;
    }
    
    public Long getNextEdgeTime() {
        return nextEdgeTime;
    }
    
    public String getNextEdgeRecordId() {
        return nextEdgeRecordId;
    }
    
    public List<String> getEdgeRecordList() {
        return edgeRecordList;
    }
    
    public AtomicInteger getCount() {
        return count;
    }
    
    public DataLoader<T, P> getDataLoader() {
        return dataLoader;
    }
    
    public EdgeRecordTimeAndIdExtractor<T> getTimeExtractor() {
        return timeExtractor;
    }
    
    public EdgeRecordProcessor<T> getEdgeRecordProcessor() {
        return edgeRecordProcessor;
    }
    
    public DataHeapConfig getConfig() {
        return config;
    }
    
    public RecordComparator<T> getRecordComparator() {
        return recordComparator;
    }
    
    // Setter 方法
    public void setIds(List<String> ids) {
        this.ids = ids;
    }
    
    public void setQueryParams(P queryParams) {
        this.queryParams = queryParams;
    }
    
    public void setNewPage(int newPage) {
        this.newPage = newPage;
    }
    
    public void setNextEdgeTime(Long nextEdgeTime) {
        this.nextEdgeTime = nextEdgeTime;
    }
    
    public void setNextEdgeRecordId(String nextEdgeRecordId) {
        this.nextEdgeRecordId = nextEdgeRecordId;
    }
    
    public void setEdgeRecordList(List<String> edgeRecordList) {
        this.edgeRecordList = edgeRecordList;
    }
    
    public void setCount(AtomicInteger count) {
        this.count = count;
    }
    
    public void setDataLoader(DataLoader<T, P> dataLoader) {
        this.dataLoader = dataLoader;
    }
    
    public void setTimeExtractor(EdgeRecordTimeAndIdExtractor<T> timeExtractor) {
        this.timeExtractor = timeExtractor;
    }
    
    public void setEdgeRecordProcessor(EdgeRecordProcessor<T> edgeRecordProcessor) {
        this.edgeRecordProcessor = edgeRecordProcessor;
    }
    
    public void setConfig(DataHeapConfig config) {
        this.config = config;
    }
    
    public void setRecordComparator(RecordComparator<T> recordComparator) {
        this.recordComparator = recordComparator;
    }
    
    /**
     * 构造函数
     *
     * @param ids 要加载数据的ID列表
     * @param queryParams 查询参数
     * @param newPage 计算后的页码
     * @param nextEdgeTime 初始边界时间
     * @param dataLoader 数据加载器
     * @param isAppend 是否追加数据
     * @param timeExtractor 记录时间提取器
     * @param edgeRecordProcessor 边界记录处理器
     * @param config 配置
     * @param recordComparator 记录比较器
     */
    public DataBatchCondition(
            List<String> ids,
            P queryParams,
            int newPage,
            Long nextEdgeTime,
            DataLoader<T, P> dataLoader,
            Boolean isAppend,
            EdgeRecordTimeAndIdExtractor<T> timeExtractor,
            EdgeRecordProcessor<T> edgeRecordProcessor,
            DataHeapConfig config,
            RecordComparator<T> recordComparator
    ) {
        this.ids = ids;
        this.queryParams = queryParams;
        this.newPage = newPage;
        this.nextEdgeTime = nextEdgeTime;
        this.dataLoader = dataLoader;
        this.timeExtractor = timeExtractor;
        this.edgeRecordProcessor = edgeRecordProcessor;
        this.config = config;
        this.recordComparator = recordComparator;
    }
    
    /**
     * 加载并补充批次数据
     *
     * @return 加载的数据列表，如果没有更多数据则返回空列表
     */
    public Tuple2<Long, List<T>> supplementData() {
        if (ids == null || ids.isEmpty()) {
            return new Tuple2<>(0L, Collections.emptyList());
        }
        
        // 尝试多次加载数据
        Tuple2<Long, List<T>> records = null;
        Exception lastException = null;
        
        for (int attempts = 0; attempts < config.getRetryCount(); attempts++) {
            try {
                records = dataLoader.loadData(ids, queryParams, newPage, nextEdgeTime);
                break;
            } catch (Exception e) {
                lastException = e;
                log.warn("加载数据失败，重试次数 {}/{}，ID列表: {}, 异常: {}",
                        attempts + 1, config.getRetryCount(), ids, e.getMessage());
                
                try {
                    Thread.sleep(config.getRetryIntervalMillis());
                } catch (InterruptedException ie) {
                    Thread.currentThread().interrupt();
                    throw new DataHeapException("数据加载过程中线程被中断", ie);
                }
            }
        }
        
        // 如果多次尝试后仍然失败
        if (records == null) {
            throw new DataHeapException.DataLoadException(
                    "多次尝试后仍然无法加载数据，ID列表: " + ids, lastException);
        }

        List<T> recordsList = records._2();
        if (recordsList.isEmpty()) {
            return new Tuple2<>(0L, Collections.emptyList());
        }
        
        // 检查边界崩溃情况
        edgeRecordProcessor.checkBoundaryCollapse(recordsList, config.getBatchSize());
        
        // 去重边界数据
        edgeRecordProcessor.removeDuplicatesRecord(recordsList, edgeRecordList, nextEdgeTime);
        edgeRecordList.clear();
        
        // 如果去重后没有数据了，返回空列表
        if (recordsList.isEmpty()) {
            return new Tuple2<>(0L, Collections.emptyList());
        }
        
        // 更新计数和边界记录
        count.addAndGet(recordsList.size());
        
        // 使用比较器获取边界记录，并更新下一次查询的边界时间
        T boundaryRecord = recordComparator.getBoundaryRecord(recordsList);
        nextEdgeTime = timeExtractor.getRecordTime(boundaryRecord);
        
        return records;
    }
    
    /**
     * 添加边界记录ID
     *
     * @param id 记录ID
     */
    public void addEdgeRecord(String id) {
        edgeRecordList.add(id);
    }

}
