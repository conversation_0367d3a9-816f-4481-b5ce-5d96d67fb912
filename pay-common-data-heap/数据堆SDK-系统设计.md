# 数据堆SDK-系统设计

## 1. 系统能力概述

### 1.1 核心能力边界

本SDK提供以下核心能力：
1. **多实体数据聚合**：支持从多个实体ID批量加载和处理数据
2. **智能分页处理**：支持连续翻页、随机跳页等多种分页场景
3. **数据排序与优先队列**：内置优先队列机制，支持自定义排序规则
4. **边界记录处理**：智能处理分页边界数据，避免数据重复或遗漏
5. **批次管理**：自动将大量ID分割成多个批次，提升查询性能
6. **内存优化**：支持流式数据处理，避免大量数据占用内存

### 1.2 适用场景

典型业务场景包括：
- 多商户交易数据查询（如按时间排序的交易记录）
- 多账户资金流水查询
- 多订单状态聚合查询
- 需要跨多个实体进行数据聚合的场景
- 大数据量分页查询优化

### 1.3 为何选择本SDK

| 对比维度   | 传统分页查询        | 本SDK方案          |
|--------|-----------------|------------------|
| 性能表现   | 深分页性能差，OFFSET越大越慢 | 基于游标的高效分页，性能稳定 |
| 数据一致性  | 分页过程中可能出现数据重复或遗漏 | 智能边界处理，保证数据一致性 |
| 多实体聚合  | 需要多次查询后手动合并排序 | 一次性聚合多实体数据并排序 |
| 内存使用   | 可能需要加载大量数据到内存 | 流式处理，内存使用可控 |
| 开发复杂度  | 需要处理复杂的分页逻辑 | 简化的Builder模式，开箱即用 |
| 扩展性    | 难以适应不同的排序和过滤需求 | 支持自定义比较器和数据加载器 |

关键优势：
1. **解决深分页问题**：使用游标分页替代传统OFFSET分页
2. **数据一致性保障**：智能处理分页边界，避免数据重复或遗漏
3. **高性能聚合**：优化多实体数据查询和排序性能
4. **简化开发**：提供Builder模式，降低接入复杂度

## 2. 整体架构

```mermaid
graph TD
    A[业务应用] --> B[DataHeapBuilder]
    B --> C[MultiEntityDataHeap]
    C --> D[DataBatchCondition]
    D --> E[DataLoader接口]
    E --> F[数据源]
    C --> G[PriorityQueue]
    G --> H[EdgeRecordProcessor]
    H --> I[边界记录处理]
    C --> J[BatchUtil]
    J --> K[ID分批处理]
```

## 3. 核心组件设计

### 3.1 DataHeapBuilder（构建器）

```java
public class DataHeapBuilder<T, P> {
    // 支持链式调用的构建器模式
    public static <T, P> DataHeapBuilder<T, P> create();
    public DataHeapBuilder<T, P> entityIds(List<String> ids);
    public DataHeapBuilder<T, P> queryParams(P params);
    public DataHeapBuilder<T, P> withLoader(LoadFunction<T, P> loadFunction);
    public DataHeapBuilder<T, P> timeField(String timeFieldName);
    public DataHeapBuilder<T, P> idField(String idFieldName);
    public MultiEntityDataHeap<T, P> build();
}
```

**核心功能**：
- 提供简化的API接口
- 支持反射自动提取时间和ID字段
- 支持函数式编程风格的数据加载器配置

### 3.2 MultiEntityDataHeap（多实体数据堆）

```java
public class MultiEntityDataHeap<T, P> {
    // 核心数据结构
    private final Map<List<String>, DataBatchCondition<T, P>> batchConditionMap;
    private final PriorityQueue<T> dataQueue;
    private final EdgeRecordProcessor<T> edgeRecordProcessor;
    
    // 核心方法
    public T getRecord();                    // 获取单条记录
    public List<T> getRecords(int batchSize); // 获取多条记录
    public Long getTotalCount();             // 获取总记录数
}
```

**核心功能**：
- 管理多个批次的数据加载条件
- 使用优先队列维护数据排序
- 支持流式数据获取

### 3.3 DataBatchCondition（数据批次条件）

```java
public class DataBatchCondition<T, P> {
    private List<String> entityIds;          // 实体ID列表
    private P queryParams;                   // 查询参数
    private Long nextEdgeTime;               // 下一个边界时间
    private AtomicInteger count;             // 当前批次记录数
    
    public Tuple2<Long, List<T>> supplementData(); // 补充数据
}
```

**核心功能**：
- 管理单个批次的查询条件和状态
- 支持动态数据补充
- 处理边界时间计算

## 4. 分页处理机制

### 4.1 分页场景分类

| 分页类型 | 场景描述 | 处理策略 |
|---------|---------|----------|
| 首页查询 | currentPage=1, lastRecordPage=0 | 正常查询，不使用边界时间 |
| 连续翻页 | abs(currentPage - lastRecordPage) = 1 | 使用边界时间优化查询 |
| 向前翻页 | currentPage < lastRecordPage | 反转排序规则，使用上边界 |
| 向后翻页 | currentPage > lastRecordPage | 使用下边界时间 |
| 随机跳页 | abs(currentPage - lastRecordPage) > 1 | 重新计算页码和偏移量 |

### 4.2 边界时间处理

```mermaid
graph TD
    A[获取记录] --> B{是否为边界时间?}
    B -->|是| C[添加到边界记录列表]
    B -->|否| D[正常处理]
    C --> E[更新下一个边界时间]
    D --> F[返回记录]
    E --> F
```

## 5. 数据加载与排序

### 5.1 数据加载接口

```java
public interface DataLoader<T, P> {
    /**
     * 加载指定ID列表的数据
     * @param ids 需要加载的ID列表
     * @param queryParams 查询参数
     * @param newPage 计算后的新页码
     * @param edgeTime 边界时间（游标）
     * @return Tuple2<总记录数, 数据列表>
     */
    Tuple2<Long, List<T>> loadData(List<String> ids, P queryParams, int newPage, Long edgeTime);
}
```

### 5.2 排序机制

```java
public interface RecordComparator<T> {
    int compare(T o1, T o2);
    
    // 默认时间倒序比较器
    static <T> RecordComparator<T> defaultTimeDesc(EdgeRecordTimeAndIdExtractor<T> extractor);
    
    // 反转比较器
    static <T> RecordComparator<T> reverseComparator(RecordComparator<T> comparator);
}
```

## 6. 配置参数

### 6.1 DataHeapConfig配置类

```java
public class DataHeapConfig {
    /**
     * 商户ID批次分组的阈值，默认400
     */
    private int idDivideThreshold = 400;
    
    /**
     * 每次查询的批次大小，默认20
     */
    private int batchSize = 20;
    
    /**
     * 记录比较器
     */
    private RecordComparator<?> recordComparator;
    
    /**
     * 边界折叠时是否抛出异常，默认false
     */
    private boolean throwOnBoundaryCollapse = false;
    
    // 构造函数
    public DataHeapConfig() {}
    
    // Getter 和 Setter 方法
    public int getIdDivideThreshold() { return idDivideThreshold; }
    public void setIdDivideThreshold(int idDivideThreshold) { this.idDivideThreshold = idDivideThreshold; }
    
    public int getBatchSize() { return batchSize; }
    public void setBatchSize(int batchSize) { this.batchSize = batchSize; }
    
    public RecordComparator<?> getRecordComparator() { return recordComparator; }
    public void setRecordComparator(RecordComparator<?> recordComparator) { this.recordComparator = recordComparator; }
    
    public boolean isThrowOnBoundaryCollapse() { return throwOnBoundaryCollapse; }
    public void setThrowOnBoundaryCollapse(boolean throwOnBoundaryCollapse) { this.throwOnBoundaryCollapse = throwOnBoundaryCollapse; }
    
    /**
     * Builder 类
     */
    public static class Builder {
        private DataHeapConfig config = new DataHeapConfig();
        
        public Builder idDivideThreshold(int idDivideThreshold) {
            config.setIdDivideThreshold(idDivideThreshold);
            return this;
        }
        
        public Builder batchSize(int batchSize) {
            config.setBatchSize(batchSize);
            return this;
        }
        
        public Builder recordComparator(RecordComparator<?> recordComparator) {
            config.setRecordComparator(recordComparator);
            return this;
        }
        
        public Builder throwOnBoundaryCollapse(boolean throwOnBoundaryCollapse) {
            config.setThrowOnBoundaryCollapse(throwOnBoundaryCollapse);
            return this;
        }
        
        public DataHeapConfig build() {
            return config;
        }
    }
    
    /**
     * 创建 Builder 实例
     */
    public static Builder builder() {
        return new Builder();
    }
    
    /**
     * 获取默认配置
     */
    public static DataHeapConfig defaultConfig() {
        return new DataHeapConfig();
    }
}
```

### 6.2 配置参数说明

| 参数名 | 默认值 | 说明 |
|-------|-------|------|
| idDivideThreshold | 400 | ID分批阈值，超过此值会分成多个批次处理 |
| batchSize | 20 | 每次返回的记录数量 |
| recordComparator | 时间倒序 | 记录排序比较器 |
| throwOnBoundaryCollapse | false | 边界折叠时是否抛异常 |

## 7. 异常处理

### 7.1 异常类型

```java
public class DataHeapException extends RuntimeException {
    // 数据加载异常
    public static class DataLoadException extends DataHeapException {
        public DataLoadException(String message, Throwable cause);
    }
    
    // 配置异常
    public static class ConfigurationException extends DataHeapException {
        public ConfigurationException(String message);
    }
    
    // 边界处理异常
    public static class BoundaryException extends DataHeapException {
        public BoundaryException(String message);
    }
}
```

### 7.2 异常处理策略

| 异常类型 | 处理策略 | 说明 |
|---------|---------|------|
| DataLoadException | 向上抛出 | 数据加载失败，需要业务方处理 |
| ConfigurationException | 启动时抛出 | 配置错误，阻止系统启动 |
| BoundaryException | 可配置 | 边界处理异常，可选择抛出或忽略 |

## 8. 性能优化

### 8.1 批次处理优化

- **ID分批**：将大量ID分成小批次，避免单次查询过大
- **并发加载**：支持多个批次并发加载数据
- **内存控制**：使用优先队列控制内存使用

### 8.2 查询优化

- **游标分页**：使用时间戳作为游标，避免深分页问题
- **边界优化**：智能处理分页边界，减少重复查询
- **索引友好**：查询条件设计考虑数据库索引优化

## 9. 扩展点设计

### 9.1 自定义数据加载器

```java
// 支持函数式接口
DataHeapBuilder.create()
    .withLoader((ids, params, page, edgeTime) -> {
        // 自定义数据加载逻辑
        return new Tuple2<>(totalCount, dataList);
    });
```

### 9.2 自定义排序规则

```java
// 支持自定义比较器
DataHeapConfig config = DataHeapConfig.builder()
    .recordComparator((r1, r2) -> {
        // 自定义排序逻辑
        return Long.compare(r1.getAmount(), r2.getAmount());
    })
    .build();
```

### 9.3 自定义字段提取器

```java
// 支持自定义时间和ID提取
DataHeapBuilder.create()
    .timeExtractor(record -> record.getCreateTime())
    .idExtractor(record -> record.getId());
```

## 10. 最佳实践

### 10.1 性能最佳实践

1. **合理设置批次大小**：根据数据量和内存情况调整batchSize
2. **优化数据库查询**：确保查询字段有合适的索引
3. **控制ID数量**：单次查询的ID数量不宜过多
4. **使用连续分页**：尽量避免随机跳页，使用连续翻页获得最佳性能

### 10.2 数据一致性最佳实践

1. **正确设置边界时间**：确保边界时间字段有足够的精度
2. **处理并发修改**：考虑数据在查询过程中被修改的情况
3. **幂等性设计**：确保重复查询不会产生副作用

### 10.3 错误处理最佳实践

1. **优雅降级**：数据加载失败时提供降级方案
2. **日志记录**：记录关键操作和异常信息
3. **监控告警**：监控查询性能和错误率

## 11. 版本兼容性

### 11.1 依赖要求

- **Java版本**：JDK 8+
- **核心依赖**：
  - javaslang 2.0.6（函数式编程支持）
  - slf4j-api 1.7.30（日志接口）

### 11.2 向后兼容性

- 保持核心API的向后兼容性
- 新增功能通过可选参数或新方法提供
- 废弃功能会提供迁移指南