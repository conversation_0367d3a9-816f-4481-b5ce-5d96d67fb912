# 数据堆SDK-接入指南

## 1. 添加SDK依赖

```xml
<dependencies>
    <!-- 数据堆SDK -->
    <dependency>
        <groupId>com.wosai.pay</groupId>
        <artifactId>pay-common-data-heap</artifactId>
        <version>1.2.1</version>
    </dependency>
</dependencies>
```

如果项目中没有引入相关依赖，则需要添加以下依赖（版本可根据项目调整）：

```xml
<dependencies>
    <!-- 函数式编程支持 -->
    <dependency>
        <groupId>io.javaslang</groupId>
        <artifactId>javaslang</artifactId>
        <version>2.0.6</version>
    </dependency>
    
    <!-- 日志接口 -->
    <dependency>
        <groupId>org.slf4j</groupId>
        <artifactId>slf4j-api</artifactId>
        <version>1.7.30</version>
    </dependency>
    

</dependencies>
```

## 2. 基础使用示例

### 2.1 定义数据实体

```java
public class Transaction {
    private String transactionId;
    private String merchantId;
    private Long amount;
    private Long createTime;
    private String status;
    
    // 构造函数
    public Transaction() {}
    
    public Transaction(String transactionId, String merchantId, Long amount, Long createTime, String status) {
        this.transactionId = transactionId;
        this.merchantId = merchantId;
        this.amount = amount;
        this.createTime = createTime;
        this.status = status;
    }
    
    // Getter 和 Setter 方法
    public String getTransactionId() {
        return transactionId;
    }
    
    public void setTransactionId(String transactionId) {
        this.transactionId = transactionId;
    }
    
    public String getMerchantId() {
        return merchantId;
    }
    
    public void setMerchantId(String merchantId) {
        this.merchantId = merchantId;
    }
    
    public Long getAmount() {
        return amount;
    }
    
    public void setAmount(Long amount) {
        this.amount = amount;
    }
    
    public Long getCreateTime() {
        return createTime;
    }
    
    public void setCreateTime(Long createTime) {
        this.createTime = createTime;
    }
    
    public String getStatus() {
        return status;
    }
    
    public void setStatus(String status) {
        this.status = status;
    }
    
    // 其他业务字段...
}
```

### 2.2 实现数据加载器

```java
@Service
public class TransactionService {
    
    @Autowired
    private TransactionMapper transactionMapper;
    
    /**
     * 查询交易数据
     * @param merchantIds 商户ID列表
     * @param queryParams 查询参数
     * @param page 页码
     * @param edgeTime 边界时间（游标）
     * @return Tuple2<总记录数, 数据列表>
     */
    public Tuple2<Long, List<Transaction>> queryTransactions(
            List<String> merchantIds, 
            Map<String, Object> queryParams, 
            int page, 
            Long edgeTime) {
        
        // 构建查询条件
        TransactionQuery query = new TransactionQuery();
        query.setMerchantIds(merchantIds);
        query.setStatus((String) queryParams.get("status"));
        query.setStartTime((Long) queryParams.get("startTime"));
        query.setEndTime((Long) queryParams.get("endTime"));
        query.setPage(page);
        query.setPageSize(20);
        
        // 如果有边界时间，使用游标分页
        if (edgeTime != null) {
            query.setEdgeTime(edgeTime);
        }
        
        // 执行查询
        List<Transaction> transactions = transactionMapper.selectByQuery(query);
        Long totalCount = transactionMapper.countByQuery(query);
        
        return new Tuple2<>(totalCount, transactions);
    }
}
```

### 2.3 使用Builder模式创建数据堆

```java
@Service
public class TransactionQueryService {
    
    @Autowired
    private TransactionService transactionService;
    
    /**
     * 查询多商户交易数据
     */
    public List<Transaction> queryMultiMerchantTransactions(
            List<String> merchantIds,
            Map<String, Object> queryParams,
            int currentPage,
            int lastRecordPage,
            Long lastRecordTime) {
        
        try {
            // 使用Builder模式创建MultiEntityDataHeap
            MultiEntityDataHeap<Transaction, Map<String, Object>> dataHeap = 
                DataHeapBuilder.<Transaction, Map<String, Object>>create()
                    .entityIds(merchantIds)
                    .queryParams(queryParams)
                    .currentPage(currentPage)
                    .lastRecordPage(lastRecordPage)
                    .lastRecordTime(lastRecordTime)
                    .withLoader((ids, params, page, edgeTime) -> {
                        return transactionService.queryTransactions(ids, params, page, edgeTime);
                    })
                    .timeField("createTime")  // 指定时间字段
                    .idField("transactionId") // 指定ID字段
                    .config(DataHeapConfig.defaultConfig()) // 使用默认配置
                    .build();
            
            // 获取数据
            List<Transaction> result = dataHeap.getRecords(20); // 获取20条记录
            
            return result;
            
        } catch (Exception e) {
            log.error("查询多商户交易数据失败", e);
            throw new BusinessException("查询失败: " + e.getMessage());
        }
    }
}
```

## 3. 高级配置

### 3.1 自定义配置

```java
// 创建自定义配置
DataHeapConfig config = DataHeapConfig.builder()
    .idDivideThreshold(500)  // ID分批阈值
    .batchSize(50)           // 每次返回记录数
    .throwOnBoundaryCollapse(true)  // 边界折叠时抛异常
    .recordComparator(RecordComparator.defaultTimeDesc(timeExtractor)) // 自定义排序
    .build();

// 使用自定义配置
MultiEntityDataHeap<Transaction, Map<String, Object>> dataHeap = 
    DataHeapBuilder.<Transaction, Map<String, Object>>create()
        .entityIds(merchantIds)
        .queryParams(queryParams)
        .withLoader(transactionService::queryTransactions)
        .timeField("createTime")
        .idField("transactionId")
        .config(config)  // 使用自定义配置
        .build();
```

### 3.2 自定义排序规则

```java
// 按金额倒序排序
RecordComparator<Transaction> amountComparator = (t1, t2) -> 
    Long.compare(t2.getAmount(), t1.getAmount());

DataHeapConfig config = DataHeapConfig.builder()
    .recordComparator(amountComparator)
    .build();
```

### 3.3 自定义时间和ID提取器

```java
// 自定义时间提取器
EdgeRecordTimeAndIdExtractor<Transaction> customExtractor = 
    new EdgeRecordTimeAndIdExtractor<Transaction>() {
        @Override
        public long getRecordTime(Transaction record) {
            return record.getCreateTime();
        }
        
        @Override
        public String getRecordId(Transaction record) {
            return record.getTransactionId();
        }
    };

// 使用自定义提取器
MultiEntityDataHeap<Transaction, Map<String, Object>> dataHeap = 
    DataHeapBuilder.<Transaction, Map<String, Object>>create()
        .entityIds(merchantIds)
        .queryParams(queryParams)
        .withLoader(transactionService::queryTransactions)
        .timeExtractor(customExtractor)  // 使用自定义提取器
        .build();
```

## 4. 数据获取方式

### 4.1 批量获取

```java
// 获取指定数量的记录
List<Transaction> records = dataHeap.getRecords(20);

// 获取默认数量的记录
List<Transaction> records = dataHeap.getRecords();
```

### 4.2 流式获取

```java
// 逐条获取记录
while (!dataHeap.isEmpty()) {
    Transaction record = dataHeap.getRecord();
    if (record != null) {
        // 处理单条记录
        processTransaction(record);
    }
}
```

### 4.3 获取统计信息

```java
// 获取总记录数
Long totalCount = dataHeap.getTotalCount();

// 获取当前队列大小
int queueSize = dataHeap.size();

// 检查是否为空
boolean isEmpty = dataHeap.isEmpty();
```

## 5. 分页场景处理

### 5.1 首页查询

```java
// 首页查询（currentPage=1, lastRecordPage=0）
MultiEntityDataHeap<Transaction, Map<String, Object>> dataHeap = 
    DataHeapBuilder.<Transaction, Map<String, Object>>create()
        .entityIds(merchantIds)
        .queryParams(queryParams)
        .currentPage(1)      // 当前页
        .lastRecordPage(0)   // 上次记录页（首页为0）
        .lastRecordTime(null) // 首页不需要边界时间
        .withLoader(transactionService::queryTransactions)
        .timeField("createTime")
        .idField("transactionId")
        .build();
```

### 5.2 连续翻页

```java
// 向后翻页（第2页 -> 第3页）
MultiEntityDataHeap<Transaction, Map<String, Object>> dataHeap = 
    DataHeapBuilder.<Transaction, Map<String, Object>>create()
        .entityIds(merchantIds)
        .queryParams(queryParams)
        .currentPage(3)           // 当前页
        .lastRecordPage(2)        // 上次记录页
        .lastRecordTime(lastTime) // 上页最后一条记录的时间
        .withLoader(transactionService::queryTransactions)
        .timeField("createTime")
        .idField("transactionId")
        .build();
```

### 5.3 随机跳页

```java
// 随机跳页（第2页 -> 第10页）
MultiEntityDataHeap<Transaction, Map<String, Object>> dataHeap = 
    DataHeapBuilder.<Transaction, Map<String, Object>>create()
        .entityIds(merchantIds)
        .queryParams(queryParams)
        .currentPage(10)     // 目标页
        .lastRecordPage(2)   // 上次记录页
        .lastRecordTime(null) // 随机跳页不使用边界时间
        .withLoader(transactionService::queryTransactions)
        .timeField("createTime")
        .idField("transactionId")
        .build();
```

## 6. 错误处理

### 6.1 异常捕获

```java
try {
    MultiEntityDataHeap<Transaction, Map<String, Object>> dataHeap = 
        DataHeapBuilder.<Transaction, Map<String, Object>>create()
            .entityIds(merchantIds)
            .queryParams(queryParams)
            .withLoader(transactionService::queryTransactions)
            .timeField("createTime")
            .idField("transactionId")
            .build();
    
    List<Transaction> records = dataHeap.getRecords();
    
} catch (DataHeapException.DataLoadException e) {
    // 数据加载异常
    log.error("数据加载失败", e);
    throw new BusinessException("查询失败，请稍后重试");
    
} catch (DataHeapException.ConfigurationException e) {
    // 配置异常
    log.error("配置错误", e);
    throw new BusinessException("系统配置错误");
    
} catch (DataHeapException.BoundaryException e) {
    // 边界处理异常
    log.warn("边界处理异常", e);
    // 可以选择忽略或降级处理
    
} catch (Exception e) {
    // 其他异常
    log.error("未知异常", e);
    throw new BusinessException("系统异常");
}
```

### 6.2 数据验证

```java
// 验证必要参数
if (merchantIds == null || merchantIds.isEmpty()) {
    throw new IllegalArgumentException("商户ID列表不能为空");
}

if (queryParams == null) {
    throw new IllegalArgumentException("查询参数不能为空");
}

// 验证分页参数
if (currentPage < 1) {
    throw new IllegalArgumentException("当前页码必须大于0");
}
```

## 7. 性能优化建议

### 7.1 数据库优化

```sql
-- 确保查询字段有合适的索引
CREATE INDEX idx_merchant_time ON transaction(merchant_id, create_time DESC);
CREATE INDEX idx_status_time ON transaction(status, create_time DESC);

-- 复合索引优化范围查询
CREATE INDEX idx_merchant_status_time ON transaction(merchant_id, status, create_time DESC);
```

### 7.2 参数调优

```java
// 根据数据量调整批次大小
DataHeapConfig config = DataHeapConfig.builder()
    .idDivideThreshold(200)  // 商户数量较少时减小阈值
    .batchSize(30)           // 根据页面显示需求调整
    .build();

// 大数据量场景
DataHeapConfig bigDataConfig = DataHeapConfig.builder()
    .idDivideThreshold(1000) // 增大批次处理能力
    .batchSize(100)          // 增大单次返回数量
    .build();
```

### 7.3 缓存策略

```java
@Service
public class CachedTransactionService {
    
    @Autowired
    private RedisTemplate<String, Object> redisTemplate;
    
    public Tuple2<Long, List<Transaction>> queryTransactionsWithCache(
            List<String> merchantIds, 
            Map<String, Object> queryParams, 
            int page, 
            Long edgeTime) {
        
        // 构建缓存键
        String cacheKey = buildCacheKey(merchantIds, queryParams, page, edgeTime);
        
        // 尝试从缓存获取
        Object cached = redisTemplate.opsForValue().get(cacheKey);
        if (cached != null) {
            return (Tuple2<Long, List<Transaction>>) cached;
        }
        
        // 缓存未命中，查询数据库
        Tuple2<Long, List<Transaction>> result = queryTransactions(merchantIds, queryParams, page, edgeTime);
        
        // 写入缓存（设置合适的过期时间）
        redisTemplate.opsForValue().set(cacheKey, result, Duration.ofMinutes(5));
        
        return result;
    }
}
```

## 8. 最佳实践

### 8.1 接口设计

```java
@RestController
@RequestMapping("/api/transactions")
public class TransactionController {
    
    @Autowired
    private TransactionQueryService transactionQueryService;
    
    @PostMapping("/query")
    public ResponseEntity<PageResult<Transaction>> queryTransactions(
            @RequestBody TransactionQueryRequest request) {
        
        try {
            List<Transaction> transactions = transactionQueryService.queryMultiMerchantTransactions(
                request.getMerchantIds(),
                request.getQueryParams(),
                request.getCurrentPage(),
                request.getLastRecordPage(),
                request.getLastRecordTime()
            );
            
            PageResult<Transaction> result = new PageResult<>();
            result.setData(transactions);
            result.setCurrentPage(request.getCurrentPage());
            result.setPageSize(transactions.size());
            
            return ResponseEntity.ok(result);
            
        } catch (Exception e) {
            log.error("查询交易数据失败", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(PageResult.error("查询失败"));
        }
    }
}
```

### 8.2 配置管理

```java
@Configuration
public class DataHeapConfiguration {
    
    @Value("${data.heap.id.divide.threshold:400}")
    private int idDivideThreshold;
    
    @Value("${data.heap.batch.size:20}")
    private int batchSize;
    
    @Bean
    public DataHeapConfig dataHeapConfig() {
        return DataHeapConfig.builder()
            .idDivideThreshold(idDivideThreshold)
            .batchSize(batchSize)
            .throwOnBoundaryCollapse(false)
            .build();
    }
}
```

### 8.3 监控和日志

```java
@Service
public class MonitoredTransactionService {
    
    private static final Logger log = LoggerFactory.getLogger(MonitoredTransactionService.class);
    
    public List<Transaction> queryWithMonitoring(
            List<String> merchantIds,
            Map<String, Object> queryParams,
            int currentPage,
            int lastRecordPage,
            Long lastRecordTime) {
        
        long startTime = System.currentTimeMillis();
        
        try {
            log.info("开始查询交易数据，商户数量: {}, 当前页: {}, 上次页: {}", 
                merchantIds.size(), currentPage, lastRecordPage);
            
            MultiEntityDataHeap<Transaction, Map<String, Object>> dataHeap = 
                DataHeapBuilder.<Transaction, Map<String, Object>>create()
                    .entityIds(merchantIds)
                    .queryParams(queryParams)
                    .currentPage(currentPage)
                    .lastRecordPage(lastRecordPage)
                    .lastRecordTime(lastRecordTime)
                    .withLoader(this::queryTransactions)
                    .timeField("createTime")
                    .idField("transactionId")
                    .build();
            
            List<Transaction> result = dataHeap.getRecords();
            
            long endTime = System.currentTimeMillis();
            log.info("查询完成，返回记录数: {}, 耗时: {}ms", 
                result.size(), endTime - startTime);
            
            return result;
            
        } catch (Exception e) {
            long endTime = System.currentTimeMillis();
            log.error("查询失败，耗时: {}ms", endTime - startTime, e);
            throw e;
        }
    }
}
```

## 9. 注意事项

### 9.1 数据一致性

1. **时间字段精度**：确保时间字段有足够的精度（建议使用毫秒级时间戳）
2. **并发修改**：考虑数据在查询过程中被修改的情况
3. **边界处理**：正确处理分页边界，避免数据重复或遗漏

### 9.2 性能考虑

1. **ID数量控制**：单次查询的商户ID数量建议不超过1000个
2. **索引优化**：确保查询字段有合适的数据库索引
3. **内存使用**：大数据量场景下注意内存使用情况

### 9.3 错误处理

1. **异常捕获**：正确捕获和处理各种异常类型
2. **降级策略**：提供查询失败时的降级方案
3. **重试机制**：对于临时性错误可以考虑重试

## 10. 常见问题

### 10.1 Q: 如何处理大量商户ID的查询？

A: SDK会自动将大量ID分批处理，你可以通过调整`idDivideThreshold`参数来控制分批大小：

```java
DataHeapConfig config = DataHeapConfig.builder()
    .idDivideThreshold(500)  // 每批最多500个ID
    .build();
```

### 10.2 Q: 如何实现自定义排序？

A: 通过自定义`RecordComparator`实现：

```java
RecordComparator<Transaction> customComparator = (t1, t2) -> {
    // 先按金额排序，再按时间排序
    int amountCompare = Long.compare(t2.getAmount(), t1.getAmount());
    if (amountCompare != 0) {
        return amountCompare;
    }
    return Long.compare(t2.getCreateTime(), t1.getCreateTime());
};
```

### 10.3 Q: 如何处理分页边界数据重复？

A: SDK内置了边界记录处理机制，会自动处理分页边界的数据重复问题。如果仍有问题，可以启用边界折叠异常：

```java
DataHeapConfig config = DataHeapConfig.builder()
    .throwOnBoundaryCollapse(true)  // 边界折叠时抛异常
    .build();
```

### 10.4 Q: 如何优化查询性能？

A: 主要优化方向：
1. 确保数据库有合适的索引
2. 调整批次大小参数
3. 使用连续分页而非随机跳页
4. 考虑添加缓存层

## 11. 版本更新日志

### v1.2.1
- 初始版本发布
- 支持多实体数据聚合
- 支持智能分页处理
- 提供Builder模式简化接入